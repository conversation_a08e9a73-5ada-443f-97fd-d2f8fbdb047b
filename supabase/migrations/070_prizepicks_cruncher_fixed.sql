-- PrizePicks AI Cruncher Schema (Fixed - No Cron Dependencies)
-- Simple line tracking and analysis system

-- Core props tracking table
CREATE TABLE prizepicks_props (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  external_id text UNIQUE NOT NULL, -- PrizePicks prop ID
  sport text NOT NULL, -- 'NFL', 'NCAAF', 'Tennis', 'WNBA'
  player_name text NOT NULL,
  team text,
  opponent text,
  prop_type text NOT NULL, -- 'passing_yards', 'rushing_yards', 'receiving_yards', etc.
  line_value decimal NOT NULL,
  game_time timestamptz,
  is_active boolean DEFAULT true,
  scraped_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Line movement history
CREATE TABLE line_movements (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  prop_id uuid REFERENCES prizepicks_props(id) ON DELETE CASCADE,
  old_value decimal,
  new_value decimal NOT NULL,
  movement_size decimal GENERATED ALWAYS AS (new_value - old_value) STORED,
  detected_at timestamptz DEFAULT now()
);

-- AI analysis cache (short-term only)
CREATE TABLE ai_analyses (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  prop_id uuid REFERENCES prizepicks_props(id) ON DELETE CASCADE,
  user_id uuid, -- Optional: track who requested
  analysis_text text NOT NULL,
  confidence_rating integer CHECK (confidence_rating >= 1 AND confidence_rating <= 5),
  recommendation text CHECK (recommendation IN ('OVER', 'UNDER', 'AVOID')),
  reasoning_points jsonb, -- Key factors that influenced the decision
  created_at timestamptz DEFAULT now(),
  expires_at timestamptz DEFAULT (now() + interval '4 hours') -- Auto-expire analysis
);

-- Indexes for performance
CREATE INDEX idx_prizepicks_props_sport_active ON prizepicks_props(sport, is_active);
CREATE INDEX idx_prizepicks_props_game_time ON prizepicks_props(game_time) WHERE is_active = true;
CREATE INDEX idx_line_movements_prop_id ON line_movements(prop_id);
CREATE INDEX idx_line_movements_detected_at ON line_movements(detected_at);
CREATE INDEX idx_ai_analyses_expires_at ON ai_analyses(expires_at);

-- Auto-cleanup expired analyses
CREATE OR REPLACE FUNCTION cleanup_expired_analyses()
RETURNS void AS $$
BEGIN
  DELETE FROM ai_analyses WHERE expires_at < now();
END;
$$ LANGUAGE plpgsql;

-- Function to detect significant line movements
CREATE OR REPLACE FUNCTION detect_line_movement()
RETURNS trigger AS $$
BEGIN
  -- Only log if line value actually changed
  IF OLD.line_value IS DISTINCT FROM NEW.line_value THEN
    INSERT INTO line_movements (prop_id, old_value, new_value)
    VALUES (NEW.id, OLD.line_value, NEW.line_value);
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for automatic line movement detection
CREATE TRIGGER trigger_detect_line_movement
  AFTER UPDATE ON prizepicks_props
  FOR EACH ROW
  EXECUTE FUNCTION detect_line_movement();

-- Update timestamp trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS trigger AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_prizepicks_props_updated_at
  BEFORE UPDATE ON prizepicks_props
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- View for active props with recent movements
CREATE VIEW active_props_with_movements AS
SELECT
  p.*,
  COUNT(lm.id) as movement_count,
  MAX(lm.detected_at) as last_movement_at,
  MAX(lm.movement_size) as biggest_movement
FROM prizepicks_props p
LEFT JOIN line_movements lm ON p.id = lm.prop_id
WHERE p.is_active = true
  AND p.game_time > now()
GROUP BY p.id;

-- View for props needing fresh analysis
CREATE VIEW props_needing_analysis AS
SELECT p.*
FROM prizepicks_props p
LEFT JOIN ai_analyses a ON p.id = a.prop_id
  AND a.created_at > now() - interval '4 hours'
WHERE p.is_active = true
  AND p.game_time > now()
  AND a.id IS NULL -- No recent analysis
ORDER BY p.game_time ASC;

-- Function to get live props summary
CREATE OR REPLACE FUNCTION get_live_props_summary()
RETURNS TABLE(
  sport text,
  active_props bigint,
  props_with_analysis bigint,
  props_with_movements bigint,
  latest_scrape timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.sport,
    COUNT(*) as active_props,
    COUNT(a.id) as props_with_analysis,
    COUNT(DISTINCT lm.prop_id) as props_with_movements,
    MAX(p.scraped_at) as latest_scrape
  FROM prizepicks_props p
  LEFT JOIN ai_analyses a ON p.id = a.prop_id
    AND a.created_at > now() - interval '4 hours'
  LEFT JOIN line_movements lm ON p.id = lm.prop_id
    AND lm.detected_at > now() - interval '24 hours'
  WHERE p.is_active = true
    AND p.game_time > now()
  GROUP BY p.sport
  ORDER BY active_props DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to check significant movements
CREATE OR REPLACE FUNCTION check_significant_movements()
RETURNS TABLE(
  prop_id uuid,
  player_name text,
  prop_type text,
  old_value decimal,
  new_value decimal,
  movement_size decimal,
  movement_time timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    lm.prop_id,
    pp.player_name,
    pp.prop_type,
    lm.old_value,
    lm.new_value,
    lm.movement_size,
    lm.detected_at
  FROM line_movements lm
  JOIN prizepicks_props pp ON lm.prop_id = pp.id
  WHERE lm.detected_at > now() - interval '1 hour'
    AND ABS(lm.movement_size) >= 1.0 -- Significant movement threshold
    AND pp.is_active = true
  ORDER BY ABS(lm.movement_size) DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old data
CREATE OR REPLACE FUNCTION cleanup_old_prizepicks_data()
RETURNS void AS $$
BEGIN
  -- Remove props for games that ended more than 24 hours ago
  DELETE FROM prizepicks_props
  WHERE game_time < now() - interval '24 hours';

  -- Remove old line movements (keep 7 days)
  DELETE FROM line_movements
  WHERE detected_at < now() - interval '7 days';

  -- Clean expired analyses
  PERFORM cleanup_expired_analyses();

  RAISE NOTICE 'Cleaned up old PrizePicks data';
END;
$$ LANGUAGE plpgsql;

-- RLS Policies (if needed for user access)
ALTER TABLE prizepicks_props ENABLE ROW LEVEL SECURITY;
ALTER TABLE line_movements ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_analyses ENABLE ROW LEVEL SECURITY;

-- Allow read access for authenticated users
CREATE POLICY "Allow read access to props" ON prizepicks_props
  FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow read access to movements" ON line_movements
  FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow read access to analyses" ON ai_analyses
  FOR SELECT TO authenticated USING (true);

-- Allow insert for service role (scraping)
CREATE POLICY "Allow service insert props" ON prizepicks_props
  FOR INSERT TO service_role WITH CHECK (true);

CREATE POLICY "Allow service update props" ON prizepicks_props
  FOR UPDATE TO service_role USING (true);

-- Allow users to insert their own analyses requests
CREATE POLICY "Allow user analyses" ON ai_analyses
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id OR user_id IS NULL);