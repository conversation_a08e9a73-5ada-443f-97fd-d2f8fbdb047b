create table stripe_balance_transactions (
    id text primary key,
    type text,
    source text,
    amount numeric,
    fee numeric,
    destination_platform_fee numeric,
    destination_platform_fee_currency text,
    net numeric,
    currency text,
    created_utc timestamptz,
    available_on_utc timestamptz,
    description text,
    customer_facing_amount numeric,
    customer_facing_currency text,
    transfer text,
    transfer_date_utc timestamptz,
    transfer_group text,
    buyer_id_metadata text,
    author_id_metadata text,
    book_id_metadata text,
    type_metadata text,
    donation_message_metadata text,
    platform_fee_metadata text,
    writer_id_metadata text,
    writer_amount_metadata text,
    entry_id_metadata text,
    is_story_venture_metadata text,
    payment_type_metadata text,
    payer_id_metadata text,
    product_type_metadata text
);

alter table stripe_balance_transactions enable row level security;
