-- Create OnlyPages waitlist table
CREATE TABLE IF NOT EXISTS public.onlypages_waitlist (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  page_id uuid NOT NULL REFERENCES public.onlypages(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  joined_at timestamptz NOT NULL DEFAULT now(),
  UNIQUE(page_id, user_id)
);

-- Enable RLS
ALTER TABLE public.onlypages_waitlist ENABLE ROW LEVEL SECURITY;

-- Policy for users to read their own waitlist entries
DROP POLICY IF EXISTS "waitlist_read_own" ON public.onlypages_waitlist;
CREATE POLICY "waitlist_read_own" ON public.onlypages_waitlist
  FOR SELECT USING (user_id = auth.uid());

-- Policy for users to insert their own waitlist entries
DROP POLICY IF EXISTS "waitlist_insert_own" ON public.onlypages_waitlist;
CREATE POLICY "waitlist_insert_own" ON public.onlypages_waitlist
  FOR INSERT WITH CHECK (user_id = auth.uid());

-- Policy for page owners to read all waitlist entries for their pages
DROP POLICY IF EXISTS "waitlist_read_page_owner" ON public.onlypages_waitlist;
CREATE POLICY "waitlist_read_page_owner" ON public.onlypages_waitlist
  FOR SELECT USING (
    EXISTS (SELECT 1 FROM public.onlypages p WHERE p.id = onlypages_waitlist.page_id AND p.user_id = auth.uid())
  );