-- Search, Analytics, and Category system for OnlyPages

-- Add creator_name and categories to onlypages
ALTER TABLE public.onlypages
ADD COLUMN IF NOT EXISTS creator_name text,
ADD COLUMN IF NOT EXISTS categories text[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS auto_tags text[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS total_views integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS weekly_views integer DEFAULT 0;

-- Create search index for full-text search
-- Note: We'll create this index after adding the search function
-- CREATE INDEX IF NOT EXISTS onlypages_search_idx ON public.onlypages
-- USING gin(to_tsvector('english',
--   coalesce(article->>'title', '') || ' ' ||
--   coalesce(article->>'dek', '') || ' ' ||
--   coalesce(creator_name, '') || ' ' ||
--   coalesce(array_to_string(categories, ' '), '') || ' ' ||
--   coalesce(array_to_string(auto_tags, ' '), '')
-- ));

-- Categories tracking table
CREATE TABLE IF NOT EXISTS public.onlypages_categories (
  category text PRIMARY KEY,
  page_count integer DEFAULT 0,
  total_views integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Page views tracking (real-time)
CREATE TABLE IF NOT EXISTS public.onlypages_views (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  page_id uuid NOT NULL REFERENCES public.onlypages(id) ON DELETE CASCADE,
  visitor_id text, -- anonymous session ID
  user_id uuid REFERENCES public.users(id) ON DELETE SET NULL,
  country text,
  referrer text,
  user_agent text,
  created_at timestamptz DEFAULT now()
);

-- Rankings table for top pages
CREATE TABLE IF NOT EXISTS public.onlypages_rankings (
  rank_type text NOT NULL, -- 'overall', 'weekly', 'category'
  rank_key text NOT NULL, -- 'all' for overall, category name for category rankings
  page_id uuid NOT NULL REFERENCES public.onlypages(id) ON DELETE CASCADE,
  rank_position integer NOT NULL,
  score integer NOT NULL, -- views or other ranking metric
  period_start date,
  period_end date,
  created_at timestamptz DEFAULT now(),
  PRIMARY KEY (rank_type, rank_key, page_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS onlypages_views_page_id_idx ON public.onlypages_views(page_id);
CREATE INDEX IF NOT EXISTS onlypages_views_created_at_idx ON public.onlypages_views(created_at);
CREATE INDEX IF NOT EXISTS onlypages_rankings_type_key_idx ON public.onlypages_rankings(rank_type, rank_key);
CREATE INDEX IF NOT EXISTS onlypages_categories_page_count_idx ON public.onlypages_categories(page_count DESC);

-- Function to update view counts
CREATE OR REPLACE FUNCTION increment_page_views(page_uuid uuid, visitor_session text DEFAULT NULL, referrer_url text DEFAULT NULL)
RETURNS void AS $$
BEGIN
  -- Insert view record
  INSERT INTO public.onlypages_views (page_id, visitor_id, referrer, created_at)
  VALUES (page_uuid, visitor_session, referrer_url, now());

  -- Update total views on page
  UPDATE public.onlypages
  SET total_views = total_views + 1,
      weekly_views = (
        SELECT COUNT(*)
        FROM public.onlypages_views
        WHERE page_id = page_uuid
        AND created_at >= now() - interval '7 days'
      )
  WHERE id = page_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to auto-categorize pages based on content
CREATE OR REPLACE FUNCTION auto_categorize_page(page_uuid uuid)
RETURNS text[] AS $$
DECLARE
  page_record record;
  detected_categories text[] := '{}';
  content_text text;
BEGIN
  SELECT article, type INTO page_record FROM public.onlypages WHERE id = page_uuid;

  IF page_record IS NULL THEN
    RETURN detected_categories;
  END IF;

  -- Extract text content for analysis
  content_text := lower(
    coalesce(page_record.article->>'title', '') || ' ' ||
    coalesce(page_record.article->>'dek', '') || ' ' ||
    coalesce(page_record.article->>'body_html', '')
  );

  -- Technology & Startups
  IF content_text ~* '\y(startup|tech|ai|software|app|platform|saas|api|code|developer|programming|innovation|digital|blockchain|crypto|venture|funding|vc)\y' THEN
    detected_categories := array_append(detected_categories, 'Technology');
  END IF;

  -- Business & Entrepreneurship
  IF content_text ~* '\y(business|entrepreneur|company|marketing|sales|revenue|profit|strategy|leadership|management|growth|brand|customer)\y' THEN
    detected_categories := array_append(detected_categories, 'Business');
  END IF;

  -- Creative Arts
  IF content_text ~* '\y(art|music|film|movie|book|writing|design|creative|artist|author|director|musician|photography|gallery|exhibition)\y' THEN
    detected_categories := array_append(detected_categories, 'Arts');
  END IF;

  -- Health & Wellness
  IF content_text ~* '\y(health|fitness|wellness|medical|doctor|therapy|mental|nutrition|diet|exercise|meditation|healing)\y' THEN
    detected_categories := array_append(detected_categories, 'Health');
  END IF;

  -- Science & Research
  IF content_text ~* '\y(science|research|study|experiment|discovery|academic|university|professor|phd|laboratory|data|analysis)\y' THEN
    detected_categories := array_append(detected_categories, 'Science');
  END IF;

  -- Entertainment & Media
  IF content_text ~* '\y(entertainment|media|celebrity|show|series|podcast|youtube|influencer|viral|trending|social)\y' THEN
    detected_categories := array_append(detected_categories, 'Entertainment');
  END IF;

  -- Finance & Investment
  IF content_text ~* '\y(finance|investment|money|financial|bank|stock|trading|investor|fund|portfolio|market|economy)\y' THEN
    detected_categories := array_append(detected_categories, 'Finance');
  END IF;

  -- Education & Learning
  IF content_text ~* '\y(education|learning|school|student|teacher|course|lesson|tutorial|knowledge|skill|training)\y' THEN
    detected_categories := array_append(detected_categories, 'Education');
  END IF;

  -- Sports & Fitness
  IF content_text ~* '\y(sport|athlete|fitness|training|competition|team|game|championship|olympic|workout|gym)\y' THEN
    detected_categories := array_append(detected_categories, 'Sports');
  END IF;

  -- Travel & Lifestyle
  IF content_text ~* '\y(travel|lifestyle|culture|adventure|journey|destination|vacation|experience|life|personal)\y' THEN
    detected_categories := array_append(detected_categories, 'Lifestyle');
  END IF;

  -- Fallback based on page type
  IF array_length(detected_categories, 1) IS NULL THEN
    detected_categories := CASE page_record.type
      WHEN 'book' THEN ARRAY['Literature']
      WHEN 'movie' THEN ARRAY['Entertainment']
      WHEN 'album' THEN ARRAY['Music']
      WHEN 'press' THEN ARRAY['News']
      WHEN 'site' THEN ARRAY['Technology']
      ELSE ARRAY['General']
    END;
  END IF;

  -- Update the page with detected categories
  UPDATE public.onlypages
  SET categories = detected_categories
  WHERE id = page_uuid;

  RETURN detected_categories;
END;
$$ LANGUAGE plpgsql;

-- Function to update category stats
CREATE OR REPLACE FUNCTION update_category_stats()
RETURNS void AS $$
DECLARE
  cat_record record;
BEGIN
  -- Clear existing stats
  DELETE FROM public.onlypages_categories;

  -- Rebuild category stats
  FOR cat_record IN
    SELECT unnest(categories) as category
    FROM public.onlypages
    WHERE status IN ('published', 'unlisted')
    AND categories IS NOT NULL
  LOOP
    INSERT INTO public.onlypages_categories (category, page_count, total_views)
    VALUES (cat_record.category, 1, 0)
    ON CONFLICT (category)
    DO UPDATE SET
      page_count = onlypages_categories.page_count + 1;
  END LOOP;

  -- Update view counts for categories
  UPDATE public.onlypages_categories
  SET total_views = (
    SELECT COALESCE(SUM(p.total_views), 0)
    FROM public.onlypages p
    WHERE p.categories @> ARRAY[onlypages_categories.category]
    AND p.status IN ('published', 'unlisted')
  );
END;
$$ LANGUAGE plpgsql;

-- Function to update rankings
CREATE OR REPLACE FUNCTION update_rankings()
RETURNS void AS $$
BEGIN
  -- Clear existing rankings
  DELETE FROM public.onlypages_rankings;

  -- Overall top 100
  INSERT INTO public.onlypages_rankings (rank_type, rank_key, page_id, rank_position, score)
  SELECT 'overall', 'all', id, row_number() OVER (ORDER BY total_views DESC), total_views
  FROM public.onlypages
  WHERE status IN ('published', 'unlisted')
  ORDER BY total_views DESC
  LIMIT 100;

  -- Weekly top 100
  INSERT INTO public.onlypages_rankings (rank_type, rank_key, page_id, rank_position, score)
  SELECT 'weekly', 'all', id, row_number() OVER (ORDER BY weekly_views DESC), weekly_views
  FROM public.onlypages
  WHERE status IN ('published', 'unlisted')
  ORDER BY weekly_views DESC
  LIMIT 100;

  -- Category rankings (top 20 per category)
  INSERT INTO public.onlypages_rankings (rank_type, rank_key, page_id, rank_position, score)
  SELECT 'category', category, page_id, rank_position, total_views
  FROM (
    SELECT
      unnest(p.categories) as category,
      p.id as page_id,
      p.total_views,
      row_number() OVER (PARTITION BY unnest(p.categories) ORDER BY p.total_views DESC) as rank_position
    FROM public.onlypages p
    WHERE p.status IN ('published', 'unlisted')
    AND p.categories IS NOT NULL
  ) ranked
  WHERE rank_position <= 20;
END;
$$ LANGUAGE plpgsql;

-- RLS policies for new tables
ALTER TABLE public.onlypages_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.onlypages_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.onlypages_rankings ENABLE ROW LEVEL SECURITY;

-- Public read access for categories and rankings
CREATE POLICY "categories_public_read" ON public.onlypages_categories FOR SELECT USING (true);
CREATE POLICY "rankings_public_read" ON public.onlypages_rankings FOR SELECT USING (true);

-- Views tracking policy
CREATE POLICY "views_insert_public" ON public.onlypages_views
  FOR INSERT WITH CHECK (true);
CREATE POLICY "views_read_page_owner" ON public.onlypages_views
  FOR SELECT USING (
    EXISTS (SELECT 1 FROM public.onlypages p WHERE p.id = page_id AND p.user_id = auth.uid())
  );

-- Trigger to auto-categorize pages when articles are updated
CREATE OR REPLACE FUNCTION trigger_auto_categorize()
RETURNS trigger AS $$
BEGIN
  IF NEW.article IS NOT NULL AND (OLD.article IS NULL OR NEW.article != OLD.article) THEN
    PERFORM auto_categorize_page(NEW.id);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'trg_auto_categorize_onlypages'
  ) THEN
    CREATE TRIGGER trg_auto_categorize_onlypages
    AFTER INSERT OR UPDATE OF article ON public.onlypages
    FOR EACH ROW EXECUTE FUNCTION trigger_auto_categorize();
  END IF;
END $$;