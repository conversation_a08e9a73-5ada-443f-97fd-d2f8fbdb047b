-- Add subscription tracking fields to genyus_user_words table
-- Date: 2025-01-17

-- Add subscription fields if they don't exist
DO $$
BEGIN
  -- Add stripe_subscription_id column
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'genyus_user_words'
    AND column_name = 'stripe_subscription_id'
  ) THEN
    ALTER TABLE genyus_user_words
    ADD COLUMN stripe_subscription_id TEXT;
  END IF;

  -- Add subscription_status column
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'genyus_user_words'
    AND column_name = 'subscription_status'
  ) THEN
    ALTER TABLE genyus_user_words
    ADD COLUMN subscription_status TEXT CHECK (
      subscription_status IN ('active', 'canceled', 'unpaid', 'past_due', 'trialing', 'incomplete')
    );
  END IF;
END $$;

-- Create index on stripe_subscription_id for faster webhook lookups
CREATE INDEX IF NOT EXISTS idx_genyus_user_words_stripe_subscription_id
ON genyus_user_words(stripe_subscription_id);

-- Create index on subscription_status for filtering
CREATE INDEX IF NOT EXISTS idx_genyus_user_words_subscription_status
ON genyus_user_words(subscription_status);

-- Update existing unlimited users to have proper tier
UPDATE genyus_user_words
SET tier = 'unlimited'
WHERE words_remaining = -1 AND tier != 'unlimited';

COMMENT ON COLUMN genyus_user_words.stripe_subscription_id IS 'Stripe subscription ID for unlimited plan users';
COMMENT ON COLUMN genyus_user_words.subscription_status IS 'Current status of the Stripe subscription';