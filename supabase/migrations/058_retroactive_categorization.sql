-- Retroactively categorize all existing OnlyPages

-- First, auto-categorize all existing published pages
DO $$
DECLARE
  page_record record;
BEGIN
  FOR page_record IN
    SELECT id FROM public.onlypages
    WHERE status IN ('published', 'unlisted', 'draft')
    AND article IS NOT NULL
  LOOP
    PERFORM auto_categorize_page(page_record.id);
  END LOOP;
END $$;

-- Update category statistics
SELECT update_category_stats();

-- Update rankings
SELECT update_rankings();

-- Add a function to manually recategorize all pages (for future use)
CREATE OR REPLACE FUNCTION recategorize_all_pages()
RETURNS integer AS $$
DECLARE
  processed_count integer := 0;
  page_record record;
BEGIN
  FOR page_record IN
    SELECT id FROM public.onlypages
    WHERE status IN ('published', 'unlisted', 'draft')
    AND article IS NOT NULL
  LOOP
    PERFORM auto_categorize_page(page_record.id);
    processed_count := processed_count + 1;
  END LOOP;

  -- Update statistics
  PERFORM update_category_stats();
  PERFORM update_rankings();

  RETURN processed_count;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job function for regular updates (to be called by cron or manually)
CREATE OR REPLACE FUNCTION update_onlypages_analytics()
RETURNS void AS $$
BEGIN
  -- Update weekly view counts for all pages
  UPDATE public.onlypages
  SET weekly_views = (
    SELECT COUNT(*)
    FROM public.onlypages_views v
    WHERE v.page_id = onlypages.id
    AND v.created_at >= now() - interval '7 days'
  );

  -- Update category stats
  PERFORM update_category_stats();

  -- Update rankings
  PERFORM update_rankings();

  -- Log the update
  RAISE NOTICE 'OnlyPages analytics updated at %', now();
END;
$$ LANGUAGE plpgsql;