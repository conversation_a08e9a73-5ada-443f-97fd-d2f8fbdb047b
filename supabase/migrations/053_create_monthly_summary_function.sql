create or replace function get_monthly_stripe_summary()
returns table (
    month text,
    currency text,
    sales numeric,
    refunds numeric,
    adjustments numeric,
    other numeric,
    gross_transactions numeric,
    net_transactions numeric,
    payout_fees numeric,
    gross_payouts numeric,
    monthly_net_activity numeric,
    month_end_balance numeric,
    sales_count bigint,
    payouts_count bigint,
    adjustments_count bigint
)
language sql
stable
as $$
  with monthly_balance_transactions as (
    select
      date_trunc('month', case when type = 'payout' then available_on_utc else created_utc end) as month,
      currency,
      sum(case when type in ('charge', 'payment') then amount else 0 end) as sales,
      sum(case when type in ('payment_refund', 'refund') then amount else 0 end) as refunds,
      sum(case when type = 'adjustment' then amount else 0 end) as adjustments,
      sum(case when type not in ('charge', 'payment', 'payment_refund', 'refund', 'adjustment', 'payout') and type not like '%transfer%' then amount else 0 end) as other,
      sum(case when type <> 'payout' and type not like '%transfer%' then amount else 0 end) as gross_transactions,
      sum(case when type <> 'payout' and type not like '%transfer%' then net else 0 end) as net_transactions,
      sum(case when type = 'payout' or type like '%transfer%' then fee * -1 else 0 end) as payout_fees,
      sum(case when type = 'payout' or type like '%transfer%' then amount else 0 end) as gross_payouts,
      sum(case when type = 'payout' or type like '%transfer%' then fee * -1 else net end) as monthly_net_activity,
      count(*) filter (where type in ('payment', 'charge')) as sales_count,
      count(*) filter (where type = 'payout') as payouts_count,
      count(distinct case when type = 'adjustment' then source end) as adjustments_count
    from public.stripe_balance_transactions
    group by 1, 2
  )
  select
    to_char(month, 'YYYY-MM') as month,
    currency,
    sales,
    refunds,
    adjustments,
    other,
    gross_transactions,
    net_transactions,
    payout_fees,
    gross_payouts,
    monthly_net_activity,
    sum(monthly_net_activity + gross_payouts) over(partition by currency order by month) as month_end_balance,
    sales_count,
    payouts_count,
    adjustments_count
  from monthly_balance_transactions
  where month < date_trunc('month', current_date)
  order by 1 desc, 2;
$$;
