-- PrizePicks Cleanup & Archiving for started games
-- Purpose: immediately deactivate and archive props once games start; keep analysis caches short-lived

-- Archive tables (lightweight history). Keep minimal retention via purge step.
CREATE TABLE IF NOT EXISTS prizepicks_props_archive (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  prop_id uuid NOT NULL,
  external_id text,
  sport text,
  player_name text,
  team text,
  opponent text,
  prop_type text,
  line_value decimal,
  game_time timestamptz,
  is_active boolean,
  scraped_at timestamptz,
  created_at timestamptz,
  updated_at timestamptz,
  archived_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS ai_analyses_archive (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  analysis_id uuid,
  prop_id uuid,
  user_id uuid,
  analysis_text text,
  confidence_rating integer,
  recommendation text,
  reasoning_points jsonb,
  created_at timestamptz,
  expires_at timestamptz,
  -- Extra optional columns if present in live table (tolerate NULLs when absent)
  sharp_line_comparison jsonb,
  edge_summary text,
  injury_concerns jsonb,
  game_context jsonb,
  correlations jsonb,
  archived_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS line_movements_archive (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  movement_id uuid,
  prop_id uuid,
  old_value decimal,
  new_value decimal,
  movement_size decimal,
  detected_at timestamptz,
  archived_at timestamptz DEFAULT now()
);

-- Ensure optional extended columns exist on ai_analyses to match application inserts
ALTER TABLE ai_analyses ADD COLUMN IF NOT EXISTS sharp_line_comparison jsonb;
ALTER TABLE ai_analyses ADD COLUMN IF NOT EXISTS edge_summary text;
ALTER TABLE ai_analyses ADD COLUMN IF NOT EXISTS injury_concerns jsonb;
ALTER TABLE ai_analyses ADD COLUMN IF NOT EXISTS game_context jsonb;
ALTER TABLE ai_analyses ADD COLUMN IF NOT EXISTS correlations jsonb;


-- Deactivate props whose games have started (idempotent)
CREATE OR REPLACE FUNCTION deactivate_started_prizepicks_props()
RETURNS void AS $$
BEGIN
  UPDATE prizepicks_props
  SET is_active = false
  WHERE is_active = true
    AND game_time IS NOT NULL
    AND game_time <= now();
END;
$$ LANGUAGE plpgsql;

-- Archive started props and their dependent rows, then delete from live tables
CREATE OR REPLACE FUNCTION archive_started_prizepicks_data()
RETURNS void AS $$
DECLARE
BEGIN
  WITH started AS (
    SELECT id FROM prizepicks_props
    WHERE game_time IS NOT NULL AND game_time <= now()
  )
  -- Archive analyses first
  , ins_analyses AS (
    INSERT INTO ai_analyses_archive (
      analysis_id, prop_id, user_id, analysis_text, confidence_rating, recommendation,
      reasoning_points, created_at, expires_at, sharp_line_comparison, edge_summary,
      injury_concerns, game_context, correlations
    )
    SELECT a.id, a.prop_id, a.user_id, a.analysis_text, a.confidence_rating, a.recommendation,
           a.reasoning_points, a.created_at, a.expires_at,
           -- Optional columns may not exist; use COALESCE against NULL if absent via casts
           a.sharp_line_comparison, a.edge_summary, a.injury_concerns, a.game_context, a.correlations
    FROM ai_analyses a
    JOIN started s ON a.prop_id = s.id
    ON CONFLICT DO NOTHING
    RETURNING 1
  )
  -- Archive line movements
  , ins_movements AS (
    INSERT INTO line_movements_archive (movement_id, prop_id, old_value, new_value, movement_size, detected_at)
    SELECT lm.id, lm.prop_id, lm.old_value, lm.new_value, lm.movement_size, lm.detected_at
    FROM line_movements lm
    JOIN started s ON lm.prop_id = s.id
    ON CONFLICT DO NOTHING
    RETURNING 1
  )
  -- Archive props
  INSERT INTO prizepicks_props_archive (
    prop_id, external_id, sport, player_name, team, opponent, prop_type, line_value,
    game_time, is_active, scraped_at, created_at, updated_at
  )
  SELECT p.id, p.external_id, p.sport, p.player_name, p.team, p.opponent, p.prop_type,
         p.line_value, p.game_time, p.is_active, p.scraped_at, p.created_at, p.updated_at
  FROM prizepicks_props p
  JOIN started s ON p.id = s.id
  ON CONFLICT DO NOTHING;

  -- Delete children then parents (live tables)
  DELETE FROM ai_analyses a USING started s WHERE a.prop_id = s.id;
  DELETE FROM line_movements lm USING started s WHERE lm.prop_id = s.id;
  DELETE FROM prizepicks_props p USING started s WHERE p.id = s.id;
END;
$$ LANGUAGE plpgsql;

-- One-shot orchestrator: deactivate, archive, then housekeeping
CREATE OR REPLACE FUNCTION run_prizepicks_cleanup()
RETURNS void AS $$
BEGIN
  PERFORM deactivate_started_prizepicks_props();
  PERFORM archive_started_prizepicks_data();

  -- Existing housekeeping
  PERFORM cleanup_expired_analyses();
  -- Keep live movements for 7 days
  DELETE FROM line_movements WHERE detected_at < now() - interval '7 days';
  -- Optional: purge archived data older than 30 days
  DELETE FROM ai_analyses_archive WHERE archived_at < now() - interval '30 days';
  DELETE FROM line_movements_archive WHERE archived_at < now() - interval '30 days';
  DELETE FROM prizepicks_props_archive WHERE archived_at < now() - interval '90 days';
END;
$$ LANGUAGE plpgsql;

-- Schedule periodic cleanup (every 15 minutes)
-- Requires pg_cron extension (already used in prior migrations)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_namespace WHERE nspname = 'cron') THEN
    PERFORM cron.schedule(
      'prizepicks-started-archive',
      '*/15 * * * *',
      'SELECT run_prizepicks_cleanup();'
    );
  END IF;
END $$;

-- Allow invocation by service role; (cron runs as superuser)
GRANT EXECUTE ON FUNCTION run_prizepicks_cleanup() TO service_role;
