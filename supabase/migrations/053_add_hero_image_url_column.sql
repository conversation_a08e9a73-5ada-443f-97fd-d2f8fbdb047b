-- Add hero_image_url column to onlypages table if it doesn't exist
-- This ensures the column is present for image uploads

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'onlypages' 
        AND column_name = 'hero_image_url'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.onlypages 
        ADD COLUMN hero_image_url text;
        
        RAISE NOTICE 'Added hero_image_url column to onlypages table';
    ELSE
        RAISE NOTICE 'hero_image_url column already exists in onlypages table';
    END IF;
END $$;
