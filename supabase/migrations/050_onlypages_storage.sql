-- Storage setup for OnlyPages assets
-- Creates a private bucket for OnlyPages uploads (e.g., hero images). Reads occur via signed URLs.

-- Create storage bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'onlypages-assets',
    'onlypages-assets',
    false,
    52428800, -- 50MB limit
    ARRAY[
        'image/jpeg',
        'image/png',
        'image/webp',
        'image/avif',
        'image/gif'
    ]
)
ON CONFLICT (id) DO NOTHING;

-- RLS policies for OnlyPages assets
-- Allow authenticated users to upload into a namespaced folder u/{auth.uid}/onlypages/{page_id}/...
DROP POLICY IF EXISTS "OnlyPages insert" ON storage.objects;
CREATE POLICY "OnlyPages insert" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'onlypages-assets' AND
        auth.role() = 'authenticated' AND
        -- Enforce user folder prefix in object name
        (storage.foldername(name))[1] = 'u' AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        (storage.foldername(name))[3] = 'onlypages'
    );

DROP POLICY IF EXISTS "OnlyPages select own" ON storage.objects;
CREATE POLICY "OnlyPages select own" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'onlypages-assets' AND
        (storage.foldername(name))[1] = 'u' AND
        (storage.foldername(name))[2] = auth.uid()::text
    );

DROP POLICY IF EXISTS "OnlyPages delete own" ON storage.objects;
CREATE POLICY "OnlyPages delete own" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'onlypages-assets' AND
        (storage.foldername(name))[1] = 'u' AND
        (storage.foldername(name))[2] = auth.uid()::text
    );

-- Note: Public read is disabled. Use signed URLs for rendering when needed.

