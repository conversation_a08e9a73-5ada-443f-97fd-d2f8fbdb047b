-- Core tables for OnlyPages MVP

-- onlypages: central record for a generated/published page
CREATE TABLE IF NOT EXISTS public.onlypages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  type text NOT NULL, -- e.g., 'press' | 'news' | 'book' | 'movie' | 'album' | 'site' | 'life' | 'custom'
  slug text UNIQUE,
  slug_hint text,
  status text NOT NULL DEFAULT 'draft', -- 'draft'|'published'|'unlisted'|'review'
  article jsonb, -- stores article JSON per schema
  theme_tokens jsonb, -- stores theme tokens JSON
  hero_image_url text, -- stores uploaded hero image URL
  conversation_words integer NOT NULL DEFAULT 0,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- Interview QA table
CREATE TABLE IF NOT EXISTS public.onlypages_interview (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  page_id uuid NOT NULL REFERENCES public.onlypages(id) ON DELETE CASCADE,
  question_id text NOT NULL,
  question_text text NOT NULL,
  answer_text text,
  is_optional boolean NOT NULL DEFAULT false,
  order_index integer NOT NULL,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- Comments table
CREATE TABLE IF NOT EXISTS public.onlypages_comments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  page_id uuid NOT NULL REFERENCES public.onlypages(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  body text NOT NULL,
  status text NOT NULL DEFAULT 'published', -- 'pending'|'published'|'removed'
  word_count integer NOT NULL DEFAULT 0,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- Metrics daily table
CREATE TABLE IF NOT EXISTS public.onlypages_metrics_daily (
  day date NOT NULL,
  page_id uuid NOT NULL REFERENCES public.onlypages(id) ON DELETE CASCADE,
  views integer NOT NULL DEFAULT 0,
  shares integer NOT NULL DEFAULT 0,
  cta_clicks integer NOT NULL DEFAULT 0,
  PRIMARY KEY (day, page_id)
);

-- Triggers to maintain updated_at
CREATE OR REPLACE FUNCTION public.set_updated_at()
RETURNS trigger AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'trg_onlypages_updated_at'
  ) THEN
    CREATE TRIGGER trg_onlypages_updated_at
    BEFORE UPDATE ON public.onlypages
    FOR EACH ROW EXECUTE FUNCTION public.set_updated_at();
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'trg_onlypages_interview_updated_at'
  ) THEN
    CREATE TRIGGER trg_onlypages_interview_updated_at
    BEFORE UPDATE ON public.onlypages_interview
    FOR EACH ROW EXECUTE FUNCTION public.set_updated_at();
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'trg_onlypages_comments_updated_at'
  ) THEN
    CREATE TRIGGER trg_onlypages_comments_updated_at
    BEFORE UPDATE ON public.onlypages_comments
    FOR EACH ROW EXECUTE FUNCTION public.set_updated_at();
  END IF;
END $$;

-- Basic RLS policies
ALTER TABLE public.onlypages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.onlypages_interview ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.onlypages_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.onlypages_metrics_daily ENABLE ROW LEVEL SECURITY;

-- onlypages policies
DROP POLICY IF EXISTS "onlypages_owner_rw" ON public.onlypages;
CREATE POLICY "onlypages_owner_rw" ON public.onlypages
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

DROP POLICY IF EXISTS "onlypages_published_read" ON public.onlypages;
CREATE POLICY "onlypages_published_read" ON public.onlypages
  FOR SELECT USING (status IN ('published','unlisted') OR user_id = auth.uid());

-- interview policies
DROP POLICY IF EXISTS "interview_rw_owner" ON public.onlypages_interview;
CREATE POLICY "interview_rw_owner" ON public.onlypages_interview
  USING (EXISTS (SELECT 1 FROM public.onlypages p WHERE p.id = onlypages_interview.page_id AND p.user_id = auth.uid()))
  WITH CHECK (EXISTS (SELECT 1 FROM public.onlypages p WHERE p.id = onlypages_interview.page_id AND p.user_id = auth.uid()));

-- comments policies
DROP POLICY IF EXISTS "comments_select_published" ON public.onlypages_comments;
CREATE POLICY "comments_select_published" ON public.onlypages_comments
  FOR SELECT USING (
    status = 'published' OR EXISTS (SELECT 1 FROM public.onlypages p WHERE p.id = onlypages_comments.page_id AND p.user_id = auth.uid())
  );

DROP POLICY IF EXISTS "comments_insert_authenticated" ON public.onlypages_comments;
CREATE POLICY "comments_insert_authenticated" ON public.onlypages_comments
  FOR INSERT WITH CHECK (
    auth.role() = 'authenticated' AND user_id = auth.uid()
  );

DROP POLICY IF EXISTS "comments_update_owner_or_page_owner" ON public.onlypages_comments;
CREATE POLICY "comments_update_owner_or_page_owner" ON public.onlypages_comments
  FOR UPDATE USING (
    user_id = auth.uid() OR EXISTS (SELECT 1 FROM public.onlypages p WHERE p.id = onlypages_comments.page_id AND p.user_id = auth.uid())
  );

DROP POLICY IF EXISTS "comments_delete_owner_or_page_owner" ON public.onlypages_comments;
CREATE POLICY "comments_delete_owner_or_page_owner" ON public.onlypages_comments
  FOR DELETE USING (
    user_id = auth.uid() OR EXISTS (SELECT 1 FROM public.onlypages p WHERE p.id = onlypages_comments.page_id AND p.user_id = auth.uid())
  );

-- metrics policies (read public aggregated by day, writes by auth)
DROP POLICY IF EXISTS "metrics_select_public" ON public.onlypages_metrics_daily;
CREATE POLICY "metrics_select_public" ON public.onlypages_metrics_daily
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "metrics_insert_auth" ON public.onlypages_metrics_daily;
CREATE POLICY "metrics_insert_auth" ON public.onlypages_metrics_daily
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "metrics_update_auth" ON public.onlypages_metrics_daily;
CREATE POLICY "metrics_update_auth" ON public.onlypages_metrics_daily
  FOR UPDATE USING (auth.role() = 'authenticated');
