-- PrizePicks AI Cruncher Schema
-- Simple line tracking and analysis system

-- Core props tracking table
CREATE TABLE prizepicks_props (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  external_id text UNIQUE NOT NULL, -- PrizePicks prop ID
  sport text NOT NULL, -- 'NFL', 'NCAAF', 'Tennis', 'WNBA'
  player_name text NOT NULL,
  team text,
  opponent text,
  prop_type text NOT NULL, -- 'passing_yards', 'rushing_yards', 'receiving_yards', etc.
  line_value decimal NOT NULL,
  game_time timestamptz,
  is_active boolean DEFAULT true,
  scraped_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Line movement history
CREATE TABLE line_movements (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  prop_id uuid REFERENCES prizepicks_props(id) ON DELETE CASCADE,
  old_value decimal,
  new_value decimal NOT NULL,
  movement_size decimal GENERATED ALWAYS AS (new_value - old_value) STORED,
  detected_at timestamptz DEFAULT now()
);

-- AI analysis cache (short-term only)
CREATE TABLE ai_analyses (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  prop_id uuid REFERENCES prizepicks_props(id) ON DELETE CASCADE,
  user_id uuid, -- Optional: track who requested
  analysis_text text NOT NULL,
  confidence_rating integer CHECK (confidence_rating >= 1 AND confidence_rating <= 5),
  recommendation text CHECK (recommendation IN ('OVER', 'UNDER', 'AVOID')),
  reasoning_points jsonb, -- Key factors that influenced the decision
  created_at timestamptz DEFAULT now(),
  expires_at timestamptz DEFAULT (now() + interval '4 hours') -- Auto-expire analysis
);

-- Indexes for performance
CREATE INDEX idx_prizepicks_props_sport_active ON prizepicks_props(sport, is_active);
CREATE INDEX idx_prizepicks_props_game_time ON prizepicks_props(game_time) WHERE is_active = true;
CREATE INDEX idx_line_movements_prop_id ON line_movements(prop_id);
CREATE INDEX idx_line_movements_detected_at ON line_movements(detected_at);
CREATE INDEX idx_ai_analyses_expires_at ON ai_analyses(expires_at);

-- Auto-cleanup expired analyses
CREATE OR REPLACE FUNCTION cleanup_expired_analyses()
RETURNS void AS $$
BEGIN
  DELETE FROM ai_analyses WHERE expires_at < now();
END;
$$ LANGUAGE plpgsql;

-- Schedule cleanup (run every hour)
SELECT cron.schedule(
  'cleanup-expired-analyses',
  '0 * * * *', -- Every hour
  'SELECT cleanup_expired_analyses();'
);

-- Function to detect significant line movements
CREATE OR REPLACE FUNCTION detect_line_movement()
RETURNS trigger AS $$
BEGIN
  -- Only log if line value actually changed
  IF OLD.line_value IS DISTINCT FROM NEW.line_value THEN
    INSERT INTO line_movements (prop_id, old_value, new_value)
    VALUES (NEW.id, OLD.line_value, NEW.line_value);
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for automatic line movement detection
CREATE TRIGGER trigger_detect_line_movement
  AFTER UPDATE ON prizepicks_props
  FOR EACH ROW
  EXECUTE FUNCTION detect_line_movement();

-- Update timestamp trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS trigger AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_prizepicks_props_updated_at
  BEFORE UPDATE ON prizepicks_props
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- View for active props with recent movements
CREATE VIEW active_props_with_movements AS
SELECT
  p.*,
  COUNT(lm.id) as movement_count,
  MAX(lm.detected_at) as last_movement_at,
  MAX(lm.movement_size) as biggest_movement
FROM prizepicks_props p
LEFT JOIN line_movements lm ON p.id = lm.prop_id
WHERE p.is_active = true
  AND p.game_time > now()
GROUP BY p.id;

-- RLS Policies (if needed for user access)
ALTER TABLE prizepicks_props ENABLE ROW LEVEL SECURITY;
ALTER TABLE line_movements ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_analyses ENABLE ROW LEVEL SECURITY;

-- Allow read access for authenticated users
CREATE POLICY "Allow read access to props" ON prizepicks_props
  FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow read access to movements" ON line_movements
  FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow read access to analyses" ON ai_analyses
  FOR SELECT TO authenticated USING (true);

-- Allow insert for service role (scraping)
CREATE POLICY "Allow service insert props" ON prizepicks_props
  FOR INSERT TO service_role WITH CHECK (true);

CREATE POLICY "Allow service update props" ON prizepicks_props
  FOR UPDATE TO service_role USING (true);

-- Allow users to insert their own analyses requests
CREATE POLICY "Allow user analyses" ON ai_analyses
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id OR user_id IS NULL);