-- PrizePicks Automation: Scheduled scraping and alerts

-- Function to trigger scraping via webhook
CREATE OR REPLACE FUNCTION trigger_prizepicks_scrape()
RETURNS void AS $$
BEGIN
  -- This function will be called by cron to trigger scraping
  -- The actual scraping happens via API call to avoid function timeout
  PERFORM net.http_post(
    url := current_setting('app.base_url') || '/api/prizepicks/scrape',
    headers := '{"Content-Type": "application/json"}'::jsonb,
    body := '{}'::jsonb
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Schedule scraping every 3 hours (8x daily)
SELECT cron.schedule(
  'prizepicks-scrape-8x-daily',
  '0 */3 * * *', -- Every 3 hours
  'SELECT trigger_prizepicks_scrape();'
);

-- Function to detect significant line movements
CREATE OR REPLACE FUNCTION check_significant_movements()
RETURNS TABLE(
  prop_id uuid,
  player_name text,
  prop_type text,
  old_value decimal,
  new_value decimal,
  movement_size decimal,
  movement_time timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    lm.prop_id,
    pp.player_name,
    pp.prop_type,
    lm.old_value,
    lm.new_value,
    lm.movement_size,
    lm.detected_at
  FROM line_movements lm
  JOIN prizepicks_props pp ON lm.prop_id = pp.id
  WHERE lm.detected_at > now() - interval '1 hour'
    AND ABS(lm.movement_size) >= 1.0 -- Significant movement threshold
    AND pp.is_active = true
  ORDER BY ABS(lm.movement_size) DESC;
END;
$$ LANGUAGE plpgsql;

-- View for props needing fresh analysis
CREATE VIEW props_needing_analysis AS
SELECT p.*
FROM prizepicks_props p
LEFT JOIN ai_analyses a ON p.id = a.prop_id
  AND a.created_at > now() - interval '4 hours'
WHERE p.is_active = true
  AND p.game_time > now()
  AND a.id IS NULL -- No recent analysis
ORDER BY p.game_time ASC;

-- Function to clean up old data
CREATE OR REPLACE FUNCTION cleanup_old_prizepicks_data()
RETURNS void AS $$
BEGIN
  -- Remove props for games that ended more than 24 hours ago
  DELETE FROM prizepicks_props
  WHERE game_time < now() - interval '24 hours';

  -- Remove old line movements (keep 7 days)
  DELETE FROM line_movements
  WHERE detected_at < now() - interval '7 days';

  -- Expired analyses are already cleaned up by existing function

  RAISE NOTICE 'Cleaned up old PrizePicks data';
END;
$$ LANGUAGE plpgsql;

-- Schedule daily cleanup
SELECT cron.schedule(
  'prizepicks-cleanup-daily',
  '0 6 * * *', -- 6 AM daily
  'SELECT cleanup_old_prizepicks_data();'
);

-- Function to get live props summary
CREATE OR REPLACE FUNCTION get_live_props_summary()
RETURNS TABLE(
  sport text,
  active_props bigint,
  props_with_analysis bigint,
  props_with_movements bigint,
  latest_scrape timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.sport,
    COUNT(*) as active_props,
    COUNT(a.id) as props_with_analysis,
    COUNT(DISTINCT lm.prop_id) as props_with_movements,
    MAX(p.scraped_at) as latest_scrape
  FROM prizepicks_props p
  LEFT JOIN ai_analyses a ON p.id = a.prop_id
    AND a.created_at > now() - interval '4 hours'
  LEFT JOIN line_movements lm ON p.id = lm.prop_id
    AND lm.detected_at > now() - interval '24 hours'
  WHERE p.is_active = true
    AND p.game_time > now()
  GROUP BY p.sport
  ORDER BY active_props DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to trigger analysis for high-priority props
CREATE OR REPLACE FUNCTION trigger_priority_analysis()
RETURNS void AS $$
DECLARE
  prop_record RECORD;
  analysis_count INTEGER := 0;
BEGIN
  -- Analyze props with recent line movements first
  FOR prop_record IN
    SELECT DISTINCT p.id
    FROM prizepicks_props p
    JOIN line_movements lm ON p.id = lm.prop_id
    LEFT JOIN ai_analyses a ON p.id = a.prop_id
      AND a.created_at > now() - interval '2 hours'
    WHERE p.is_active = true
      AND p.game_time > now()
      AND p.game_time < now() + interval '24 hours' -- Games in next 24h
      AND lm.detected_at > now() - interval '1 hour'
      AND ABS(lm.movement_size) >= 1.0
      AND a.id IS NULL
    ORDER BY ABS(lm.movement_size) DESC
    LIMIT 10 -- Don't overwhelm the API
  LOOP
    -- Trigger analysis via webhook
    PERFORM net.http_post(
      url := current_setting('app.base_url') || '/api/prizepicks/analyze',
      headers := '{"Content-Type": "application/json"}'::jsonb,
      body := json_build_object('propId', prop_record.id)::jsonb
    );

    analysis_count := analysis_count + 1;
  END LOOP;

  RAISE NOTICE 'Triggered % priority analyses', analysis_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Schedule priority analysis every hour
SELECT cron.schedule(
  'prizepicks-priority-analysis',
  '30 * * * *', -- 30 minutes past each hour
  'SELECT trigger_priority_analysis();'
);

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION trigger_prizepicks_scrape() TO service_role;
GRANT EXECUTE ON FUNCTION check_significant_movements() TO authenticated;
GRANT EXECUTE ON FUNCTION get_live_props_summary() TO authenticated;
GRANT SELECT ON props_needing_analysis TO authenticated;