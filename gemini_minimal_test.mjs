import https from 'https';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

async function runMinimalTest() {
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    console.error("🔴 Error: GEMINI_API_KEY is not set in .env.local");
    return;
  }

  const data = JSON.stringify({
    contents: [{
      parts: [{
        text: "Hello"
      }]
    }]
  });

  const options = {
    hostname: 'generativelanguage.googleapis.com',
    path: `/v1beta/models/gemini-pro:generateContent?key=${apiKey}`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length
    }
  };

  console.log("🔵 Attempting minimal API call to Gemini...");

  const req = https.request(options, (res) => {
    let responseBody = '';
    res.on('data', (chunk) => {
      responseBody += chunk;
    });
    res.on('end', () => {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        console.log("🟢 Success! Gemini API responded:");
        try {
          const parsed = JSON.parse(responseBody);
          console.log(JSON.stringify(parsed, null, 2));
        } catch (e) {
          console.error("🔴 Error parsing JSON response:", responseBody);
        }
      } else {
        console.error(`🔴 Error: Received status code ${res.statusCode}`);
        console.error(responseBody);
      }
    });
  });

  req.on('error', (error) => {
    console.error("🔴 Error connecting to Gemini API:", error.message);
  });

  req.write(data);
  req.end();
}

runMinimalTest();
