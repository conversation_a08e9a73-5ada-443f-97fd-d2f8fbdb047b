# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

- **Development**: `npm run dev` (uses turbopack for faster builds)
- **Development with Turbo**: `npm run dev:turbo` (explicit turbopack usage)
- **Build**: `npm run build`
- **Lint**: `npm run lint`
- **Production**: `npm run start`
- **Changelog**: `npm run codebook` (generate changelog from git commits)
- **Weekly Changelog**: `npm run codebook:week`
- **Monthly Changelog**: `npm run codebook:month`

## Architecture

OnlyDiary is a Next.js 15 platform for diary entries, book publishing, and creator monetization with comprehensive AI features ("Genyus"). Built with App Router, TypeScript, and integrated third-party services.

### Core Features
- **Content Creation**: Diary entries, books/chapters, audio posts, video content
- **Monetization**: Subscriptions, donations, book sales, credit system
- **AI Integration**: "Genyus" chat system with memory, context packs, and conversation intelligence
- **Social Features**: Comments, reactions, follows, mailing lists
- **Media Handling**: Photos, videos, audio with AWS S3/Cloudflare R2 storage
- **Payment Processing**: Stripe integration with multi-tier fee structure

### Tech Stack
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript with strict mode
- **Styling**: Tailwind CSS v4 with custom design system
- **Database**: Supabase with comprehensive RLS policies
- **Storage**: AWS S3, Cloudflare R2 for media
- **Payments**: Stripe with Connect for creator payouts
- **AI**: OpenAI GPT-4, Anthropic Claude, Google Gemini
- **Email**: Resend for transactional emails
- **Analytics**: Vercel Analytics, Google Analytics, Facebook Pixel
- **Fonts**: Geist Sans and Geist Mono

### Project Structure
- `app/` - Next.js App Router pages, API routes, and layouts
- `lib/` - Core business logic, utilities, and integrations
  - `lib/supabase/` - Database clients, types, and utilities
  - `lib/stripe/` - Payment processing and monetization
  - `lib/genyus/` - AI chat system and memory management
  - `lib/email/` - Email notifications and templates
- `components/` - React components organized by feature
- `supabase/migrations/` - Database schema and migration files
- `styles/` - Global styles and typography definitions

### Key Integrations
- **Supabase**: Database with RLS, real-time subscriptions, auth
- **Stripe**: Payments, subscriptions, Connect for creator payouts
- **OpenAI/Anthropic/Google**: AI chat features with conversation memory
- **AWS Rekognition**: Image moderation and content analysis
- **Cloudflare R2**: Video storage and processing
- **Resend**: Transactional emails and notifications

### Database Schema
- Multi-role user system (visitor, subscriber, writer, admin, user)
- Content tables: diary_entries, projects, chapters, comments
- Monetization: payments, post_credits, withdrawals, donations
- Social: follows, reactions, bookmarks, blocks
- AI: genyus_conversations, genyus_memories, context_packs
- Media: photos, videos, audio_posts with moderation

### Configuration
- TypeScript path mapping: `@/*` points to root directory
- Next.js with image optimization for Supabase, Unsplash, Google, GitHub
- Security headers including CSP, HSTS, frame options
- ESLint/TypeScript build errors ignored for production deploys
- PWA support with service worker (currently disabled)