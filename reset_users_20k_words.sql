-- Reset all users to 20,000 words fresh
-- Run this to give everyone a fresh start with 20k words

-- Update existing users to 20k words
UPDATE genyus_user_words
SET words_remaining = 20000,
    updated_at = NOW(),
    last_reset = NOW()
WHERE words_remaining != -1;  -- Don't update unlimited users

-- Reset any existing records that might have been missed
UPDATE genyus_user_words
SET words_remaining = 20000,
    updated_at = NOW(),
    last_reset = NOW()
WHERE tier = 'free' OR tier = 'starter';

-- Show results to verify
SELECT
    tier,
    COUNT(*) as user_count,
    AVG(words_remaining) as avg_words,
    MIN(words_remaining) as min_words,
    MAX(words_remaining) as max_words
FROM genyus_user_words
GROUP BY tier
ORDER BY tier;

-- Optional: Clear any cached responses so users get fresh responses
-- TRUNCATE TABLE genyus_response_cache;

-- Optional: Clear request/response history (uncomment if needed)
-- DELETE FROM genyus_answers WHERE created_at < NOW() - INTERVAL '30 days';
-- DELETE FROM genyus_provider_responses WHERE created_at < NOW() - INTERVAL '30 days';
-- DELETE FROM genyus_requests WHERE created_at < NOW() - INTERVAL '30 days';