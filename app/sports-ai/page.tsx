'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { createSupabaseClient } from '@/lib/supabase/client';
import { useRouter } from 'next/navigation';

const supabase = createSupabaseClient();

export default function SportsAILandingPage() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [nextUpdate, setNextUpdate] = useState('');
  const [currentTrend, setCurrentTrend] = useState('');
  const router = useRouter();

  // Calculate next update time
  useEffect(() => {
    const calculateNextUpdate = () => {
      const now = new Date();
      const estOffset = -5 * 60; // EST is UTC-5
      const estNow = new Date(now.getTime() + (estOffset + now.getTimezoneOffset()) * 60000);

      const updateTimes = [6, 9, 12, 15, 18, 21, 0, 3]; // Every 3 hours EST
      let nextUpdateTime = null;

      for (const hour of updateTimes) {
        const updateTime = new Date(estNow);
        updateTime.setHours(hour, 0, 0, 0);

        if (updateTime > estNow) {
          nextUpdateTime = updateTime;
          break;
        }
      }

      // If no next update today, use first update tomorrow
      if (!nextUpdateTime) {
        nextUpdateTime = new Date(estNow);
        nextUpdateTime.setDate(estNow.getDate() + 1);
        nextUpdateTime.setHours(8, 0, 0, 0);
      }

      const timeUntilUpdate = nextUpdateTime.getTime() - estNow.getTime();
      const hoursUntil = Math.floor(timeUntilUpdate / (1000 * 60 * 60));
      const minutesUntil = Math.floor((timeUntilUpdate % (1000 * 60 * 60)) / (1000 * 60));

      setNextUpdate(`${hoursUntil}h ${minutesUntil}m`);
    };

    // Set current trend
    const trends = [
      "NBA player props mispriced by 12+ points",
      "3 MLB totals showing massive line movement",
      "NFL receiving yards creating +EV opportunities",
      "Tennis match momentum indicators flashing green",
      "Hockey goalies facing value-heavy matchups",
      "Soccer corner markets offering 15%+ edge"
    ];
    setCurrentTrend(trends[Math.floor(Math.random() * trends.length)]);

    calculateNextUpdate();
    const interval = setInterval(calculateNextUpdate, 60000);
    return () => clearInterval(interval);
  }, []);

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) return;

    setIsLoading(true);

    try {
      // For now, just redirect to register with email pre-filled
      // You can enhance this later with actual trial signup logic
      router.push(`/register?email=${encodeURIComponent(email)}&source=sports-ai`);
    } catch (error) {
      console.error('Signup error:', error);
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/3 w-64 h-64 bg-green-500/10 rounded-full blur-3xl animate-pulse delay-500"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          {/* Header */}
          <div className="text-center mb-16">
            {/* Live Indicator */}
            <div className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-full px-4 py-2 mb-8">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-green-400 text-sm font-medium">LIVE: {currentTrend}</span>
            </div>

            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
              AI Analyzes <span className="bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">10,000+</span><br />
              Sports Lines <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">While You Sleep</span>
            </h1>

            <p className="text-xl sm:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed">
              Fresh data every 3 hours. Smart money movements detected instantly.<br />
              <span className="text-green-400 font-semibold">Next update in {nextUpdate}</span>
            </p>

            {/* Signup Form */}
            <div className="max-w-lg mx-auto mb-12">
              <form onSubmit={handleSignup} className="flex flex-col sm:flex-row gap-4">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email for 7-day free trial"
                  className="flex-1 px-6 py-4 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-400 transition-all"
                  required
                />
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-8 py-4 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold rounded-xl transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl disabled:opacity-50"
                >
                  {isLoading ? 'Starting...' : 'Start Free Trial →'}
                </button>
              </form>
              <p className="text-sm text-gray-400 mt-3">7 days free, then $19.99/month. Cancel anytime.</p>
            </div>

            {/* Social Proof */}
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 text-gray-300">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                <span className="text-sm">Data updated every 3 hours</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                <span className="text-sm">All major sports covered</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
                <span className="text-sm">Real-time line movements</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="relative py-20 bg-black/20 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">The Smart Money Never Sleeps</h2>
            <p className="text-xl text-gray-300">Neither does our AI. Here's what happens behind the scenes:</p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:border-white/20 transition-all group">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <span className="text-2xl">🎯</span>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Continuous Line Monitoring</h3>
              <p className="text-gray-300 leading-relaxed">
                Our algorithms scan thousands of prop lines across NBA, NFL, MLB, NHL, Tennis, Soccer, and MMA.
                <span className="text-green-400 font-semibold"> Updated every 3 hours around the clock</span>
                to catch every movement that matters.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:border-white/20 transition-all group">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <span className="text-2xl">⚡</span>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Smart Money Detection</h3>
              <p className="text-gray-300 leading-relaxed">
                When lines move against public betting patterns, our AI flags potential value.
                <span className="text-blue-400 font-semibold"> Sharp action creates opportunities</span>
                that disappear within minutes - we catch them first.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:border-white/20 transition-all group">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <span className="text-2xl">🧠</span>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Personalized Intelligence</h3>
              <p className="text-gray-300 leading-relaxed">
                Tell our AI your preferred sports, bet types, and risk tolerance. Get
                <span className="text-purple-400 font-semibold"> custom recommendations</span>
                that match your style, not generic picks.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="relative py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div className="group">
              <div className="text-5xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent mb-4 group-hover:scale-110 transition-transform">
                10,000+
              </div>
              <div className="text-xl text-white font-semibold mb-2">Lines Analyzed Daily</div>
              <div className="text-gray-400">Across all major sports and markets</div>
            </div>
            <div className="group">
              <div className="text-5xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-4 group-hover:scale-110 transition-transform">
                8x
              </div>
              <div className="text-xl text-white font-semibold mb-2">Daily Updates</div>
              <div className="text-gray-400">Fresh data every 3 hours</div>
            </div>
            <div className="group">
              <div className="text-5xl font-bold bg-gradient-to-r from-blue-400 to-green-400 bg-clip-text text-transparent mb-4 group-hover:scale-110 transition-transform">
                &lt;30s
              </div>
              <div className="text-xl text-white font-semibold mb-2">Response Time</div>
              <div className="text-gray-400">From question to actionable insight</div>
            </div>
          </div>
        </div>
      </div>

      {/* How It Works */}
      <div className="relative py-20 bg-black/20 backdrop-blur-sm">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">How It Works</h2>
            <p className="text-xl text-gray-300">Three steps to smarter betting</p>
          </div>

          <div className="space-y-8">
            {/* Step 1 */}
            <div className="flex flex-col lg:flex-row items-center gap-12">
              <div className="lg:w-1/2">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-xl">1</div>
                  <h3 className="text-3xl font-bold text-white">Ask Your Question</h3>
                </div>
                <p className="text-xl text-gray-300 leading-relaxed">
                  "What NBA player props look good tonight?" or "Any MLB totals moving with sharp action?"
                  Our AI understands natural language and sports context.
                </p>
              </div>
              <div className="lg:w-1/2">
                <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-white/10">
                  <div className="bg-white/5 rounded-lg p-4 mb-4">
                    <div className="text-green-400 text-sm mb-2">You:</div>
                    <div className="text-white">"Any value picks for tonight's games?"</div>
                  </div>
                  <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-lg p-4">
                    <div className="text-blue-400 text-sm mb-2">PrizePicks AI:</div>
                    <div className="text-white text-sm">🎯 Found 3 high-value opportunities:<br />• Jayson Tatum OVER 23.5 points<br />• Lakers/Warriors UNDER 234.5<br />• De'Aaron Fox OVER 6.5 assists</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Step 2 */}
            <div className="flex flex-col lg:flex-row-reverse items-center gap-12">
              <div className="lg:w-1/2">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-xl">2</div>
                  <h3 className="text-3xl font-bold text-white">Get Smart Analysis</h3>
                </div>
                <p className="text-xl text-gray-300 leading-relaxed">
                  Receive detailed breakdowns showing line movements, historical data, and why our algorithms
                  flagged each opportunity. No guesswork.
                </p>
              </div>
              <div className="lg:w-1/2">
                <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-white/10">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Line Movement</span>
                      <span className="text-green-400">📈 +2.5 points</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Sharp Action</span>
                      <span className="text-blue-400">🔥 85% on OVER</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Historical Edge</span>
                      <span className="text-purple-400">⭐ 12% value</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Step 3 */}
            <div className="flex flex-col lg:flex-row items-center gap-12">
              <div className="lg:w-1/2">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">3</div>
                  <h3 className="text-3xl font-bold text-white">Make Informed Decisions</h3>
                </div>
                <p className="text-xl text-gray-300 leading-relaxed">
                  Armed with data-driven insights, make confident bets knowing you have the same information
                  that sharp bettors pay thousands for.
                </p>
              </div>
              <div className="lg:w-1/2">
                <div className="bg-gradient-to-br from-green-500/10 to-blue-500/10 rounded-2xl p-6 border border-green-500/30">
                  <div className="text-center">
                    <div className="text-4xl mb-4">🎯</div>
                    <div className="text-2xl font-bold text-white mb-2">Confident Betting</div>
                    <div className="text-green-400">Data-driven decisions</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Final CTA */}
      <div className="relative py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6">
            Stop Guessing. Start <span className="bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">Winning</span>.
          </h2>
          <p className="text-xl text-gray-300 mb-12">
            Join the AI revolution in sports betting. Your first week is free.
          </p>

          <div className="max-w-lg mx-auto mb-8">
            <form onSubmit={handleSignup} className="flex flex-col sm:flex-row gap-4">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                className="flex-1 px-6 py-4 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-400 transition-all"
                required
              />
              <button
                type="submit"
                disabled={isLoading}
                className="px-8 py-4 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold rounded-xl transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl disabled:opacity-50"
              >
                {isLoading ? 'Starting...' : 'Start Free Trial'}
              </button>
            </form>
          </div>

          <div className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-6 text-sm text-gray-400">
            <span>✓ 7-day free trial</span>
            <span>✓ $19.99/month after trial</span>
            <span>✓ Cancel anytime</span>
            <span>✓ No long-term commitment</span>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="border-t border-white/10 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row items-center justify-between">
            <div className="text-gray-400 text-sm mb-4 sm:mb-0">
              © 2025 OnlyDiary. Advanced sports intelligence platform.
            </div>
            <div className="flex space-x-6 text-sm text-gray-400">
              <Link href="/terms" className="hover:text-white transition-colors">Terms</Link>
              <Link href="/privacy" className="hover:text-white transition-colors">Privacy</Link>
              <Link href="/login" className="hover:text-white transition-colors">Login</Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}