"use client"

import { useState, useTransition, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter, useSearchParams } from "next/navigation"
import { But<PERSON>, GoogleButton } from "@/components/ui/button"
import { LinkButton } from "@/components/ui/link-button"

export default function RegisterPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [name, setName] = useState("")

  const searchParams = useSearchParams()

  // Pre-fill email from URL parameters
  useEffect(() => {
    const emailParam = searchParams.get('email')
    if (emailParam) {
      setEmail(emailParam)
    }
  }, [searchParams])

  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [isPending, startTransition] = useTransition()
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)
  const router = useRouter()
  const supabase = createSupabaseClient()

  const handleRegister = (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setSuccess("")

    startTransition(async () => {
      try {
        // Get source parameter to track where user came from
        const source = searchParams.get('source')

        // Sign up with Supabase Auth
        const { data, error: signUpError } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              name: name,
              role: 'user',
              signup_source: source || 'direct'
            }
          }
        })

        if (signUpError) {
          // Handle specific error cases
          if (signUpError.message.includes('already registered')) {
            setError('This email is already registered. Please try signing in instead, or use a different email.')
          } else if (signUpError.message.includes('email')) {
            setError('There was an issue with this email address. Please check and try again, or contact support if you continue having issues.')
          } else {
            setError(signUpError.message)
          }
          return
        }

        if (data.user) {
          // Send welcome email
          try {
            await fetch('/api/send-welcome-email', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                userId: data.user.id,
                userEmail: email,
                userName: name,
                userRole: 'user'
              })
            })
          } catch (emailError) {
            console.log('Welcome email failed (non-critical):', emailError)
          }

          // Customize success message and redirect based on source
          if (source === 'sports-ai') {
            setSuccess("Welcome to PrizePicks AI! Please check your email to verify your account and start your free trial.")
            setTimeout(() => router.push("/login?next=/prizepicks"), 3000)
          } else {
            setSuccess("Welcome to OnlyDiary! Please check your email to verify your account and start your journey.")
            setTimeout(() => router.push("/login"), 3000)
          }
        }
      } catch {
        setError("An unexpected error occurred")
      }
    })
  }

  const handleGoogleSignUp = async () => {
    setError("")
    setIsGoogleLoading(true)

    try {
      const params = new URLSearchParams(window.location.search)
      const source = params.get('source')
      const nextDest = source === 'sports-ai' ? '/prizepicks' : (params.get('next') || '/timeline')
      const origin = window.location.origin // Always use runtime origin (handles ngrok/localhost/prod)
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${origin}/auth/callback?next=${encodeURIComponent(nextDest)}`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        }
      })

      if (error) {
        setError('Failed to sign up with Google. Please try again.')
        setIsGoogleLoading(false)
      }
    } catch {
      setError('An unexpected error occurred with Google sign up')
      setIsGoogleLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center px-4 sm:px-6">
      <div className="max-w-md w-full bg-white rounded-lg p-8 shadow-sm">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-serif text-gray-800">Join OnlyDiary</h1>
          <p className="text-gray-600 font-serif mt-2">
            Share your stories, discover amazing content, and connect with creators
          </p>
        </div>

        {/* In-app browser notice (helps Google OAuth reliability) */}
        {typeof navigator !== 'undefined' && /FBAN|FBAV|Instagram|Line|Wechat|Weibo|TikTok/i.test(navigator.userAgent) && (
          <div className="mb-3 rounded-lg border border-yellow-200 bg-yellow-50 p-3 text-sm text-yellow-900 text-left">
            For a secure Google sign-in, open this page in your device’s browser (Safari/Chrome).
            In Instagram/Twitter, tap ••• or the share icon → Open in Browser.
          </div>
        )}

        {/* Google Sign Up Button */}
        <GoogleButton
          onClick={handleGoogleSignUp}
          isLoading={isGoogleLoading}
          disabled={isPending}
        >
          Continue with Google
        </GoogleButton>

        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Or continue with email</span>
          </div>
        </div>

        <form onSubmit={handleRegister} className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              Full Name
            </label>
            <input
              id="name"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent placeholder:text-gray-600 md:placeholder:text-gray-500"
              placeholder="Your full name"
            />
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent placeholder:text-gray-600 md:placeholder:text-gray-500"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              minLength={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent placeholder:text-gray-600 md:placeholder:text-gray-500"
              placeholder="At least 6 characters"
            />
          </div>

          <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
            <div className="flex items-center gap-3 mb-2">
              <span className="text-2xl">✨</span>
              <h3 className="font-medium text-gray-800">What you can do on OnlyDiary</h3>
            </div>
            <ul className="text-sm text-gray-600 space-y-1">
              <li className="flex items-center gap-2">
                <span className="text-blue-500">📖</span>
                Create and share diary entries
              </li>
              <li className="flex items-center gap-2">
                <span className="text-blue-500">📚</span>
                Publish books and stories
              </li>
              <li className="flex items-center gap-2">
                <span className="text-blue-500">💝</span>
                Support creators you love
              </li>
              <li className="flex items-center gap-2">
                <span className="text-blue-500">🌟</span>
                Discover amazing content
              </li>
            </ul>
          </div>

          {error && (
            <div className="text-red-600 text-sm font-medium bg-red-50 p-3 rounded-lg">
              {error}
            </div>
          )}

          {success && (
            <div className="text-green-600 text-sm font-medium bg-green-50 p-3 rounded-lg">
              {success}
            </div>
          )}

          <Button
            type="submit"
            isLoading={isPending}
            className="w-full bg-blue-600 text-white hover:bg-blue-700"
          >
            Join OnlyDiary
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-gray-600 font-serif mb-4">
            Already have an account?
          </p>
          <LinkButton href="/login" variant="outline" className="w-full">
            Sign In
          </LinkButton>
        </div>
      </div>
    </div>
  )
}