'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createSupabaseClient } from '@/lib/supabase/client';
import { ConnectionStatus } from '@/components/ConnectionStatus';

const supabase = createSupabaseClient();

export default function PrizePicksPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [authLoading, setAuthLoading] = useState(true);
  const [dataFreshness, setDataFreshness] = useState<string>('Calculating...');
  const [nextUpdate, setNextUpdate] = useState<string>('Calculating...');
  const [currentTrends, setCurrentTrends] = useState<string>('Analyzing market...');
  const router = useRouter();

  // Calculate dynamic data freshness based on EST update schedule
  useEffect(() => {
    const calculateUpdateTimes = () => {
      const now = new Date();
      const estOffset = -5 * 60; // EST is UTC-5
      const estNow = new Date(now.getTime() + (estOffset + now.getTimezoneOffset()) * 60000);
      
      const updateTimes = [8, 12, 17, 23]; // 8AM, 12PM, 5PM, 11PM EST
      let nextUpdateTime = null;
      let lastUpdateTime = null;

      for (const hour of updateTimes) {
        const updateTime = new Date(estNow);
        updateTime.setHours(hour, 0, 0, 0);
        
        if (updateTime > estNow) {
          nextUpdateTime = updateTime;
          break;
        }
        lastUpdateTime = updateTime;
      }

      // If no next update today, use first update tomorrow
      if (!nextUpdateTime) {
        nextUpdateTime = new Date(estNow);
        nextUpdateTime.setDate(estNow.getDate() + 1);
        nextUpdateTime.setHours(8, 0, 0, 0);
      }

      // Calculate time until next update
      const timeUntilUpdate = nextUpdateTime.getTime() - estNow.getTime();
      const hoursUntil = Math.floor(timeUntilUpdate / (1000 * 60 * 60));
      const minutesUntil = Math.floor((timeUntilUpdate % (1000 * 60 * 60)) / (1000 * 60));
      
      setNextUpdate(`${hoursUntil}h ${minutesUntil}m`);

      // Calculate last update time
      if (lastUpdateTime) {
        const timeSinceUpdate = estNow.getTime() - lastUpdateTime.getTime();
        const hoursSince = Math.floor(timeSinceUpdate / (1000 * 60 * 60));
        const minutesSince = Math.floor((timeSinceUpdate % (1000 * 60 * 60)) / (1000 * 60));
        
        if (hoursSince > 0) {
          setDataFreshness(`${hoursSince}h ${minutesSince}m ago`);
        } else {
          setDataFreshness(`${minutesSince}m ago`);
        }
      }

      // Generate random trends based on time of day
      const trends = [
        "3+ NBA lines moving with smart money",
        "2+ MLB value picks detected", 
        "NFL player props showing +EV",
        "NHL goalie matchups creating value",
        "Tennis underdogs with strong metrics",
        "Esports totals offering excellent value",
        "Soccer corner counts mispriced",
        "MMA fighter props undervalued"
      ];
      setCurrentTrends(trends[Math.floor(Math.random() * trends.length)]);
    };

    calculateUpdateTimes();
    const interval = setInterval(calculateUpdateTimes, 60000); // Update every minute
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        router.push('/login');
        return;
      }
      setUser(user);
      setAuthLoading(false);
    };
    checkAuth();
  }, [router]);

  if (authLoading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  }

  if (!user) {
    return null;
  }

  const handleStartConversation = () => {
    setIsLoading(true);
    // Redirect to the PrizePicks chat interface
    router.push('/prizepicks/chat');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 to-blue-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">🎯 PrizePicks AI</h1>
          <p className="text-xl opacity-90">
            The world's most advanced sports betting intelligence powered by OnlyGenyus
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 className="text-2xl font-semibold mb-4">How It Works</h2>
            <ul className="space-y-3">
              <li>• Real-time line data updated 4x daily (8AM, 12PM, 5PM, 11PM EST)</li>
              <li>• AI-powered analysis of thousands of lines across all sports</li>
              <li>• +EV opportunity identification based on line movements</li>
              <li>• Personalized recommendations based on your preferences</li>
            </ul>
          </div>

          <div className="grid md:grid-cols-2 gap-6 mb-6">
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-3">📊 Data Freshness</h3>
              <p>Next update in: {nextUpdate}</p>
              <p className="text-sm opacity-75">Last updated: {dataFreshness}</p>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-3">🔥 Current Trends</h3>
              <p>{currentTrends}</p>
              <p className="text-sm opacity-75">Live market analysis</p>
            </div>
          </div>

          <div className="text-center">
            <button
              onClick={handleStartConversation}
              disabled={isLoading}
              className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-4 px-8 rounded-lg text-xl transition-all duration-200 disabled:opacity-50"
            >
              {isLoading ? 'Loading...' : 'Start PrizePicks Analysis'}
            </button>
          </div>

          <div className="mt-6 flex justify-center">
            <ConnectionStatus />
          </div>
        </div>
      </div>
    </div>
  );
}
