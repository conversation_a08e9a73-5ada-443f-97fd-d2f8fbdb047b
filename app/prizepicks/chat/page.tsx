'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { createSupabaseClient } from '@/lib/supabase/client';
import Link from 'next/link';
import { ConnectionStatus } from '@/components/ConnectionStatus';

const supabase = createSupabaseClient();

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

export default function PrizePicksChatPage() {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isAsking, setIsAsking] = useState(false);
  const [selectedSport, setSelectedSport] = useState<string>('all');
  const [dataFreshness, setDataFreshness] = useState<string>('Calculating...');
  const [nextUpdate, setNextUpdate] = useState<string>('Calculating...');

  const router = useRouter();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const sports = [
    { value: 'all', label: 'All Sports' },
    { value: 'nba', label: 'NBA' },
    { value: 'nfl', label: 'NFL' },
    { value: 'mlb', label: 'MLB' },
    { value: 'nhl', label: 'NHL' },
    { value: 'soccer', label: 'Soccer' },
    { value: 'mma', label: 'MMA' },
    { value: 'tennis', label: 'Tennis' },
    { value: 'esports', label: 'E-Sports' },
    { value: 'golf', label: 'Golf' },
    { value: 'ncaab', label: 'NCAA Basketball' },
    { value: 'ncaaf', label: 'NCAA Football' },
    { value: 'wnba', label: 'WNBA' },
    { value: 'formula1', label: 'Formula 1' }
  ];

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        router.push('/login');
        return;
      }
      setUser(user);
      setLoading(false);
    };
    checkAuth();
  }, [router]);

  // Calculate dynamic data freshness based on EST update schedule
  useEffect(() => {
    const calculateUpdateTimes = () => {
      const now = new Date();
      const estOffset = -5 * 60; // EST is UTC-5
      const estNow = new Date(now.getTime() + (estOffset + now.getTimezoneOffset()) * 60000);
      
      const updateTimes = [8, 12, 17, 23]; // 8AM, 12PM, 5PM, 11PM EST
      let nextUpdateTime = null;
      let lastUpdateTime = null;

      for (const hour of updateTimes) {
        const updateTime = new Date(estNow);
        updateTime.setHours(hour, 0, 0, 0);
        
        if (updateTime > estNow) {
          nextUpdateTime = updateTime;
          break;
        }
        lastUpdateTime = updateTime;
      }

      // If no next update today, use first update tomorrow
      if (!nextUpdateTime) {
        nextUpdateTime = new Date(estNow);
        nextUpdateTime.setDate(estNow.getDate() + 1);
        nextUpdateTime.setHours(8, 0, 0, 0);
      }

      // Calculate time until next update
      const timeUntilUpdate = nextUpdateTime.getTime() - estNow.getTime();
      const hoursUntil = Math.floor(timeUntilUpdate / (1000 * 60 * 60));
      const minutesUntil = Math.floor((timeUntilUpdate % (1000 * 60 * 60)) / (1000 * 60));
      
      setNextUpdate(`${hoursUntil}h ${minutesUntil}m`);

      // Calculate last update time
      if (lastUpdateTime) {
        const timeSinceUpdate = estNow.getTime() - lastUpdateTime.getTime();
        const hoursSince = Math.floor(timeSinceUpdate / (1000 * 60 * 60));
        const minutesSince = Math.floor((timeSinceUpdate % (1000 * 60 * 60)) / (1000 * 60));
        
        if (hoursSince > 0) {
          setDataFreshness(`${hoursSince}h ${minutesSince}m ago`);
        } else {
          setDataFreshness(`${minutesSince}m ago`);
        }
      }
    };

    calculateUpdateTimes();
    const interval = setInterval(calculateUpdateTimes, 60000); // Update every minute
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // Add welcome message when chat starts with dynamic content
    if (messages.length === 0 && !loading && nextUpdate !== 'Calculating...') {
      const trends = [
        "3+ NBA lines moving with smart money",
        "2+ MLB value picks detected", 
        "NFL player props showing +EV",
        "NHL goalie matchups creating value",
        "Tennis underdogs with strong metrics",
        "Esports totals offering excellent value",
        "Soccer corner counts mispriced",
        "MMA fighter props undervalued"
      ];
      const currentTrend = trends[Math.floor(Math.random() * trends.length)];
      
      setMessages([{
        id: 'welcome',
        type: 'assistant',
        content: `🎯 WELCOME TO PRIZEPICKS MODE 🎯\n\n*Real-time sports intelligence powered by OnlyGenyus*\n\n📅 **Data Updated**: 8 AM • 12 PM • 5 PM • 11 PM EST\n⏰ **Next Update**: In ${nextUpdate}\n🔥 **Current Hot Moves**: ${currentTrend}\n\nMy algorithms analyze thousands of lines to find you +EV opportunities that 99% of bettors miss.\n\nWould you like:\n1. **THE ELITE 5** - My top picks across all sports  \n2. **SPORT-SPECIFIC** - NBA/NFL/MLB/NHL/Soccer/MMA/Tennis/E-Sports best plays\n3. **LIVE MOVEMENTS** - Lines moving right now\n4. **PLAYER FOCUS** - Specific players you're watching\n\nWhat's your play? 🚀`,
        timestamp: new Date()
      }]);
    }
  }, [loading, nextUpdate]);

  useEffect(() => {
    // Auto-scroll to bottom when messages change
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || isAsking || !user) return;

    const question = inputValue.trim();
    setInputValue('');
    setIsAsking(true);

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: question,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, userMessage]);

    // Add streaming assistant message
    const assistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      isStreaming: true
    };
    setMessages(prev => [...prev, assistantMessage]);

    try {
      const response = await fetch('/api/prizepicks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question,
          sport: selectedSport
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get response');
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      const decoder = new TextDecoder('utf-8');
      let result = '';

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          result += chunk;
          
          setMessages(prev => prev.map(msg => 
            msg.id === assistantMessage.id 
              ? { ...msg, content: result }
              : msg
          ));
        }
      }

      // Mark as complete
      setMessages(prev => prev.map(msg => 
        msg.id === assistantMessage.id 
          ? { ...msg, isStreaming: false }
          : msg
      ));

    } catch (error) {
      console.error('Error asking PrizePicks AI:', error);
      setMessages(prev => prev.slice(0, -1)); // Remove streaming message
      setMessages(prev => [...prev, {
        id: (Date.now() + 2).toString(),
        type: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date()
      }]);
    } finally {
      setIsAsking(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading...</div>
      </div>
    );
  }

  if (!user) return null;

  return (
    <div className="h-screen bg-gradient-to-br from-purple-900 to-blue-900 flex flex-col">
      {/* Header - Mobile Optimized */}
      <div className="flex-shrink-0 bg-black/20 backdrop-blur-sm px-3 sm:px-6 py-2 sm:py-3">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          {/* Back Button - Hidden on mobile, shown with icon only */}
          <Link
            href="/prizepicks"
            className="flex items-center space-x-1 sm:space-x-2 text-white/80 hover:text-white transition-colors"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span className="hidden sm:inline text-sm font-medium">Back</span>
          </Link>

          {/* Center: PrizePicks Branding - Responsive */}
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xs sm:text-sm">🎯</span>
            </div>
            <h1 className="text-base sm:text-lg font-semibold text-white">PrizePicks AI</h1>
          </div>

          {/* Right: Data Status - Compact on mobile */}
          <div className="text-right">
            <div className="text-[10px] sm:text-xs text-white/70">
              Updated: {dataFreshness}
            </div>
            <div className="text-[10px] sm:text-xs text-green-400">
              Next: {nextUpdate}
            </div>
          </div>
        </div>
      </div>

      {/* Sport Filter - Mobile Optimized */}
      <div className="flex-shrink-0 bg-black/10 backdrop-blur-sm px-3 sm:px-6 py-2">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center space-x-2 overflow-x-auto pb-1">
            <span className="text-xs text-white/70 whitespace-nowrap hidden sm:inline">Filter:</span>
            <span className="text-xs text-white/70 whitespace-nowrap sm:hidden">🎯</span>
            {sports.map((sport) => (
              <button
                key={sport.value}
                onClick={() => setSelectedSport(sport.value)}
                className={`px-2.5 py-1 sm:px-3 sm:py-1.5 rounded-lg text-[11px] sm:text-xs font-medium transition-all whitespace-nowrap flex-shrink-0 ${
                  selectedSport === sport.value
                    ? 'bg-white/20 text-white border border-white/30'
                    : 'bg-white/10 text-white/80 hover:bg-white/20'
                }`}
              >
                {sport.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Messages Container - Mobile Optimized */}
      <div className="flex-1 overflow-y-auto px-3 sm:px-6 py-4">
        <div className="max-w-4xl mx-auto space-y-4 sm:space-y-6">
          {messages.map((message, index) => (
            <div
              key={message.id}
              className={`animate-in slide-in-from-bottom-4 duration-300 ${
                message.type === 'user' ? 'flex justify-end' : 'flex justify-start'
              }`}
            >
              <div className={`max-w-[90%] sm:max-w-[85%] ${
                message.type === 'user' ? 'text-right' : 'text-left'
              }`}>
                {message.type === 'user' ? (
                  <div className="bg-white/20 backdrop-blur-sm rounded-xl sm:rounded-2xl px-4 py-2 sm:px-5 sm:py-3">
                    <div className="font-medium leading-relaxed text-white text-sm sm:text-base">
                      {message.content}
                    </div>
                    <div className="text-[10px] sm:text-xs text-white/50 mt-1 sm:mt-2">
                      {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </div>
                  </div>
                ) : (
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl sm:rounded-2xl px-4 py-3 sm:px-5 sm:py-4 border border-white/20">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-5 h-5 sm:w-6 sm:h-6 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                        <span className="text-white font-bold text-[10px] sm:text-xs">🎯</span>
                      </div>
                      <span className="text-sm font-medium text-white">PrizePicks AI</span>
                      <span className="text-[10px] sm:text-xs text-white/50">
                        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                    </div>
                    <div className="text-sm sm:text-base text-white leading-relaxed whitespace-pre-wrap">
                      {message.isStreaming && !message.content ? (
                        <div className="flex items-center space-x-2 text-white/70">
                          <div className="flex space-x-1">
                            <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                            <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                            <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                          </div>
                          <span className="text-xs sm:text-sm">Analyzing lines...</span>
                        </div>
                      ) : (
                        <>
                          {message.content}
                          {message.isStreaming && message.content && (
                            <span className="inline-block w-1.5 h-3 sm:w-2 sm:h-4 bg-green-400 ml-1 animate-pulse rounded-sm" />
                          )}
                        </>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input Area - Mobile Optimized */}
      <div className="flex-shrink-0 border-t border-white/10 bg-black/20 backdrop-blur-sm px-3 sm:px-6 py-4 pb-16 sm:pb-20">
        <div className="max-w-4xl mx-auto">
          <form onSubmit={handleSubmit} className="relative">
            <div className="relative flex items-end space-x-2 sm:space-x-3">
              <div className="flex-1 relative">
                <textarea
                  value={inputValue}
                  onChange={(e) => {
                    setInputValue(e.target.value);
                    // Auto-resize
                    const textarea = e.target;
                    textarea.style.height = 'auto';
                    textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
                  }}
                  onKeyDown={handleKeyDown}
                  placeholder="Ask about picks, lines, or player props..."
                  className="w-full resize-none border border-white/20 rounded-lg sm:rounded-xl px-3 py-2 sm:px-4 sm:py-3 pr-12 sm:pr-16 text-sm sm:text-base placeholder-white/60 text-white bg-white/10 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-300 transition-all"
                  rows={1}
                  maxLength={1000}
                  disabled={isAsking}
                  style={{
                    minHeight: '42px',
                    maxHeight: '100px'
                  }}
                />
                <div className="absolute right-2 sm:right-3 bottom-2 sm:bottom-3 flex items-center space-x-1 sm:space-x-2">
                  {isAsking ? (
                    <div className="flex items-center space-x-1 text-[10px] sm:text-xs text-green-400">
                      <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="hidden xs:inline">Analyzing...</span>
                    </div>
                  ) : (
                    <div className="text-[10px] sm:text-xs text-white/40">
                      {inputValue.length}/1000
                    </div>
                  )}
                </div>
              </div>
              <button
                type="submit"
                disabled={!inputValue.trim() || isAsking}
                className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 disabled:from-gray-400 disabled:to-gray-400 text-white rounded-lg sm:rounded-xl transition-all flex items-center justify-center disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
              >
                {isAsking ? (
                  <div className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Connection Status */}
      <div className="fixed bottom-4 right-4">
        <ConnectionStatus />
      </div>
    </div>
  );
}
