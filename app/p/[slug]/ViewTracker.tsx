'use client'

import { useEffect } from 'react'

interface ViewTrackerProps {
  pageId: string
}

export default function ViewTracker({ pageId }: ViewTrackerProps) {
  useEffect(() => {
    // Generate or get visitor ID
    let visitorId = localStorage.getItem('onlypages_visitor_id')
    if (!visitorId) {
      visitorId = Math.random().toString(36).substring(2) + Date.now().toString(36)
      localStorage.setItem('onlypages_visitor_id', visitorId)
    }

    // Track the view
    const trackView = async () => {
      try {
        await fetch('/api/onlypages/track-view', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            pageId,
            visitorId,
            referrer: document.referrer || null
          })
        })
      } catch (error) {
        console.error('Failed to track view:', error)
      }
    }

    // Track after a short delay to ensure the page has loaded
    const timeoutId = setTimeout(trackView, 1000)

    return () => clearTimeout(timeoutId)
  }, [pageId])

  return null
}