import { createSupabaseServerClient } from '@/lib/supabase/client'
import { notFound, redirect } from 'next/navigation'
import type { Metadata } from 'next'
import WaitlistButton from './WaitlistButton'
import HeaderToggle from './HeaderToggle'
import ViewTracker from './ViewTracker'
import HideMain<PERSON>avi<PERSON> from './OnlyPagesLogo'
import OnlyPagesTagsAndRelated from '@/components/OnlyPagesTagsAndRelated'
import TemplateRenderer from '@/components/OnlyPagesTemplateRenderer'
import { getTemplate } from '@/lib/onlypages/templates'

export const revalidate = 60 // cache lightly for public pages

async function getPage(slug: string) {
  const supabase = await createSupabaseServerClient()
  const { data: page } = await supabase
    .from('onlypages')
    .select('id, slug, status, article, theme_tokens, template_id, user_id, hero_image_url, categories, auto_tags')
    .eq('slug', slug)
    .single()
  if (!page || !['published', 'unlisted'].includes(page.status)) return null
  return page as any
}

async function getUser() {
  const supabase = await createSupabaseServerClient()
  const { data: { user } } = await supabase.auth.getUser()
  return user
}

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  const { slug } = await params
  const page = await getPage(slug)
  if (!page) return { title: 'OnlyPages' }
  const title = page.article?.title || 'OnlyPages'
  const description = page.article?.dek || 'Published on OnlyPages'
  return {
    title,
    description,
    openGraph: {
      title,
      description,
      url: `https://onlydiary.app/p/${slug}`
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description
    }
  }
}

export default async function OnlyPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params
  const [page, user] = await Promise.all([getPage(slug), getUser()])
  if (!page) notFound()

  const theme = page.theme_tokens || {}
  const accent = theme.accent || '#6b46c1'
  const bg = theme.bg || '#ffffff'
  const ink = theme.ink || '#111827'
  const muted = theme.muted || '#6b7280'
  const family = theme.family || 'serif'

  // Get template if available
  const template = page.template_id ? getTemplate(page.template_id) : null

  // Check if body already contains a waitlist button to avoid duplicates
  const bodyHtml = page.article?.body_html?.toLowerCase() || ''
  const hasWaitlistInBody =
    bodyHtml.includes('waitlist') ||
    bodyHtml.includes('join the waitlist') ||
    bodyHtml.includes('join waitlist') ||
    bodyHtml.includes('sign up to join') ||
    bodyHtml.includes('join now') ||
    bodyHtml.includes('get started') ||
    bodyHtml.includes('be notified') ||
    bodyHtml.includes('early access') ||
    bodyHtml.includes('first to know') ||
    bodyHtml.includes('stay updated') ||
    bodyHtml.includes('get updates') ||
    bodyHtml.includes('be the first') ||
    bodyHtml.includes('notify me') ||
    bodyHtml.includes('contact me') ||
    bodyHtml.includes('reach out') ||
    bodyHtml.includes('get in touch') ||
    // Check for button/action elements that might be CTAs
    (bodyHtml.includes('<button') && (bodyHtml.includes('join') || bodyHtml.includes('sign') || bodyHtml.includes('contact'))) ||
    (bodyHtml.includes('href=') && (bodyHtml.includes('mailto') || bodyHtml.includes('contact'))) ||
    // Check for common CTA patterns
    bodyHtml.includes('cta') ||
    bodyHtml.includes('call to action') ||
    // If there's already a CTA with action words, likely no need for additional waitlist
    (page.article?.cta_label && page.article?.cta_url)

  // If we have a template, use the new template renderer
  if (template && page.article) {
    return (
      <div>
        <HideMainNavigation />
        <ViewTracker pageId={page.id} />
        <HeaderToggle user={user} muted={muted} tags={page.article?.tags} />

        <TemplateRenderer
          template={template}
          article={page.article}
          theme={{ accent, bg, ink, muted, family }}
          heroImageUrl={page.hero_image_url}
        >
          {/* Call to action */}
          {page.article?.cta_label && page.article?.cta_url && (
            <div className="text-center mt-12 sm:mt-16 pt-8 sm:pt-12 border-t" style={{ borderColor: `${muted}20` }}>
              <a
                href={page.article.cta_url}
                className="inline-flex items-center px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-semibold rounded-lg transition-all duration-200 hover:transform hover:scale-105 shadow-lg hover:shadow-xl"
                style={{
                  background: `linear-gradient(135deg, ${accent}, ${accent}dd)`,
                  color: '#fff'
                }}
              >
                {page.article.cta_label}
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </a>
            </div>
          )}

          {/* Waitlist section with AI-generated CTA label */}
          {!hasWaitlistInBody && (page.article?.cta_label && !page.article?.cta_url) && (
            <div className="text-center mt-12 pt-8 sm:pt-12 border-t" style={{ borderColor: `${muted}20` }}>
              <WaitlistButton
                pageId={page.id}
                user={user}
                accent={accent}
                customLabel={page.article.cta_label}
              />
            </div>
          )}

          {/* Default waitlist section - only show if no CTA at all and no waitlist content in body */}
          {!hasWaitlistInBody && !page.article?.cta_label && (
            <div className="text-center mt-12 pt-8 sm:pt-12 border-t" style={{ borderColor: `${muted}20` }}>
              <WaitlistButton pageId={page.id} user={user} accent={accent} />
            </div>
          )}

          {/* Tags and Related Pages Section */}
          <div className="mt-16 pt-8 border-t" style={{ borderColor: `${muted}20` }}>
            <OnlyPagesTagsAndRelated
              pageId={page.id}
              categories={page.categories || []}
              tags={page.auto_tags || []}
              accent={accent}
            />
          </div>
        </TemplateRenderer>

        {/* Footer */}
        <footer className="border-t mt-12 sm:mt-20" style={{ borderColor: `${muted}20` }}>
          <div className="max-w-4xl mx-auto px-4 sm:px-6 py-6 sm:py-8 text-center">
            <p className="text-sm" style={{ color: muted }}>
              Published with <span style={{ color: accent }}>OnlyPages</span>
            </p>
          </div>
        </footer>
      </div>
    )
  }

  return (
    <div style={{ backgroundColor: bg, color: ink, minHeight: '100vh' }}>
      <HideMainNavigation />
      <ViewTracker pageId={page.id} />

      <HeaderToggle user={user} muted={muted} tags={page.article?.tags} />

      {/* Main content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 py-8 sm:py-12">
        {/* Article header */}
        <header className="text-center mb-12">
          <h1
            className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6"
            style={{
              color: ink,
              fontFamily: family === 'serif' ? 'Georgia, serif' : family === 'mono' ? 'Monaco, monospace' : '-apple-system, BlinkMacSystemFont, sans-serif'
            }}
          >
            {page.article?.title}
          </h1>
          {page.article?.dek && (
            <p
              className="text-lg sm:text-xl md:text-2xl leading-relaxed max-w-3xl mx-auto"
              style={{ color: muted }}
            >
              {page.article.dek}
            </p>
          )}
        </header>

        {/* Hero image */}
        {page.hero_image_url && (
          <div className="mb-8 sm:mb-12 flex justify-center">
            <div className="relative max-w-2xl w-full">
              <img
                src={page.hero_image_url}
                alt={page.article?.title || 'Hero image'}
                className="w-full h-auto object-contain rounded-lg shadow-lg"
                style={{
                  maxHeight: '500px',
                  objectFit: 'contain'
                }}
              />
            </div>
          </div>
        )}

        {/* Pull quote as featured element */}
        {page.article?.pull_quote && (
          <div className="max-w-2xl mx-auto mb-8 sm:mb-12">
            <blockquote
              className="text-xl sm:text-2xl md:text-3xl font-medium text-center py-6 sm:py-8 px-4 sm:px-6 rounded-lg border-l-4"
              style={{
                backgroundColor: `${accent}08`,
                borderLeftColor: accent,
                color: ink
              }}
            >
              "{page.article.pull_quote}"
            </blockquote>
          </div>
        )}

        {/* Article body */}
        <article
          className="prose prose-sm sm:prose-lg md:prose-xl max-w-none"
          style={{
            ['--tw-prose-body' as any]: ink,
            ['--tw-prose-headings' as any]: ink,
            ['--tw-prose-links' as any]: accent,
            ['--tw-prose-bold' as any]: ink,
            ['--tw-prose-code' as any]: ink,
            ['--tw-prose-pre-code' as any]: ink,
            ['--tw-prose-pre-bg' as any]: `${muted}10`,
            ['--tw-prose-quotes' as any]: accent,
            fontFamily: family === 'serif' ? 'Georgia, serif' : family === 'mono' ? 'Monaco, monospace' : '-apple-system, BlinkMacSystemFont, sans-serif'
          }}
        >
          <div dangerouslySetInnerHTML={{ __html: page.article?.body_html || '' }} />
        </article>

        {/* Call to action */}
        {page.article?.cta_label && page.article?.cta_url && (
          <div className="text-center mt-12 sm:mt-16 pt-8 sm:pt-12 border-t" style={{ borderColor: `${muted}20` }}>
            <a
              href={page.article.cta_url}
              className="inline-flex items-center px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-semibold rounded-lg transition-all duration-200 hover:transform hover:scale-105 shadow-lg hover:shadow-xl"
              style={{
                background: `linear-gradient(135deg, ${accent}, ${accent}dd)`,
                color: '#fff'
              }}
            >
              {page.article.cta_label}
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </a>
          </div>
        )}

        {/* Waitlist section with AI-generated CTA label */}
        {!hasWaitlistInBody && (page.article?.cta_label && !page.article?.cta_url) && (
          <div className="text-center mt-12 pt-8 sm:pt-12 border-t" style={{ borderColor: `${muted}20` }}>
            <WaitlistButton
              pageId={page.id}
              user={user}
              accent={accent}
              customLabel={page.article.cta_label}
            />
          </div>
        )}

        {/* Default waitlist section - only show if no CTA at all and no waitlist content in body */}
        {!hasWaitlistInBody && !page.article?.cta_label && (
          <div className="text-center mt-12 pt-8 sm:pt-12 border-t" style={{ borderColor: `${muted}20` }}>
            <WaitlistButton pageId={page.id} user={user} accent={accent} />
          </div>
        )}

        {/* Tags and Related Pages Section */}
        <div className="mt-16 pt-8 border-t" style={{ borderColor: `${muted}20` }}>
          <OnlyPagesTagsAndRelated
            pageId={page.id}
            categories={page.categories || []}
            tags={page.auto_tags || []}
            accent={accent}
          />
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t mt-12 sm:mt-20" style={{ borderColor: `${muted}20` }}>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 py-6 sm:py-8 text-center">
          <p className="text-sm" style={{ color: muted }}>
            Published with <span style={{ color: accent }}>OnlyPages</span>
          </p>
        </div>
      </footer>
    </div>
  )
}
