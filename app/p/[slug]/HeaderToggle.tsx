"use client"

import { useState } from "react"
import Link from "next/link"
import OnlyPagesSearch from "@/components/OnlyPagesSearch"

interface HeaderToggleProps {
  user: any
  muted: string
  tags?: string[]
}

export default function HeaderToggle({ user, muted, tags }: HeaderToggleProps) {
  const [isSearchOpen, setIsSearchOpen] = useState(false)

  return (
    <>
      {/* Brilliant OnlyPages navigation header - Mobile optimized */}
      <div className="fixed top-0 left-0 right-0 bg-white/98 backdrop-blur-md border-b border-gray-100 z-50 shadow-sm">
        <div className="max-w-7xl mx-auto px-3 sm:px-6">
          {/* Main header row */}
          <div className="flex items-center justify-between h-14 sm:h-16">
            {/* Left: Enhanced OnlyPages brand */}
            <Link href="/explore" className="flex items-center space-x-2 flex-shrink-0">
              <div className="flex items-center space-x-1.5">
                <div className="w-7 h-7 sm:w-8 sm:h-8 bg-gradient-to-br from-violet-500 to-purple-600 rounded-lg flex items-center justify-center shadow-sm">
                  <span className="text-white text-sm sm:text-base font-bold">📄</span>
                </div>
                <span className="text-lg sm:text-xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
                  OnlyPages
                </span>
              </div>
            </Link>

            {/* Desktop: Center search bar */}
            <div className="hidden md:flex flex-1 max-w-lg mx-6">
              <OnlyPagesSearch placeholder="Search OnlyPages..." />
            </div>

            {/* Right: Navigation */}
            <div className="flex items-center space-x-1 sm:space-x-3">
              {/* Mobile search toggle */}
              <button
                onClick={() => setIsSearchOpen(!isSearchOpen)}
                className="md:hidden p-2 text-gray-600 hover:text-violet-600 transition-colors rounded-lg hover:bg-gray-50"
                aria-label="Toggle search"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>

              {/* Navigation links - responsive */}
              <Link
                href="/explore"
                className="hidden sm:block text-sm font-medium text-gray-600 hover:text-violet-600 transition-colors px-2 py-1 rounded-md hover:bg-violet-50"
              >
                Explore
              </Link>
              <Link
                href="/explore/top-100"
                className="hidden lg:block text-sm font-medium text-gray-600 hover:text-violet-600 transition-colors px-2 py-1 rounded-md hover:bg-violet-50"
              >
                Top 100
              </Link>
              {user && (
                <Link
                  href="/dashboard"
                  className="text-xs sm:text-sm font-semibold bg-gradient-to-r from-violet-600 to-purple-600 text-white px-2.5 sm:px-4 py-1.5 sm:py-2 rounded-lg hover:from-violet-700 hover:to-purple-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105"
                >
                  Dashboard
                </Link>
              )}

              {/* Mobile explore with colorful binoculars */}
              <div className="sm:hidden">
                <Link
                  href="/explore"
                  className="relative p-2 bg-gradient-to-br from-blue-50 to-indigo-50 text-blue-600 hover:from-blue-100 hover:to-indigo-100 hover:text-blue-700 transition-all duration-200 rounded-lg border border-blue-200 hover:border-blue-300"
                  aria-label="Explore OnlyPages"
                >
                  <div className="relative">
                    {/* Colorful binoculars icon */}
                    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none">
                      {/* Left lens */}
                      <circle cx="8" cy="12" r="3" fill="#3B82F6" stroke="#1E40AF" strokeWidth="1.5"/>
                      {/* Right lens */}
                      <circle cx="16" cy="12" r="3" fill="#3B82F6" stroke="#1E40AF" strokeWidth="1.5"/>
                      {/* Bridge */}
                      <path d="M11 12h2" stroke="#6B7280" strokeWidth="2" strokeLinecap="round"/>
                      {/* Left strap */}
                      <path d="M5 10l2 1" stroke="#374151" strokeWidth="1.5" strokeLinecap="round"/>
                      {/* Right strap */}
                      <path d="M19 10l-2 1" stroke="#374151" strokeWidth="1.5" strokeLinecap="round"/>
                      {/* Lens reflections for extra shine */}
                      <circle cx="7" cy="10.5" r="0.8" fill="#60A5FA" opacity="0.6"/>
                      <circle cx="15" cy="10.5" r="0.8" fill="#60A5FA" opacity="0.6"/>
                    </svg>

                    {/* Subtle pulse animation */}
                    <div className="absolute inset-0 rounded-lg bg-blue-400 opacity-20 animate-pulse"></div>
                  </div>
                </Link>
              </div>
            </div>
          </div>

          {/* Mobile search bar - expandable */}
          {isSearchOpen && (
            <div className="md:hidden pb-3 border-t border-gray-100 mt-3 pt-3 animate-in slide-in-from-top-2 duration-200">
              <OnlyPagesSearch placeholder="Search OnlyPages..." />
              <div className="flex items-center justify-center space-x-4 mt-3">
                <Link
                  href="/explore"
                  className="text-sm font-medium text-gray-600 hover:text-violet-600 transition-colors px-3 py-1 rounded-md hover:bg-violet-50"
                  onClick={() => setIsSearchOpen(false)}
                >
                  Explore
                </Link>
                <Link
                  href="/explore/top-100"
                  className="text-sm font-medium text-gray-600 hover:text-violet-600 transition-colors px-3 py-1 rounded-md hover:bg-violet-50"
                  onClick={() => setIsSearchOpen(false)}
                >
                  Top 100
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Dynamic padding to account for fixed header */}
      <div className={`${isSearchOpen ? 'h-32' : 'h-14'} sm:h-16 transition-all duration-200`} />
    </>
  )
}
