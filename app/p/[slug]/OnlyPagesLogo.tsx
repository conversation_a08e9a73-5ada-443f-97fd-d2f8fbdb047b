'use client'

import { useEffect } from 'react'

export default function HideMainNavigation() {
  useEffect(() => {
    // Hide the main site navigation when on OnlyPages
    const hideMainNav = () => {
      // Look for common navigation selectors
      const navSelectors = [
        'header nav', // Header navigation
        'nav[class*="nav"]', // Navigation with nav class
        '[class*="navigation"]', // Navigation classes
        '[class*="header"]', // Header classes
        'div[class*="nav"]', // Div with nav class
        '.top-0.fixed', // Fixed top elements (likely main nav)
        '.fixed.top-0' // Alternative order
      ]

      for (const selector of navSelectors) {
        const navElements = document.querySelectorAll(selector)
        navElements.forEach((navElement) => {
          // Skip our OnlyPages header
          if (navElement.querySelector('[href="/explore"]')) return

          // Hide the main navigation
          if (!navElement.dataset.originalDisplay) {
            navElement.dataset.originalDisplay = getComputedStyle(navElement).display
          }
          navElement.style.display = 'none'
        })
      }

      // Also look for common navigation text patterns and hide their containers
      const textPatterns = ['Books', 'Library', 'Dashboard', 'Create', 'Timeline', 'Messages']
      textPatterns.forEach(pattern => {
        const elements = Array.from(document.querySelectorAll('*')).filter(el =>
          el.textContent && el.textContent.includes(pattern) &&
          !el.querySelector('[href="/explore"]') // Don't hide our OnlyPages nav
        )

        elements.forEach(el => {
          // Find the closest navigation container
          let navContainer = el.closest('nav, header, [class*="nav"], [class*="header"]')
          if (navContainer && !navContainer.querySelector('[href="/explore"]')) {
            if (!navContainer.dataset.originalDisplay) {
              navContainer.dataset.originalDisplay = getComputedStyle(navContainer).display
            }
            navContainer.style.display = 'none'
          }
        })
      })
    }

    // Hide immediately
    hideMainNav()

    // Hide after a short delay in case the DOM isn't fully loaded
    const timeoutId = setTimeout(hideMainNav, 100)
    const timeoutId2 = setTimeout(hideMainNav, 500)

    // Cleanup function to restore navigation when leaving OnlyPages
    return () => {
      clearTimeout(timeoutId)
      clearTimeout(timeoutId2)

      // Restore all hidden navigation elements
      const allElements = document.querySelectorAll('*')
      allElements.forEach(element => {
        if (element.dataset.originalDisplay) {
          element.style.display = element.dataset.originalDisplay
          delete element.dataset.originalDisplay
        }
      })
    }
  }, [])

  return null // This component doesn't render anything visible
}