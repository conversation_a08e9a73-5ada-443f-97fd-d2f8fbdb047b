"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"

interface WaitlistButtonProps {
  pageId: string
  user: any
  accent: string
}

export default function WaitlistButton({ pageId, user, accent }: WaitlistButtonProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [joined, setJoined] = useState(false)
  const [checking, setChecking] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Check if user is already on waitlist when component loads
  useEffect(() => {
    const checkWaitlistStatus = async () => {
      if (!user) {
        console.log('No user, skipping waitlist check')
        setChecking(false)
        return
      }

      try {
        console.log('Checking waitlist status for page:', pageId)
        const response = await fetch(`/api/onlypages/waitlist?page_id=${pageId}`)
        const data = await response.json()
        console.log('Waitlist check response:', { status: response.status, data })

        if (response.ok) {
          setJoined(data.on_waitlist)
        } else {
          console.error('Waitlist check failed:', data)
        }
      } catch (err) {
        console.error('Failed to check waitlist status:', err)
      } finally {
        setChecking(false)
      }
    }

    checkWaitlistStatus()
  }, [user, pageId])

  const handleJoinWaitlist = async () => {
    if (!user) {
      // Redirect to sign up page if not authenticated
      router.push('/register?redirect=' + encodeURIComponent(window.location.pathname))
      return
    }

    try {
      setLoading(true)
      setError(null)

      console.log('Attempting to join waitlist for page:', pageId)

      const response = await fetch('/api/onlypages/waitlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ page_id: pageId }),
      })

      const data = await response.json()
      console.log('Waitlist API response:', { status: response.status, data })

      if (!response.ok) {
        throw new Error(data.error || 'Failed to join waitlist')
      }

      setJoined(true)
    } catch (err: any) {
      console.error('Waitlist join error:', err)
      setError(err.message || 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  if (checking) {
    return (
      <div className="p-4 sm:p-6 rounded-lg border-2 border-dashed" style={{ borderColor: `${accent}40` }}>
        <div className="flex items-center justify-center">
          <svg className="animate-spin h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
        </div>
      </div>
    )
  }

  if (joined) {
    return (
      <div className="p-4 sm:p-6 rounded-lg border-2 border-dashed" style={{ borderColor: `${accent}40` }}>
        <div className="flex items-center justify-center gap-2 text-green-600">
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          <span className="font-medium text-sm sm:text-base">You're on the waitlist!</span>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 sm:p-6 rounded-lg border-2 border-dashed" style={{ borderColor: `${accent}40` }}>
      {error && (
        <div className="mb-3 sm:mb-4 p-2 bg-red-50 border border-red-200 text-red-600 text-xs sm:text-sm rounded text-center">
          {error}
        </div>
      )}

      <button
        onClick={handleJoinWaitlist}
        disabled={loading}
        className="w-full py-3 sm:py-4 px-6 sm:px-8 rounded-lg font-medium transition-all duration-200 hover:transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base"
        style={{
          background: `linear-gradient(135deg, ${accent}, ${accent}dd)`,
          color: '#fff',
          boxShadow: '0 4px 14px 0 rgba(0, 0, 0, 0.1)'
        }}
      >
        {loading ? (
          <span className="flex items-center justify-center">
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
            </svg>
            Processing...
          </span>
        ) : user ? (
          'Join the Waitlist'
        ) : (
          'Sign Up for the Waitlist'
        )}
      </button>
    </div>
  )
}
