'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { WORD_PACKS } from '@/lib/genyus/config'
import { useNavigation } from '@/contexts/NavigationContext'

import { GroupSelector } from '@/components/genyus/GroupSelector'
import { GroupCreateModal } from '@/components/genyus/GroupCreateModal'
import { PersonalHistoryModal } from '@/components/genyus/PersonalHistoryModal'
import { useGroupChatRealTime } from '@/lib/genyus/real-time'
import { ConversationIntelligenceIndicator, useConversationIntelligence, ConversationStatus } from '@/components/genyus/ConversationIntelligenceIndicator'
import { MentionInput } from '@/components/genyus/MentionInput'
import { SocialMedia2OnboardingModal } from '@/components/onboarding/SocialMedia2OnboardingModal'
import { useSocialMedia2Onboarding, emitGroupChatMessage } from '@/hooks/useSocialMedia2Onboarding'
import { GroupInviteStatusTracker } from '@/components/genyus/GroupInviteStatusTracker'

// Simple markdown renderer
function renderMarkdown(text: string): string {
  return text
    // Headers
    .replace(/^### (.*$)/gm, '<h3 class="text-lg font-semibold mt-6 mb-3">$1</h3>')
    .replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold mt-6 mb-4">$1</h2>')
    .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mt-6 mb-4">$1</h1>')
    // Bold
    .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
    // Italic
    .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
    // Code blocks
    .replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-100 rounded p-3 my-4 overflow-x-auto"><code>$1</code></pre>')
    // Inline code
    .replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm">$1</code>')
    // Lists
    .replace(/^- (.*$)/gm, '<li class="ml-4">• $1</li>')
    .replace(/^(\d+)\. (.*$)/gm, '<li class="ml-4">$1. $2</li>')
    // Line breaks
    .replace(/\n\n/g, '</p><p class="mb-4">')
    .replace(/\n/g, '<br>')
    // Wrap in paragraphs
    .replace(/^(.+)/, '<p class="mb-4">$1')
    .replace(/(.+)$/, '$1</p>')
}

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  isStreaming?: boolean
}

export default function GenyusPage() {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [messages, setMessages] = useState<Message[]>([])

  // Debug message state changes
  useEffect(() => {
    console.log('📊 Messages state changed:', messages.length, 'messages')
  }, [messages])
  const [inputValue, setInputValue] = useState('')
  const [isAsking, setIsAsking] = useState(false)
  const [wordsRemaining, setWordsRemaining] = useState<number | null>(null)
  const [needsUpgrade, setNeedsUpgrade] = useState(false)
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)
  const [purchasing, setPurchasing] = useState(false)
  const [sharingMessageId, setSharingMessageId] = useState<string | null>(null)
  const [showPersonalHistory, setShowPersonalHistory] = useState(false)

  // Model selection state
  const [selectedModel, setSelectedModel] = useState<'haiku3' | 'deepseek' | 'gemini'>('gemini')

  // Group chat state - DISABLED (always null)
  const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null)
  const [groupMembers, setGroupMembers] = useState<any[]>([])
  const [currentGroupName, setCurrentGroupName] = useState<string>('')
  const [showInviteTracker, setShowInviteTracker] = useState(false)
  const [currentGroupInviteCode, setCurrentGroupInviteCode] = useState<string>('')
  const [showGroupChatOption, setShowGroupChatOption] = useState(true)
  const [showChatHistoryPanel, setShowChatHistoryPanel] = useState(false)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [isLoadingGroup, setIsLoadingGroup] = useState(false)

  // Handle group selection with member loading - DISABLED (always personal chat)
  const handleGroupSelect = useCallback(async (groupId: string | null) => {
    // Group chat disabled - always force personal chat
    setSelectedGroupId(null)
    setMessages([]) // Clear messages when switching
    setShowGroupChatOption(true) // Show group chat option for new conversation

    if (groupId) {
      setIsLoadingGroup(true)
      try {
        // Fetch group details and members
        console.log('Loading group details for:', groupId)
        const response = await fetch(`/api/genyus/group/${groupId}`)
        if (response.ok) {
          const data = await response.json()
          console.log('Group data received:', data)

          setCurrentGroupName(data.group.name)
          setCurrentGroupInviteCode(data.group.inviteCode || '')

          const formattedMembers = data.group.members.map((member: any) => {
            console.log('Processing member:', member)
            console.log('Member properties:', {
              userId: member.userId,
              name: member.name,
              profilePictureUrl: member.profilePictureUrl,
              allKeys: Object.keys(member)
            })
            return {
              id: member.userId, // API returns userId, not id
              name: member.name,
              email: member.email || '',
              avatar: member.profilePictureUrl || null
            }
          })

          console.log('Setting group members:', formattedMembers)
          setGroupMembers(formattedMembers)

          // Hide invite tracker if group has multiple members
          if (data.group.members.length > 1) {
            setShowInviteTracker(false)
          }
        } else if (response.status === 404 || response.status === 403) {
          // Group doesn't exist or user is not a member - clear selection
          console.log('Group not found or access denied, clearing selection')
          setSelectedGroupId(null)
          setCurrentGroupName('')
          setCurrentGroupInviteCode('')
          setGroupMembers([])
          setShowInviteTracker(false)

          // Update URL to remove group parameter
          const url = new URL(window.location.href)
          url.searchParams.delete('group')
          window.history.replaceState({}, '', url.toString())
        }
      } catch (error) {
        console.error('Error loading group details:', error)
      } finally {
        setIsLoadingGroup(false)
      }
    } else {
      setGroupMembers([])
      setCurrentGroupName('')
      setIsLoadingGroup(false)
    }
  }, [isLoadingGroup])

  const inputRef = useRef<HTMLTextAreaElement>(null)
  const router = useRouter()
  const searchParams = typeof window !== 'undefined' ? new URLSearchParams(window.location.search) : null
  const supabase = createSupabaseClient()
  const { hideNavigation, showNavigation } = useNavigation()


  // Real-time messaging for group chats
  const { start: startRealTime, stop: stopRealTime, updateLastMessage, checkForNewMessages } = useGroupChatRealTime(
    selectedGroupId,
    user?.id || '',
    (newMessage) => {
      setMessages(prev => {
        // Replace streaming or temp assistant with real-time assistant
        if (newMessage.type === 'assistant') {
          const streamingIdx = prev.findIndex(m => m.type === 'assistant' && m.isStreaming)
          if (streamingIdx !== -1) {
            const next = prev.slice()
            next[streamingIdx] = { ...newMessage, isStreaming: false }
            return next
          }
          // Replace last assistant with temp numeric id
          const lastAssistantIdx = [...prev].map((m, i) => ({ m, i })).reverse().find(({ m }) => m.type === 'assistant')?.i
          if (lastAssistantIdx != null) {
            const last = prev[lastAssistantIdx]
            const looksTempId = typeof last.id === 'string' && /^[0-9]+$/.test(last.id)
            if (looksTempId) {
              const next = prev.slice()
              next[lastAssistantIdx] = { ...newMessage, isStreaming: false }
              return next
            }
          }
        }
        // Dedupe by id
        if (prev.some(m => m.id === newMessage.id)) return prev
        return [...prev, newMessage]
      })
      // Only refresh members if this is the first message from a new user
      const isNewUser = !groupMembers.some(member => (member.id ?? (member as any).userId) === newMessage.user.id)
      if (isNewUser && selectedGroupId) {
        console.log('New user detected, refreshing members')
        refreshGroupMembers()
      }

      // Auto-scroll to new message
      setTimeout(() => {
        const messagesContainer = document.querySelector('.messages-container')
        if (messagesContainer) {
          messagesContainer.scrollTop = messagesContainer.scrollHeight
        }
      }, 100)
    },
    (error) => {
      console.error('Real-time error:', error)
    }
  )

  // Conversation intelligence
  const { isWaiting, waitReason, estimatedTime, showWaiting, hideWaiting } = useConversationIntelligence()

  // Social Media 2.0 Onboarding
  const onboarding = useSocialMedia2Onboarding(user?.id)

  // Initialize clean personal chat mode
  useEffect(() => {
    setSelectedGroupId(null)
    // Don't clear messages on mount - let conversations persist
  }, [])

  // Generate intelligent waiting messages
  const getWaitingMessage = (strategy: any) => {
    if (strategy.responseType === 'wait_for_specific_user') {
      const targetUser = groupMembers.find(m => m.id === strategy.waitingFor)
      return `I'd love to hear ${targetUser?.name || 'someone'}'s thoughts on this before I respond. ${targetUser?.name || 'They'} might have valuable insights to share.`
    }

    if (strategy.responseType === 'wait_for_any_user') {
      return "I'm curious to hear what others think about this. Let me give everyone a chance to share their perspective."
    }

    if (strategy.needsSpecificAnswer) {
      return "This seems like something that would benefit from personal experience. I'll wait to see what others have to share."
    }

    return "Let me give everyone a moment to contribute to this discussion."
  }

  // Render message content with @ mention highlighting
  const renderMessageContent = (content: string, isMarkdown: boolean = false) => {
    if (!selectedGroupId || groupMembers.length === 0) {
      // No group context, render normally
      return isMarkdown ? (
        <div dangerouslySetInnerHTML={{ __html: renderMarkdown(content) }} />
      ) : (
        <div className="whitespace-pre-wrap break-words overflow-wrap-anywhere">{content}</div>
      )
    }

    // Parse @ mentions in group context
    const mentionPattern = /@(\w+(?:\s+\w+)*)/g
    const parts = []
    let lastIndex = 0
    let match

    while ((match = mentionPattern.exec(content)) !== null) {
      // Add text before mention
      if (match.index > lastIndex) {
        parts.push(content.substring(lastIndex, match.index))
      }

      // Find mentioned user
      const mentionText = match[1]
      const mentionedUser = groupMembers.find(member =>
        member.name.toLowerCase().includes(mentionText.toLowerCase()) ||
        member.name.toLowerCase() === mentionText.toLowerCase()
      )

      if (mentionedUser) {
        parts.push(
          <span
            key={`mention-${match.index}`}
            className="bg-blue-100 text-blue-800 px-1 rounded font-medium"
          >
            @{mentionedUser.name}
          </span>
        )
      } else {
        // Unknown mention, render as-is
        parts.push(match[0])
      }

      lastIndex = match.index + match[0].length
    }

    // Add remaining text
    if (lastIndex < content.length) {
      parts.push(content.substring(lastIndex))
    }

    return isMarkdown ? (
      <div dangerouslySetInnerHTML={{ __html: renderMarkdown(parts.join('')) }} />
    ) : (
      <div className="whitespace-pre-wrap break-words overflow-wrap-anywhere">{parts}</div>
    )
  }

  // Word pack configuration from config
  const wordPacksArray = [
    { id: 'unlimited', ...WORD_PACKS.UNLIMITED },
  ]

  useEffect(() => {
    // Hide navigation when entering OnlyGenyus
    hideNavigation()

    // Prevent body scroll and bounce on mobile (lighter approach)
    const originalStyle = {
      overflow: document.body.style.overflow,
      overscrollBehavior: document.body.style.overscrollBehavior
    }

    // Don't set overflow hidden - it breaks scrolling in the messages container
    document.body.style.overscrollBehavior = 'none'

    const checkAuth = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/login?next=' + encodeURIComponent('/genyus'))
        return
      }
      setUser(user)
      await loadWordBalance(user.id)

      // Check for group parameter in URL - DISABLED (clear any group params)
      const groupParam = searchParams?.get('group')
      if (groupParam) {
        // Group chat disabled - clear any group parameters from URL
        console.log('🚫 Group chat disabled - clearing group parameter from URL')
        const url = new URL(window.location.href)
        url.searchParams.delete('group')
        window.history.replaceState({}, '', url.toString())
      }
      if (false && groupParam) {
        // Validate group exists before selecting
        try {
          const response = await fetch(`/api/genyus/group/${groupParam}`)
          if (response.ok) {
            await handleGroupSelect(groupParam)
          } else {
            // Group doesn't exist, clear the URL parameter
            console.log('Group from URL parameter not found, clearing')
            const url = new URL(window.location.href)
            url.searchParams.delete('group')
            window.history.replaceState({}, '', url.toString())
          }
        } catch (error) {
          console.error('Error validating group from URL:', error)
          // Clear invalid group parameter
          const url = new URL(window.location.href)
          url.searchParams.delete('group')
          window.history.replaceState({}, '', url.toString())
        }
      }

      setLoading(false)
    }
    checkAuth()

    // Restore original styles and show navigation when leaving OnlyGenyus
    return () => {
      document.body.style.overflow = originalStyle.overflow
      document.body.style.overscrollBehavior = originalStyle.overscrollBehavior
      showNavigation()
    }
  }, [router, supabase, hideNavigation, showNavigation])

  // Group chat is disabled - removed group selection logic that was clearing messages

  // Auto-recovery: Check if messages disappeared and auto-refresh
  useEffect(() => {
    if (!selectedGroupId || !user) return

    // Set up a timer to check for missing messages (less aggressive)
    const checkInterval = setInterval(async () => {
      // If we're in a group chat but have no messages, and we're not currently loading, and not asking
      if (selectedGroupId && messages.length === 0 && !isLoadingGroup && !isAsking) {
        console.log('Auto-recovery: No messages detected in group chat, refreshing...')

        // Manually reload messages
        try {
          const response = await fetch(`/api/genyus/group/${selectedGroupId}`)
          if (response.ok) {
            const data = await response.json()
            const groupMessages = data.group.messages.map((msg: any) => ({
              id: msg.id,
              type: msg.type,
              content: msg.content,
              timestamp: new Date(msg.timestamp),
              user: msg.user
            }))
            setMessages(groupMessages)
            console.log('Auto-recovery: Loaded', groupMessages.length, 'messages')
          }
        } catch (error) {
          console.error('Auto-recovery failed:', error)
        }
      }
    }, 60000) // Check every 60 seconds (less aggressive)

    return () => clearInterval(checkInterval)
  }, [selectedGroupId, messages.length, user, isLoadingGroup])

  // Refresh group members on specific actions only (not constant polling)
  const refreshGroupMembers = useCallback(async () => {
    if (!selectedGroupId || isLoadingGroup) return
    try {
      console.log('Refreshing group members for:', selectedGroupId)
      const res = await fetch(`/api/genyus/group/${selectedGroupId}`)
      if (res.ok) {
        const data = await res.json()
        const formatted = (data.group.members || []).map((member: any) => ({
          id: member.userId,
          name: member.name,
          email: member.email || '',
          avatar: member.profilePictureUrl || null
        }))
        setGroupMembers(formatted)
        setCurrentGroupName(data.group.name)
        setCurrentGroupInviteCode(data.group.inviteCode || '')
      }
    } catch (e) {
      console.error('Member refresh failed:', e)
    }
  }, [selectedGroupId, isLoadingGroup])

  // Removed auto-scroll - let user control scrolling manually

  const loadWordBalance = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('genyus_user_words')
        .select('words_remaining')
        .eq('user_id', userId)
        .single()
      
      if (error && error.code !== 'PGRST116') {
        console.error('Error loading word balance:', error)
        return
      }
      
      if (data) {
        setWordsRemaining(data.words_remaining)
      } else {
        // Initialize user with first month generous allowance
        const { data: newBalance, error: insertError } = await supabase
          .from('genyus_user_words')
          .insert({
            user_id: userId,
            words_remaining: WORD_PACKS.FREE_FIRST_MONTH,
            tier: 'free',
            is_first_month: true
          })
          .select('words_remaining')
          .single()
        
        if (!insertError && newBalance) {
          setWordsRemaining(newBalance.words_remaining)
        }
      }
    } catch (error) {
      console.error('Error with word balance:', error)
    }
  }



  const handlePurchase = async (priceId: string) => {
    setPurchasing(true)
    try {
      const response = await fetch('/api/words/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
        },
        body: JSON.stringify({ priceId })
      })

      const data = await response.json()
      if (response.ok && data.url) {
        window.location.href = data.url
      } else {
        console.error('Checkout error:', data.error)
        alert('Failed to create checkout session. Please try again.')
      }
    } catch (error) {
      console.error('Purchase error:', error)
      alert('Failed to initiate purchase. Please try again.')
    } finally {
      setPurchasing(false)
    }
  }

  const loadGroupMessages = async () => {
    if (!selectedGroupId) return

    try {
      const response = await fetch(`/api/genyus/group/${selectedGroupId}`)
      if (response.ok) {
        const data = await response.json()
        const groupMessages = data.group.messages.map((msg: any) => ({
          id: msg.id,
          type: msg.type,
          content: msg.content,
          timestamp: new Date(msg.timestamp),
          user: msg.user
        }))
        console.log('📥 Loading group messages:', groupMessages.length, 'messages')
        setMessages(groupMessages)
        const formattedMembers = data.group.members.map((member: any) => ({
          id: member.userId,
          name: member.name,
          email: member.email || '',
          avatar: member.profilePictureUrl || null
        }))
        setGroupMembers(formattedMembers)

        // Update real-time polling with latest message timestamp
        if (groupMessages.length > 0) {
          const lastMessage = groupMessages[groupMessages.length - 1]
          updateLastMessage(lastMessage.timestamp.toISOString())
        }

        // Auto-scroll to bottom after loading messages
        setTimeout(() => {
          const messagesContainer = document.querySelector('.messages-container')
          if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight
          }
        }, 100)
      }
    } catch (error) {
      console.error('Failed to load group messages:', error)
    }
  }

  const handleShareToTimeline = async (messageId: string) => {
    setSharingMessageId(messageId)

    try {
      // Find the question and answer pair
      const messageIndex = messages.findIndex(m => m.id === messageId)
      if (messageIndex === -1 || messageIndex === 0) return

      const answerMessage = messages[messageIndex]
      const questionMessage = messages[messageIndex - 1]

      if (answerMessage.type !== 'assistant' || questionMessage.type !== 'user') return

      const response = await fetch('/api/genyus/share', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: questionMessage.content,
          answer: answerMessage.content
        })
      })

      const data = await response.json()

      if (response.ok) {
        // Show success message with better styling
        const successMessage = document.createElement('div')
        successMessage.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-xl shadow-lg z-50 font-medium'
        successMessage.textContent = '✨ Shared to your timeline!'
        document.body.appendChild(successMessage)

        setTimeout(() => {
          successMessage.remove()
        }, 3000)
      } else {
        throw new Error(data.error || 'Failed to share')
      }
    } catch (error) {
      console.error('Share error:', error)
      // Show error message
      const errorMessage = document.createElement('div')
      errorMessage.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-xl shadow-lg z-50 font-medium'
      errorMessage.textContent = 'Failed to share. Please try again.'
      document.body.appendChild(errorMessage)

      setTimeout(() => {
        errorMessage.remove()
      }, 3000)
    } finally {
      setSharingMessageId(null)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputValue.trim() || isAsking || !user) return

    const question = inputValue.trim()

    // Prevent double submission by clearing input and setting state immediately
    setInputValue('')
    setIsAsking(true)

    console.log('🚀 Submitting message:', question.substring(0, 50) + '...')

    // Add a small delay to prevent rapid double-clicks
    await new Promise(resolve => setTimeout(resolve, 100))

    // Hide group chat option once user starts chatting
    setShowGroupChatOption(false)

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: question,
      timestamp: new Date()
    }
    setMessages(prev => [...prev, userMessage])

    // Group chat disabled - no group activity emission needed

    // Add streaming assistant message
    const assistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      isStreaming: true
    }
    setMessages(prev => [...prev, assistantMessage])

    try {
      // Group chat disabled - always use personal chat
      const endpoint = '/api/genyus'
      const requestBody = {
        question,
        model: selectedModel // Pass user's model choice
      }

      console.log('Sending message:', { endpoint, requestBody })

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      })

      console.log('Response status:', response.status)
      console.log('Response headers:', Object.fromEntries(response.headers.entries()))
      console.log('Response body type:', typeof response.body)

      // Reconcile local user message id with server id for deduplication
      const serverMessageId = response.headers.get('X-Message-Id')
      if (serverMessageId) {
        setMessages(prev => {
          const hasServerAlready = prev.some(m => m.id === serverMessageId)
          if (hasServerAlready) {
            // Real-time arrived first; drop the local temp
            return prev.filter(m => m.id !== userMessage.id)
          }
          return prev.map(m => m.id === userMessage.id ? { ...m, id: serverMessageId } : m)
        })
      }

      if (!response.ok) {
        const errorData = await response.json()
        console.log('Error response:', errorData)
        if (response.status === 402) {
          setNeedsUpgrade(true)
          setShowPurchaseModal(true) // Show upgrade modal immediately
          setMessages(prev => prev.slice(0, -1)) // Remove streaming message
          return
        }



        throw new Error(errorData.error || 'Failed to get response')
      }

      // Skip intelligent waiting check for now - focus on streaming
      // TODO: Re-implement intelligent waiting without interfering with streaming

      // SSE streaming reader (mobile-safe, no fallback)
      console.log('🔄 Starting SSE streaming')

      if (!response.body) {
        throw new Error('No response body')
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder('utf-8', { fatal: false })
      let buffer = ''
      let result = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value, { stream: true })
          buffer += chunk

          // Process complete SSE events (separated by \n\n)
          let sepIndex: number
          while ((sepIndex = buffer.indexOf('\n\n')) !== -1) {
            const eventBlock = buffer.slice(0, sepIndex)
            buffer = buffer.slice(sepIndex + 2)

            const lines = eventBlock.split('\n')
            let eventName: string | null = null
            const dataLines: string[] = []

            for (const line of lines) {
              if (line.startsWith('event:')) {
                eventName = line.slice(6).trim()
              } else if (line.startsWith('data:')) {
                // Preserve spaces from the model output; remove only the single spec-defined space after 'data:'
                let payload = line.slice(5)
                if (payload.startsWith(' ')) payload = payload.slice(1)
                dataLines.push(payload)
              }
            }

            const dataPayload = dataLines.join('\n')

            if (eventName === 'end') {
              // End-of-stream signal from server
              buffer = ''
              break
            } else if (eventName === 'error') {
              console.error('SSE error event:', dataPayload)
              throw new Error(dataPayload || 'SSE error')
            } else {
              // Default data event
              if (dataPayload) {
                result += dataPayload
                setMessages(prev => prev.map(msg =>
                  msg.id === assistantMessage.id
                    ? { ...msg, content: result }
                    : msg
                ))
              }
            }
          }
        }

        // Flush any trailing data lines if buffer remains without terminator
        if (buffer.trim().length > 0) {
          const trailing = buffer
            .split('\n')
            .filter(l => l.startsWith('data:'))
            .map(l => {
              let payload = l.slice(5)
              if (payload.startsWith(' ')) payload = payload.slice(1)
              return payload
            })
            .join('\n')
          if (trailing) {
            result += trailing
            setMessages(prev => prev.map(msg =>
              msg.id === assistantMessage.id
                ? { ...msg, content: result }
                : msg
            ))
          }
        }

        console.log('✅ SSE STREAM COMPLETE:', result.length, 'chars')
      } catch (error) {
        console.error('❌ SSE STREAMING ERROR:', error)
        throw error
      }

      // Mark as complete
      setMessages(prev => prev.map(msg => 
        msg.id === assistantMessage.id 
          ? { ...msg, isStreaming: false }
          : msg
      ))

      // Reload word balance
      await loadWordBalance(user.id)

      // Real-time updates will handle new messages automatically

    } catch (error) {
      console.error('Error asking Genyus:', error)
      setMessages(prev => prev.slice(0, -1)) // Remove streaming message
      setMessages(prev => [...prev, {
        id: (Date.now() + 2).toString(),
        type: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date()
      }])
    } finally {
      setIsAsking(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e as any)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading...</div>
      </div>
    )
  }

  if (!user) return null

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Clean Header */}
      <div className="flex-shrink-0 bg-white border-b border-gray-100 px-4 sm:px-6 py-3">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          {/* Back Button */}
          <Link
            href="/write"
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span className="text-sm font-medium hidden sm:inline">Back</span>
          </Link>

          {/* Center: OnlyGenyus Branding */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h1 className="text-lg font-semibold text-gray-900">OnlyGenyus</h1>
          </div>

          {/* Right: Word Count */}
          {wordsRemaining !== null && (
            <div className="text-right">
              <div className="text-sm font-medium text-gray-700">
                {wordsRemaining === -1 ? (
                  <span className="text-green-600">Unlimited</span>
                ) : wordsRemaining < 50 ? (
                  <span className="text-red-600">Upgrade Required</span>
                ) : (
                  <span>{wordsRemaining.toLocaleString()} words</span>
                )}
              </div>
              {wordsRemaining !== -1 && wordsRemaining < 50 && (
                <button
                  onClick={() => setShowPurchaseModal(true)}
                  className="text-xs text-red-600 hover:text-red-700 mt-0.5 font-medium"
                >
                  Upgrade
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Group Selector - DISABLED */}
      {false && (
        <GroupSelector
          selectedGroupId={selectedGroupId}
          onGroupSelect={handleGroupSelect}
          currentGroupName={currentGroupName}
          onGroupCreated={(group) => {
            // Auto-select the new group and load its details
            handleGroupSelect(group.id)
          }}
        />
      )}

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col max-w-4xl mx-auto w-full bg-white rounded-t-xl shadow-sm">
        {/* Group Members Indicator - DISABLED */}
        {false && selectedGroupId && groupMembers.length > 0 && (
          <div className="px-4 sm:px-6 py-2 border-b border-gray-100 bg-gray-50">
            <div className="max-w-3xl mx-auto flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-xs text-gray-600">In this chat:</span>
                <div className="flex items-center space-x-1">
                  {groupMembers.map((member, index) => (
                    <div key={member.id || member.userId || `member-${index}`} className="flex items-center">
                      <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                        {member.avatar && member.avatar !== 'null' ? (
                          <img
                            src={member.avatar}
                            alt={member.name}
                            className="w-6 h-6 rounded-full object-cover"
                            onError={(e) => {
                              // Hide image on error and show initials instead
                              e.currentTarget.style.display = 'none'
                            }}
                          />
                        ) : null}
                        {(!member.avatar || member.avatar === 'null') && (
                          <span className="text-xs font-medium text-gray-600">
                            {member.name?.charAt(0)?.toUpperCase() || '?'}
                          </span>
                        )}
                      </div>
                      <span className="ml-1 text-xs text-gray-700">{member.name}</span>
                      {index < groupMembers.length - 1 && <span className="ml-2 text-gray-400">•</span>}
                    </div>
                  ))}
                </div>
              </div>

              {/* Waiting for friend indicator */}
              {groupMembers.length === 1 && (
                <div className="flex items-center space-x-2 text-xs text-blue-600">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span>Waiting for your friend to join...</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Messages Container - SOLID SCROLLABLE AREA */}
        <div
          className="messages-container flex-1 overflow-y-auto px-4 sm:px-6 py-6"
          style={{
            scrollBehavior: 'smooth',
            overscrollBehavior: 'contain'
          }}
        >
          {(() => {
            if (messages.length === 0) {
              console.log('🚨 Showing empty state - messages.length:', messages.length, 'selectedGroupId:', selectedGroupId, 'isLoadingGroup:', isLoadingGroup)
              return !isLoadingGroup
            }
            return false
          })() ? (
            /* Empty State */
            <div className="flex flex-col items-center justify-center h-full min-h-[400px] text-center space-y-6 px-4">
              <div className="w-20 h-20 bg-gradient-to-br from-purple-600 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div className="space-y-2">
                <h2 className="text-2xl font-semibold text-gray-900">
                  Chat with AI
                </h2>
                <p className="text-gray-600 text-base max-w-sm">
                  AI that gets smarter with every chat.
                </p>
              </div>

              {/* Refresh Button for Group Chats */}
              {selectedGroupId && (
                <div className="mt-6">
                  <button
                    onClick={async () => {
                      console.log('Manual refresh requested for group:', selectedGroupId)
                      await loadGroupMessages()
                      // Force scroll to bottom after refresh
                      setTimeout(() => {
                        const messagesContainer = document.querySelector('.messages-container')
                        if (messagesContainer) {
                          messagesContainer.scrollTop = messagesContainer.scrollHeight
                          console.log('Forced scroll to bottom after refresh')
                        }
                      }, 200)
                    }}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Refresh Messages
                  </button>
                  <p className="text-sm text-gray-500 mt-2">
                    Messages not loading? Click to refresh the conversation.
                  </p>
                </div>
              )}

              {selectedGroupId && groupMembers.length > 0 && (
                <div className="mt-4 flex items-center justify-center space-x-2">
                  <span className="text-xs text-gray-400">Members:</span>
                  {groupMembers.slice(0, 5).map((member, index) => (
                    <div key={member.id || member.userId || `empty-member-${index}`} className="flex items-center">
                      <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-xs font-medium text-gray-600">
                          {member.name?.charAt(0)?.toUpperCase() || '?'}
                        </span>
                      </div>
                      {index < Math.min(groupMembers.length - 1, 4) && (
                        <span className="text-xs text-gray-300 ml-1">,</span>
                      )}
                    </div>
                  ))}
                  {groupMembers.length > 5 && (
                    <span className="text-xs text-gray-400">+{groupMembers.length - 5} more</span>
                  )}
                </div>
              )}

              {/* Invite Status Tracker - DISABLED */}
              {false && selectedGroupId && showInviteTracker && currentGroupInviteCode && messages.length === 0 && (
                <div className="max-w-md mx-auto">
                  <GroupInviteStatusTracker
                    groupId={selectedGroupId}
                    inviteCode={currentGroupInviteCode}
                    onUserJoined={() => {
                      setShowInviteTracker(false)
                      // Refresh group members
                      handleGroupSelect(selectedGroupId)
                    }}
                  />
                </div>
              )}
            </div>
          ) : (
            /* Messages */
            <div className="space-y-8 max-w-3xl mx-auto">
              {/* Welcome Message for Empty State */}
              {messages.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-3">Welcome to OnlyGenyus</h2>
                  <p className="text-gray-600 mb-6 max-w-md mx-auto">
                    Your AI companion with <span className="font-semibold text-blue-600">Invisible Intelligence</span> —
                    an AI that adapts to your unique style and grows with you over time.
                  </p>
                  <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      <span>Learns your patterns</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                      <span>Adapts personality</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      <span>Never mentions memory</span>
                    </div>
                  </div>
                </div>
              )}

              {messages.map((message, index) => (
                <div
                  key={message.id}
                  className="group animate-in slide-in-from-bottom-4 duration-300"
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  {message.type === 'user' ? (
                    /* User Message */
                    <div className="flex justify-end">
                      <div className="max-w-[85%]">
                        {selectedGroupId && (message as any).user && (
                          <div className="flex items-center justify-end space-x-2 mb-2">
                            <span className="text-xs text-gray-500">
                              {(message as any).user.name}
                            </span>
                            <div className="w-6 h-6 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                              {(message as any).user.profilePictureUrl ? (
                                <img
                                  src={(message as any).user.profilePictureUrl}
                                  alt={(message as any).user.name}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center bg-blue-100">
                                  <span className="text-xs font-medium text-blue-600">
                                    {(message as any).user.name.charAt(0).toUpperCase()}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                        <div className="bg-blue-600 text-white rounded-2xl px-5 py-3">
                          <div className="font-medium leading-relaxed">
                            {renderMessageContent(message.content)}
                          </div>
                          <div className="text-xs text-blue-100 mt-2 opacity-75">
                            {message.timestamp.toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    /* Assistant Message */
                    <div className="flex justify-start">
                      <div className="max-w-[95%]">
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                          </div>
                          <span className="text-sm font-medium text-gray-700">OnlyGenyus</span>
                          <span className="text-xs text-gray-400">
                            {message.timestamp.toLocaleTimeString()}
                          </span>
                          <span className="text-xs px-2 py-0.5 rounded-full bg-gray-100 text-gray-500">
                            {selectedModel === 'deepseek' ? '💼 Business' : '💬 Conversation'}
                          </span>
                        </div>
                        <div className="bg-gray-50 rounded-2xl px-5 py-4 border border-gray-100">
                          <div className="prose prose-sm max-w-none text-gray-900 leading-relaxed">
                            {message.isStreaming && !message.content ? (
                              /* Loading state before streaming begins */
                              <div className="flex items-center space-x-2 text-gray-500">
                                <div className="flex space-x-1">
                                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                                </div>
                                <span className="text-sm">Thinking...</span>
                              </div>
                            ) : (
                              <>
                                {renderMessageContent(message.content, true)}
                                {message.isStreaming && message.content && (
                                  <span className="inline-block w-2 h-4 bg-blue-500 ml-1 animate-pulse rounded-sm" />
                                )}
                              </>
                            )}
                          </div>
                        </div>

                        {/* Share Button - DISABLED FOR NOW */}
                        {false && !message.isStreaming && message.content && (
                          <div className="flex items-center justify-end mt-2 space-x-2">
                            <button
                              onClick={() => handleShareToTimeline(message.id)}
                              disabled={sharingMessageId === message.id}
                              className="inline-flex items-center space-x-1 px-3 py-1.5 text-xs font-medium text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50"
                            >
                              {sharingMessageId === message.id ? (
                                <>
                                  <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                                  <span>Sharing...</span>
                                </>
                              ) : (
                                <>
                                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                  </svg>
                                  <span>Share to Timeline</span>
                                </>
                              )}
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Fixed Input Area */}
        <div className="flex-shrink-0 border-t border-gray-200 bg-white px-4 sm:px-6 py-4 pb-20 shadow-lg">
          <div className="max-w-3xl mx-auto">
            {/* Start AI Group Chat Option - DISABLED */}
            {false && showGroupChatOption && !selectedGroupId && messages.length === 0 && (
              <div className="mb-4 space-y-3">
                <div
                  onClick={() => setShowCreateModal(true)}
                  className="flex items-center justify-center p-3 border border-gray-200 rounded-lg bg-gray-50 hover:bg-blue-50 hover:border-blue-200 transition-colors cursor-pointer group"
                >
                  <svg className="w-4 h-4 text-gray-500 group-hover:text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 919.288 0M15 7a3 3 0 11-6 0 3 3 0 616 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <span className="text-sm text-gray-600 group-hover:text-blue-600 font-medium">Start AI Group Chat</span>
                </div>

                {/* Quick access to existing groups */}
                <div className="text-center">
                  <button
                    onClick={() => {
                      // If we have a selected group, return to it, otherwise go to main genyus page
                      const destination = selectedGroupId ? `/genyus?group=${selectedGroupId}` : '/genyus'
                      window.location.href = destination
                    }}
                    className="text-xs text-blue-600 hover:text-blue-800 underline"
                  >
                    Or return to your group chats
                  </button>
                </div>
              </div>
            )}

            {/* Clean Controls */}
            <div className="mb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button
                    type="button"
                    onClick={() => setShowPersonalHistory(true)}
                    className="flex items-center space-x-1.5 px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="hidden sm:inline">History</span>
                  </button>

                  {messages.length > 0 && (
                    <button
                      type="button"
                      onClick={() => {
                        setMessages([])
                        setInputValue('')
                      }}
                      className="flex items-center space-x-1.5 px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      <span className="hidden sm:inline">New Chat</span>
                    </button>
                  )}
                </div>

                {/* Model Selection */}
                <div className="flex items-center space-x-3 z-10">
                  <span className="text-sm text-gray-600">AI Model:</span>
                  <div className="flex items-center space-x-2">
                    <button
                      type="button"
                      onClick={() => setSelectedModel('gemini')}
                      className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-colors ${
                        selectedModel === 'gemini'
                          ? 'bg-green-100 text-green-800 border border-green-200'
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      💬 Conversation
                    </button>
                    <button
                      type="button"
                      onClick={() => setSelectedModel('deepseek')}
                      className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-colors ${
                        selectedModel === 'deepseek'
                          ? 'bg-purple-100 text-purple-800 border border-purple-200'
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      💼 Business
                    </button>
                  </div>
                </div>

                
              </div>
            </div>

            <form onSubmit={handleSubmit} className="relative">
              <div className="relative flex items-end space-x-3">
                <div className="flex-1 relative">
                  {selectedGroupId ? (
                    // Use MentionInput for group chats
                    <MentionInput
                      value={inputValue}
                      onChange={setInputValue}
                      onSubmit={() => handleSubmit(new Event('submit') as any)}
                      placeholder={`Message ${currentGroupName || 'the group'}...`}
                      groupMembers={groupMembers.filter(member => member.id !== user?.id).map(member => ({
                        id: member.id,
                        name: member.name,
                        profilePictureUrl: member.avatar ?? undefined
                      }))} // Exclude current user
                      disabled={isAsking || needsUpgrade}
                      className="w-full"
                    />
                  ) : (
                    // Regular textarea for personal chat
                    <>
                      <textarea
                        ref={inputRef}
                        value={inputValue}
                        onChange={(e) => {
                          setInputValue(e.target.value)
                          // Auto-resize for immersive typing
                          const textarea = e.target
                          textarea.style.height = 'auto'
                          textarea.style.height = Math.min(textarea.scrollHeight, e.target.value.trim() ? 200 : 60) + 'px'
                        }}
                        onKeyDown={handleKeyDown}
                        placeholder="Ask anything..."
                        className="w-full resize-none border border-gray-200 rounded-xl px-4 py-3 pr-16 text-base placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-300 bg-white transition-all"
                        rows={1}
                        maxLength={8000}
                        disabled={isAsking || needsUpgrade}
                        style={{
                          minHeight: '48px',
                          maxHeight: inputValue.trim() ? '200px' : '80px',
                          transition: 'all 0.2s ease-out'
                        }}
                      />
                      <div className="absolute right-3 bottom-3 flex items-center space-x-2">
                        {isAsking ? (
                          <div className="flex items-center space-x-1.5 text-xs text-purple-600">
                            <div className="w-2 h-2 bg-purple-600 rounded-full animate-pulse"></div>
                            <span className="hidden sm:inline">
                              {selectedModel === 'deepseek' ? 'Business thinking...' : 'Conversation thinking...'}
                            </span>
                          </div>
                        ) : (
                          <div className="text-xs text-gray-400">
                            {inputValue.length}/8000
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </div>
                <button
                  type="submit"
                  disabled={!inputValue.trim() || isAsking || needsUpgrade}
                  className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-300 disabled:to-gray-300 text-white rounded-xl transition-all flex items-center justify-center disabled:cursor-not-allowed shadow-md hover:shadow-lg"
                >
                  {isAsking ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  )}
                </button>
              </div>
            </form>

            {needsUpgrade && (
              <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-xl">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="w-5 h-5 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-amber-800 font-medium">
                      🎯 Ready to Upgrade to Unlimited?
                    </p>
                    <p className="text-sm text-amber-700 mt-1">
                      You've used all your free words! Upgrade to unlimited conversations with Gemini 2.5 Flash-Lite and get the full OnlyGenyus experience.
                    </p>
                    <button
                      onClick={() => setShowPurchaseModal(true)}
                      className="mt-2 text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors"
                    >
                      Upgrade to Unlimited →
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Purchase Modal */}
      {showPurchaseModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-3xl p-8 w-full max-w-lg shadow-2xl">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-2xl font-semibold text-gray-900">Refill Words</h2>
                <p className="text-gray-500 text-sm mt-1">Choose your word pack</p>
              </div>
              <button
                onClick={() => setShowPurchaseModal(false)}
                className="text-gray-400 hover:text-gray-600 p-2 hover:bg-gray-100 rounded-xl transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <p className="text-gray-600 text-sm mb-8 bg-gray-50 p-4 rounded-xl">
              💡 Your free words reset monthly. Refill anytime to keep the creativity flowing.
            </p>

            <div className="space-y-4">
              {wordPacksArray.map((pack) => (
                <button
                  key={pack.id}
                  onClick={() => handlePurchase(pack.priceId)}
                  disabled={purchasing}
                  className="w-full p-5 border-2 border-gray-100 hover:border-blue-200 rounded-2xl hover:bg-blue-50/50 transition-all text-left disabled:opacity-50 group"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-semibold text-gray-900 text-lg">{pack.label}</div>
                      <div className="font-mono text-sm text-gray-600 mt-1">
                        {pack.words === -1 ? 'Unlimited words' : `${pack.words.toLocaleString()} words`}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-blue-600 text-xl">{pack.price}</div>
                      <div className="text-xs text-gray-500 font-mono">
                        ${(parseFloat(pack.price.slice(1)) / (pack.words / 1000)).toFixed(2)}/1k words
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>

            <div className="mt-8 text-center">
              <div className="flex items-center justify-center space-x-2 text-xs text-gray-500">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <span>Secure payment powered by Stripe</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Conversation Intelligence Indicator */}
      <ConversationIntelligenceIndicator
        isVisible={isWaiting}
        reason={waitReason}
        estimatedWaitTime={estimatedTime}
        onComplete={hideWaiting}
      />

      {/* Modals */}
      {/* Group Create Modal - DISABLED */}
      {false && (
        <GroupCreateModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onGroupCreated={(group) => {
            // Auto-select the new group and load its details
            handleGroupSelect(group.id)
            setShowCreateModal(false)
          }}
          onInviteSent={(group) => {
            // Take user to group chat after sending invite and show tracker
            handleGroupSelect(group.id)
            setShowInviteTracker(true)
            setCurrentGroupInviteCode(group.invite_code)
            setShowCreateModal(false)
          }}
        />
      )}

      <PersonalHistoryModal
        isOpen={showPersonalHistory}
        onClose={() => setShowPersonalHistory(false)}
      />

      {/* Social Media 2.0 Onboarding */}
      <SocialMedia2OnboardingModal
        isOpen={onboarding.shouldShow}
        onClose={onboarding.closeModal}
        userName={user?.email?.split('@')[0] || 'Creator'}
        userId={user?.id}
        source={onboarding.source}
      />
    </div>
  )
}
