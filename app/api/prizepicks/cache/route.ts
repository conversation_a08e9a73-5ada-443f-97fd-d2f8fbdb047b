import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/server'

interface CacheStats {
  total_analyses: number
  fresh_analyses: number
  cached_analyses: number
  cache_hit_rate: number
  api_calls_saved: number
}

export async function GET() {
  try {
    const supabase = createSupabaseServerClient()

    // Get cache statistics for today
    const today = new Date().toISOString().split('T')[0]
    const startOfDay = `${today}T00:00:00.000Z`
    const endOfDay = `${today}T23:59:59.999Z`

    // Count total analyses today
    const { count: totalAnalyses } = await supabase
      .from('ai_analyses')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startOfDay)
      .lte('created_at', endOfDay)

    // Count fresh analyses (not served from cache)
    const { count: freshAnalyses } = await supabase
      .from('ai_analyses')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startOfDay)
      .lte('created_at', endOfDay)
      .eq('is_cached', false)

    const cachedAnalyses = (totalAnalyses || 0) - (freshAnalyses || 0)
    const cacheHitRate = totalAnalyses ? (cachedAnalyses / totalAnalyses) * 100 : 0

    // Each analysis saves ~2-3 API calls (Claude + Odds API)
    const apiCallsSaved = cachedAnalyses * 2.5

    const stats: CacheStats = {
      total_analyses: totalAnalyses || 0,
      fresh_analyses: freshAnalyses || 0,
      cached_analyses: cachedAnalyses,
      cache_hit_rate: Math.round(cacheHitRate * 10) / 10,
      api_calls_saved: Math.round(apiCallsSaved)
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error('Cache stats error:', error)
    return NextResponse.json({ error: 'Failed to fetch cache stats' }, { status: 500 })
  }
}

// Clear expired cache entries
export async function DELETE() {
  try {
    const supabase = createSupabaseServerClient()

    // Delete expired analyses
    const { data: deleted, error } = await supabase
      .from('ai_analyses')
      .delete()
      .lt('expires_at', new Date().toISOString())
      .select('id')

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      message: `Cleared ${deleted?.length || 0} expired cache entries`
    })

  } catch (error) {
    console.error('Cache cleanup error:', error)
    return NextResponse.json({ error: 'Failed to clear cache' }, { status: 500 })
  }
}

// Force refresh analysis (bypass cache)
export async function POST(request: NextRequest) {
  try {
    const { propId } = await request.json()

    if (!propId) {
      return NextResponse.json({ error: 'Prop ID required' }, { status: 400 })
    }

    const supabase = createSupabaseServerClient()

    // Delete existing analysis for this prop
    await supabase
      .from('ai_analyses')
      .delete()
      .eq('prop_id', propId)

    // Trigger fresh analysis
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/prizepicks/analyze`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ propId })
    })

    if (!response.ok) {
      throw new Error('Failed to generate fresh analysis')
    }

    const analysis = await response.json()

    return NextResponse.json({
      success: true,
      message: 'Fresh analysis generated',
      analysis
    })

  } catch (error) {
    console.error('Force refresh error:', error)
    return NextResponse.json({ error: 'Failed to force refresh' }, { status: 500 })
  }
}