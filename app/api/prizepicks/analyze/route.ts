import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/server'
import Anthropic from '@anthropic-ai/sdk'

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY!
})

interface SharpLineComparison {
  bookmaker: string
  line: number
  juice: number
  edge_percentage: number
}

interface InjuryReport {
  player: string
  status: string
  description: string
  probability_playing: number
}

interface GameContext {
  weather?: string
  venue: string
  total: number
  spread: number
  pace_projection: number
  game_script: 'competitive' | 'blowout_risk' | 'unknown'
}

interface AnalysisResult {
  analysis_text: string
  confidence_rating: number
  recommendation: 'OVER' | 'UNDER' | 'AVOID'
  reasoning_points: string[]
  sharp_line_comparison?: SharpLineComparison[]
  edge_summary?: string
  injury_concerns?: InjuryReport[]
  game_context?: GameContext
  correlations?: string[]
}

// Fetch sharp lines from multiple sportsbooks via Odds API
async function getSharpLineComparison(prop: any): Promise<SharpLineComparison[]> {
  try {
    if (!process.env.THE_ODDS_API_KEY) return []

    const sportMapping: Record<string, string> = {
      'NFL': 'americanfootball_nfl',
      'NCAAF': 'americanfootball_ncaaf',
      'NBA': 'basketball_nba',
      'WNBA': 'basketball_wnba'
    }

    const propTypeMapping: Record<string, string> = {
      'passing_yards': 'player_pass_yds',
      'rushing_yards': 'player_rush_yds',
      'receiving_yards': 'player_rec_yds',
      'receptions': 'player_receptions',
      'passing_touchdowns': 'player_pass_tds',
      'pass_yards': 'player_pass_yds',
      'rush_yards': 'player_rush_yds',
      'rec_yards': 'player_rec_yds'
    }

    const sportKey = sportMapping[prop.sport]
    const marketKey = propTypeMapping[prop.prop_type.toLowerCase()]

    if (!sportKey || !marketKey) return []

    const url = `https://api.the-odds-api.com/v4/sports/${sportKey}/odds`
    const params = new URLSearchParams({
      apiKey: process.env.THE_ODDS_API_KEY,
      regions: 'us',
      markets: marketKey,
      bookmakers: 'pinnacle,circa,draftkings,fanduel,mgm,caesars',
      oddsFormat: 'american'
    })

    const response = await fetch(`${url}?${params}`)
    if (!response.ok) return []

    const data = await response.json()
    const comparisons: SharpLineComparison[] = []

    for (const game of data) {
      for (const bookmaker of game.bookmakers || []) {
        for (const market of bookmaker.markets || []) {
          if (market.key !== marketKey) continue

          for (const outcome of market.outcomes || []) {
            if (outcome.point !== undefined &&
                outcome.name?.toLowerCase().includes(prop.player_name.toLowerCase().split(' ').pop())) {

              const edgePercentage = ((prop.line_value - outcome.point) / outcome.point) * 100

              comparisons.push({
                bookmaker: bookmaker.title,
                line: outcome.point,
                juice: outcome.price,
                edge_percentage: edgePercentage
              })
            }
          }
        }
      }
    }

    return comparisons.slice(0, 4) // Top 4 books

  } catch (error) {
    console.error('Sharp line fetch error:', error)
    return []
  }
}

// Get injury reports and game context
async function getGameContext(prop: any): Promise<{ injuries: InjuryReport[], context: GameContext }> {
  try {
    // Import injury API (dynamic import to avoid circular dependencies)
    const { getInjuryContextForProp } = await import('@/lib/prizepicks/injury-api')

    // Get comprehensive injury context
    const injuryContext = await getInjuryContextForProp(prop)

    // Compile relevant injuries
    const relevantInjuries: InjuryReport[] = []

    if (injuryContext.player_injury) {
      relevantInjuries.push({
        player: injuryContext.player_injury.player_name,
        status: injuryContext.player_injury.injury_status,
        description: injuryContext.player_injury.injury_description,
        probability_playing: injuryContext.player_injury.injury_status === 'HEALTHY' ? 100 :
                            injuryContext.player_injury.injury_status === 'PROBABLE' ? 85 :
                            injuryContext.player_injury.injury_status === 'QUESTIONABLE' ? 50 :
                            injuryContext.player_injury.injury_status === 'DOUBTFUL' ? 15 : 0
      })
    }

    // Add key absences that might affect game script
    for (const absence of injuryContext.game_injury_context.key_absences.slice(0, 3)) {
      if (!relevantInjuries.find(inj => inj.player === absence)) {
        relevantInjuries.push({
          player: absence,
          status: 'OUT',
          description: 'Key player absence affects game dynamics',
          probability_playing: 0
        })
      }
    }

    // Build game context
    const context: GameContext = {
      venue: 'TBD', // Would fetch from venue API
      total: 45.5, // Would fetch from sportsbook APIs
      spread: -3.5, // Would fetch from sportsbook APIs
      pace_projection: 65.5, // Would calculate from team pace metrics
      game_script: injuryContext.game_injury_context.lineup_uncertainty ? 'unknown' :
                   injuryContext.game_injury_context.key_absences.length > 2 ? 'blowout_risk' : 'competitive'
    }

    return { injuries: relevantInjuries, context }

  } catch (error) {
    console.error('Game context error:', error)

    // Fallback to mock data
    const mockInjuries: InjuryReport[] = []
    const mockContext: GameContext = {
      venue: 'Unknown',
      total: 45.5,
      spread: -3.5,
      pace_projection: 65.5,
      game_script: 'competitive'
    }

    return { injuries: mockInjuries, context: mockContext }
  }
}

// Generate correlation suggestions
function getCorrelationSuggestions(prop: any): string[] {
  const correlations: string[] = []

  const propType = prop.prop_type.toLowerCase()
  const playerName = prop.player_name

  if (propType.includes('passing_yards') || propType.includes('pass_yards')) {
    correlations.push(`Consider ${playerName} passing TDs - high correlation with passing yards`)
    correlations.push(`Look at game total - higher totals often mean more passing volume`)
    correlations.push(`Check weather conditions - wind/rain affects passing games significantly`)
  }

  if (propType.includes('rushing_yards') || propType.includes('rush_yards')) {
    correlations.push(`Consider ${playerName} rushing attempts - volume drives yards`)
    correlations.push(`Check game script - teams losing late abandon the run`)
    correlations.push(`Look at opposing run defense ranking`)
  }

  if (propType.includes('receiving_yards') || propType.includes('rec_yards')) {
    correlations.push(`Consider ${playerName} receptions - targets drive opportunity`)
    correlations.push(`Check if primary WR - target share matters significantly`)
    correlations.push(`Look at opposing pass defense vs WR position`)
  }

  if (propType.includes('receptions')) {
    correlations.push(`Consider ${playerName} receiving yards for same-game parlay`)
    correlations.push(`Check red zone targets - affects TD probability`)
    correlations.push(`Look at team passing volume trends`)
  }

  return correlations.slice(0, 3) // Top 3 correlations
}

function buildAnalysisPrompt(prop: any, sharpLines: SharpLineComparison[], gameContext: any, correlations: string[]): string {
  const currentDate = new Date().toLocaleDateString()
  const gameDate = new Date(prop.game_time).toLocaleDateString()

  let sharpLineText = ''
  if (sharpLines.length > 0) {
    sharpLineText = `\n\nSHARP SPORTSBOOK LINES:
${sharpLines.map(line =>
  `- ${line.bookmaker}: ${line.line} (${line.edge_percentage > 0 ? '+' : ''}${line.edge_percentage.toFixed(1)}% edge)`
).join('\n')}

EDGE ANALYSIS:
${sharpLines.length > 0 ?
  `Average Sharp Line: ${(sharpLines.reduce((sum, line) => sum + line.line, 0) / sharpLines.length).toFixed(1)}
PrizePicks Line: ${prop.line_value}
Line Differential: ${(prop.line_value - (sharpLines.reduce((sum, line) => sum + line.line, 0) / sharpLines.length)).toFixed(1)}`
  : 'No sharp lines available for comparison'}`
  }

  let correlationText = ''
  if (correlations.length > 0) {
    correlationText = `\n\nCORRELATED OPPORTUNITIES:
${correlations.map(corr => `- ${corr}`).join('\n')}`
  }

  return `You are an elite sports betting analyst with access to sharp sportsbook data. Analyze this PrizePicks prop bet with professional-grade insights.

PROP DETAILS:
Player: ${prop.player_name}
Team: ${prop.team}${prop.opponent ? ` vs ${prop.opponent}` : ''}
Sport: ${prop.sport}
Prop: ${prop.prop_type}
Line: ${prop.line_value}
Game Date: ${gameDate}
Analysis Date: ${currentDate}${sharpLineText}${correlationText}

ENHANCED ANALYSIS REQUIREMENTS:

1. **STATISTICAL FOUNDATION**
   - Player's last 5 games in this category (hit rate vs similar lines)
   - Season averages and trends (early vs late season performance)
   - Home/away splits and venue-specific performance
   - Rest advantage/disadvantage (days between games)

2. **MATCHUP DYNAMICS**
   - Opponent defensive rankings vs this position/prop type
   - Historical head-to-head performance
   - Coaching tendencies and game plan implications
   - Personnel matchups (key injuries affecting usage)

3. **SHARP LINE EDGE ASSESSMENT**
   - Compare PrizePicks line to sharp sportsbook consensus
   - Identify significant line discrepancies (>3% edges)
   - Consider juice and market efficiency factors
   - Flag props with exceptional value or overpriced lines

4. **GAME SCRIPT & CONTEXT**
   - Projected game flow (competitive vs blowout potential)
   - Total/spread implications for prop volume
   - Weather impact (outdoor games only)
   - Pace of play and possession projections

5. **RISK FACTORS**
   - Lineup uncertainty or late scratches
   - Motivational factors (playoff implications, etc.)
   - Public betting bias and line movement
   - Variance potential (floor vs ceiling outcomes)

FORMAT YOUR RESPONSE AS JSON:
{
  "analysis_text": "Comprehensive 3-4 paragraph analysis with specific data points and reasoning",
  "confidence_rating": 1-5 (where 5 is highest confidence based on data strength),
  "recommendation": "OVER" | "UNDER" | "AVOID",
  "reasoning_points": [
    "Primary factor with specific data",
    "Secondary factor with matchup details",
    "Risk factor or edge opportunity",
    "Game context consideration"
  ],
  "edge_summary": "Brief summary of line value vs sharp books",
  "correlations": [
    "Related prop suggestion 1",
    "Related prop suggestion 2"
  ]
}

CRITICAL: Base your recommendation on data-driven analysis, not gut feel. If sharp lines show significant edge (>5%), weight this heavily in your recommendation.`
}

export async function POST(request: NextRequest) {
  try {
    const { propId } = await request.json()

    if (!propId) {
      return NextResponse.json({ error: 'Prop ID required' }, { status: 400 })
    }

    const supabase = await createSupabaseServerClient()

    // Get prop details
    const { data: prop, error: propError } = await supabase
      .from('prizepicks_props')
      .select('*')
      .eq('id', propId)
      .single()

    if (propError || !prop) {
      return NextResponse.json({ error: 'Prop not found' }, { status: 404 })
    }

    // Check for recent analysis (within 4 hours)
    const { data: existingAnalysis } = await supabase
      .from('ai_analyses')
      .select('*')
      .eq('prop_id', propId)
      .gte('created_at', new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    if (existingAnalysis) {
      return NextResponse.json(existingAnalysis)
    }

    // Fetch enhanced data
    console.log(`Analyzing prop: ${prop.player_name} ${prop.prop_type} ${prop.line_value}`)

    // Get sharp line comparisons (use API call strategically)
    const sharpLines = await getSharpLineComparison(prop)

    // Get game context and injury data
    const { injuries, context } = await getGameContext(prop)

    // Get correlation suggestions
    const correlations = getCorrelationSuggestions(prop)

    // Build enhanced prompt with all data
    const prompt = buildAnalysisPrompt(prop, sharpLines, context, correlations)

    const response = await anthropic.messages.create({
      model: 'claude-sonnet-4-20250514', // Latest Sonnet for best analysis
      max_tokens: 1500, // Increased for enhanced analysis
      temperature: 0.2, // Lower temperature for more analytical consistency
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ]
    })

    const analysisText = response.content[0]?.type === 'text' ? response.content[0].text : ''

    // Parse JSON response
    let analysisResult: AnalysisResult
    try {
      analysisResult = JSON.parse(analysisText)

      // Add enhanced data to result
      analysisResult.sharp_line_comparison = sharpLines
      analysisResult.injury_concerns = injuries
      analysisResult.game_context = context

      // Generate edge summary if sharp lines available
      if (sharpLines.length > 0) {
        const avgSharpLine = sharpLines.reduce((sum, line) => sum + line.line, 0) / sharpLines.length
        const edgePercent = ((prop.line_value - avgSharpLine) / avgSharpLine) * 100

        if (Math.abs(edgePercent) > 5) {
          analysisResult.edge_summary = `Strong ${edgePercent > 0 ? 'UNDER' : 'OVER'} edge: ${Math.abs(edgePercent).toFixed(1)}% line differential vs sharp books`
        } else if (Math.abs(edgePercent) > 2) {
          analysisResult.edge_summary = `Moderate ${edgePercent > 0 ? 'UNDER' : 'OVER'} edge: ${Math.abs(edgePercent).toFixed(1)}% line differential`
        } else {
          analysisResult.edge_summary = `No significant edge: ${Math.abs(edgePercent).toFixed(1)}% differential (stay away)`
        }
      }

    } catch (parseError) {
      // Fallback if JSON parsing fails
      analysisResult = {
        analysis_text: analysisText,
        confidence_rating: 3,
        recommendation: 'AVOID',
        reasoning_points: ['Analysis parsing failed - manual review needed'],
        sharp_line_comparison: sharpLines,
        edge_summary: sharpLines.length > 0 ? 'Unable to parse line comparison' : 'No sharp lines available',
        injury_concerns: injuries,
        game_context: context,
        correlations: correlations
      }
    }

    // Validate analysis result
    if (!analysisResult.recommendation || !['OVER', 'UNDER', 'AVOID'].includes(analysisResult.recommendation)) {
      analysisResult.recommendation = 'AVOID'
    }

    if (!analysisResult.confidence_rating || analysisResult.confidence_rating < 1 || analysisResult.confidence_rating > 5) {
      analysisResult.confidence_rating = 3
    }

    // Save enhanced analysis to database
    const { data: savedAnalysis, error: saveError } = await supabase
      .from('ai_analyses')
      .insert([
        {
          prop_id: propId,
          analysis_text: analysisResult.analysis_text,
          confidence_rating: analysisResult.confidence_rating,
          recommendation: analysisResult.recommendation,
          reasoning_points: analysisResult.reasoning_points,
          sharp_line_comparison: analysisResult.sharp_line_comparison,
          edge_summary: analysisResult.edge_summary,
          injury_concerns: analysisResult.injury_concerns,
          game_context: analysisResult.game_context,
          correlations: analysisResult.correlations,
          created_at: new Date().toISOString(),
          expires_at: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString() // 6 hours cache for enhanced analysis
        }
      ])
      .select()
      .single()

    if (saveError) {
      console.error('Failed to save analysis:', saveError)
      return NextResponse.json({ error: 'Failed to save analysis' }, { status: 500 })
    }

    return NextResponse.json(savedAnalysis)

  } catch (error) {
    console.error('Analysis error:', error)

    // Check if it's an Anthropic API error
    if (error instanceof Error && error.message.includes('rate limit')) {
      return NextResponse.json(
        { error: 'AI service temporarily unavailable - please try again in a moment' },
        { status: 429 }
      )
    }

    return NextResponse.json(
      { error: 'Analysis failed - please try again' },
      { status: 500 }
    )
  }
}