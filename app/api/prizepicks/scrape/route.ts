import { NextRequest, NextResponse } from 'next/server'
import { updatePrizePicksDatabase } from '@/lib/prizepicks/scraper'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}))
    const { sport } = body

    console.log(`Starting PrizePicks scrape for ${sport || 'all sports'}`)

    const result = await updatePrizePicksDatabase(sport)

    console.log('Scrape completed:', result)

    return NextResponse.json({
      success: true,
      message: `Scraping completed: ${result.new} new props, ${result.updated} updated, ${result.movements} line movements detected`,
      data: result
    })

  } catch (error) {
    console.error('Scraping API error:', error)

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Scraping failed'
      },
      { status: 500 }
    )
  }
}

// Allow GET for manual triggering
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const sport = searchParams.get('sport') || undefined

  try {
    const result = await updatePrizePicksDatabase(sport)

    return NextResponse.json({
      success: true,
      message: `Manual scrape completed: ${result.new} new props, ${result.updated} updated`,
      data: result
    })

  } catch (error) {
    console.error('Manual scrape error:', error)

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Manual scrape failed'
      },
      { status: 500 }
    )
  }
}