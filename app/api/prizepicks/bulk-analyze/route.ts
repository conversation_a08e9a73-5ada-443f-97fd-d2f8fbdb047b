import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

interface BulkAnalysisResult {
  analyzed: number
  cached: number
  failed: number
  api_calls_used: number
  estimated_cost: number
  total_props: number
}

// Prioritize props for analysis based on importance
function getPropPriority(prop: any): number {
  let priority = 0

  // High-value players get higher priority
  const highValuePlayers = ['josh allen', 'lamar jackson', 'patrick mahomes', 'tyreek hill', 'travis kelce']
  const playerName = prop.player_name.toLowerCase()
  if (highValuePlayers.some(name => playerName.includes(name))) {
    priority += 10
  }

  // Prime-time games get higher priority
  const gameHour = new Date(prop.game_time).getHours()
  if (gameHour >= 19 || gameHour <= 1) { // 7 PM to 1 AM (prime time)
    priority += 5
  }

  // Popular prop types get higher priority
  const popularProps = ['passing_yards', 'rushing_yards', 'receiving_yards', 'receptions']
  if (popularProps.includes(prop.prop_type)) {
    priority += 3
  }

  // Recent line movements get higher priority
  if (prop.movement_count && prop.movement_count > 0) {
    priority += 2
  }

  return priority
}

export async function POST(request: NextRequest) {
  try {
    const { maxAnalyses = 50, forceRefresh = false } = await request.json()

    const supabase = await createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get active props for today and tomorrow
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)

    const { data: props, error: propsError } = await supabase
      .from('prizepicks_props')
      .select('*')
      .eq('is_active', true)
      .gte('game_time', new Date().toISOString())
      .lt('game_time', tomorrow.toISOString())
      .order('game_time', { ascending: true })

    if (propsError || !props) {
      return NextResponse.json({ error: 'Failed to fetch props' }, { status: 500 })
    }

    // Filter props that need analysis
    const propsNeedingAnalysis: any[] = []

    for (const prop of props) {
      if (forceRefresh) {
        propsNeedingAnalysis.push(prop)
        continue
      }

      // Get last analysis (no time window)
      const { data: lastAnalysis } = await supabase
        .from('ai_analyses')
        .select('created_at')
        .eq('prop_id', prop.id)
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (!lastAnalysis) {
        // Never analyzed -> analyze now
        propsNeedingAnalysis.push(prop)
        continue
      }

      // Has analysis: only re-run if the line has changed since last analysis
      const { data: lastMovement } = await supabase
        .from('line_movements')
        .select('detected_at')
        .eq('prop_id', prop.id)
        .order('detected_at', { ascending: false })
        .limit(1)
        .single()

      if (lastMovement && new Date(lastMovement.detected_at).getTime() > new Date(lastAnalysis.created_at).getTime()) {
        propsNeedingAnalysis.push(prop)
      }
    }

    // Sort by priority
    propsNeedingAnalysis.sort((a, b) => getPropPriority(b) - getPropPriority(a))

    // Limit to maxAnalyses
    const propsToAnalyze = propsNeedingAnalysis.slice(0, maxAnalyses)

    const result: BulkAnalysisResult = {
      analyzed: 0,
      cached: props.length - propsNeedingAnalysis.length,
      failed: 0,
      api_calls_used: 0,
      estimated_cost: 0,
      total_props: props.length
    }

    // Analyze props in batches to avoid rate limits
    const batchSize = 5
    for (let i = 0; i < propsToAnalyze.length; i += batchSize) {
      const batch = propsToAnalyze.slice(i, i + batchSize)

      // Process batch with small delay between requests
      const batchPromises = batch.map(async (prop, index) => {
        try {
          // Small delay to avoid rate limits
          await new Promise(resolve => setTimeout(resolve, index * 200))

          const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/prizepicks/analyze`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ propId: prop.id })
          })

          if (response.ok) {
            result.analyzed++
            result.api_calls_used += 2.5 // Estimate: Claude + Odds API calls
          } else {
            result.failed++
          }

        } catch (error) {
          console.error(`Failed to analyze prop ${prop.id}:`, error)
          result.failed++
        }
      })

      await Promise.all(batchPromises)

      // Longer delay between batches
      if (i + batchSize < propsToAnalyze.length) {
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }

    // Calculate estimated cost (rough estimates)
    // Claude Sonnet: ~$0.003 per request
    // Odds API: ~$0.01 per request (assuming $10/1000 calls)
    result.estimated_cost = (result.analyzed * 0.003) + (result.analyzed * 0.01)

    return NextResponse.json({
      success: true,
      message: `Bulk analysis completed. Analyzed ${result.analyzed} props, ${result.cached} served from cache, ${result.failed} failed.`,
      result
    })

  } catch (error) {
    console.error('Bulk analysis error:', error)
    return NextResponse.json({ error: 'Bulk analysis failed' }, { status: 500 })
  }
}

// Get bulk analysis status
export async function GET() {
  try {
    const supabase = createSupabaseServerClient()

    // Get today's props and analysis status
    const today = new Date().toISOString().split('T')[0]
    const startOfDay = `${today}T00:00:00.000Z`
    const endOfDay = `${today}T23:59:59.999Z`

    // Count total active props for today
    const { count: totalProps } = await supabase
      .from('prizepicks_props')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)
      .gte('game_time', startOfDay)
      .lte('game_time', endOfDay)

    // Count props with recent analysis
    const { count: analyzedProps } = await supabase
      .from('prizepicks_props')
      .select(`
        id,
        ai_analyses!inner(created_at)
      `, { count: 'exact', head: true })
      .eq('is_active', true)
      .gte('game_time', startOfDay)
      .lte('game_time', endOfDay)
      .gte('ai_analyses.created_at', new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString())

    const analysisCompletePercentage = totalProps ? (analyzedProps / totalProps) * 100 : 0

    return NextResponse.json({
      total_props: totalProps || 0,
      analyzed_props: analyzedProps || 0,
      pending_analysis: (totalProps || 0) - (analyzedProps || 0),
      completion_percentage: Math.round(analysisCompletePercentage * 10) / 10,
      ready_for_betting: analysisCompletePercentage >= 80
    })

  } catch (error) {
    console.error('Bulk analysis status error:', error)
    return NextResponse.json({ error: 'Failed to get status' }, { status: 500 })
  }
}