import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export async function POST(req: NextRequest) {
  try {
    const { question, sport } = await req.json();

    // Get user from auth header
    const authHeader = req.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has access to PrizePicks (same as <PERSON><PERSON><PERSON><PERSON><PERSON> for now)
    const { data: wordData } = await supabase
      .from('genyus_user_words')
      .select('words_remaining, tier')
      .eq('user_id', user.id)
      .single();

    if (!wordData || (wordData.words_remaining < 1 && wordData.tier !== 'unlimited')) {
      return NextResponse.json({ error: 'Insufficient word balance' }, { status: 402 });
    }

    // For now, provide a response indicating the feature is being built
    // This will be replaced with actual RAG + AI analysis once the data collection is set up
    
    const responseText = `🎯 **PrizePicks AI Analysis**\n\n*Feature Status: Data Integration In Progress*\n\nI'm currently analyzing your query about "${question}"${sport !== 'all' ? ` in ${sport.toUpperCase()}` : ''}.\n\n📊 **System Status**:\n• Real-time odds data integration: *Building*\n• AI analysis engine: *Ready*\n• +EV opportunity detection: *Calibrating*\n\n🔧 **What's Coming**:\n- Live line data from major sportsbooks\n- Real-time smart money tracking\n- Mathematical edge calculations\n- Personalized +EV recommendations\n\n💡 **Pro Tip**: Once fully launched, I'll analyze thousands of lines across ${sport !== 'all' ? sport.toUpperCase() : 'all sports'} to find opportunities that 99% of bettors miss.\n\nStay tuned for the complete PrizePicks experience! 🚀`;

    // Deduct words (same as OnlyGenyus pricing)
    const wordsUsed = Math.max(1, Math.ceil(responseText.length / 4));
    
    if (wordData.tier !== 'unlimited') {
      await supabase
        .from('genyus_user_words')
        .update({ words_remaining: wordData.words_remaining - wordsUsed })
        .eq('user_id', user.id);
    }

    // Create stream for response
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        // Simulate streaming response
        for (let i = 0; i < responseText.length; i += 5) {
          const chunk = responseText.slice(i, i + 5);
          controller.enqueue(encoder.encode(chunk));
          await new Promise(resolve => setTimeout(resolve, 20));
        }
        controller.close();
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
      },
    });

  } catch (error) {
    console.error('PrizePicks API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
