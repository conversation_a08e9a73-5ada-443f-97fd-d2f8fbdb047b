import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { password } = await request.json()

    if (!password) {
      return NextResponse.json({ error: 'Password required' }, { status: 400 })
    }

    // Check against environment variable
    const correctPassword = process.env.PRIZEPICKS_PASSWORD

    if (!correctPassword) {
      return NextResponse.json({ error: 'Password not configured' }, { status: 500 })
    }

    if (password === correctPassword) {
      return NextResponse.json({ success: true })
    } else {
      return NextResponse.json({ error: 'Incorrect password' }, { status: 401 })
    }

  } catch (error) {
    console.error('Password verification error:', error)
    return NextResponse.json({ error: 'Verification failed' }, { status: 500 })
  }
}