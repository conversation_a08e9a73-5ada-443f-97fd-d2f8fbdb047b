import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { createSupabaseAdminClient } from '@/lib/supabase/admin'

// Helper function to parse game time
function parseGameTime(dayOfWeek: string, timeStr: string): string {
  const now = new Date()
  const currentDay = now.getDay() // 0 = Sunday, 1 = Monday, etc.

  // Map day names to numbers
  const dayMap: Record<string, number> = {
    'Sun': 0, 'Mon': 1, 'Tue': 2, 'Wed': 3, 'Thu': 4, 'Fri': 5, 'Sat': 6
  }

  const targetDay = dayMap[dayOfWeek]
  if (targetDay === undefined) {
    // Default to tomorrow if we can't parse the day
    const tomorrow = new Date(now)
    tomorrow.setDate(now.getDate() + 1)
    tomorrow.setHours(20, 0, 0, 0)
    return tomorrow.toISOString()
  }

  // Parse time
  const timeMatch = timeStr.match(/(\d{1,2}):(\d{2})(am|pm)/)
  if (!timeMatch) {
    // Default time if we can't parse
    const tomorrow = new Date(now)
    tomorrow.setDate(now.getDate() + 1)
    tomorrow.setHours(20, 0, 0, 0)
    return tomorrow.toISOString()
  }

  let hours = parseInt(timeMatch[1])
  const minutes = parseInt(timeMatch[2])
  const ampm = timeMatch[3]

  if (ampm === 'pm' && hours !== 12) hours += 12
  if (ampm === 'am' && hours === 12) hours = 0

  // Calculate days until target day
  let daysUntil = targetDay - currentDay

  // If it's the same day, check if the time has passed
  if (daysUntil === 0) {
    const gameTimeToday = new Date(now)
    gameTimeToday.setHours(hours, minutes, 0, 0)
    if (gameTimeToday <= now) {
      daysUntil = 7 // Next week
    }
  } else if (daysUntil < 0) {
    daysUntil += 7 // Next week
  }

  const gameDate = new Date(now)
  gameDate.setDate(now.getDate() + daysUntil)
  gameDate.setHours(hours, minutes, 0, 0)

  return gameDate.toISOString()
}

interface PrizePicksProp {
  external_id: string
  sport: string
  player_name: string
  team?: string
  opponent?: string
  prop_type: string
  line_value: number
  game_time?: string
  is_active?: boolean
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has authorized email
    const authorizedEmails = ['<EMAIL>', '<EMAIL>']
    if (!authorizedEmails.includes(user.email || '')) {
      return NextResponse.json({ error: 'Unauthorized email address' }, { status: 403 })
    }

    const { props, date } = await request.json()

    if (!props || !Array.isArray(props)) {
      return NextResponse.json({ error: 'Props array is required' }, { status: 400 })
    }

    const inputDate = date || new Date().toISOString().split('T')[0]

    // Deactivate all existing props for the day
    await supabase
      .from('prizepicks_props')
      .update({ is_active: false })
      .eq('date_scraped', inputDate)

    // Process and insert new props
    const propsToInsert = props.map((prop: PrizePicksProp) => ({
      external_id: prop.external_id || `manual_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      sport: 'NFL', // Default to NFL for now
      player_name: prop.player_name,
      team: prop.team,
      opponent: prop.opponent || null,
      prop_type: prop.prop_type.toLowerCase().replace(/\s+/g, '_'),
      line_value: prop.line_value,
      game_time: prop.game_time,
      is_active: true,
      scraped_at: new Date().toISOString(),
      date_scraped: inputDate,
      source: 'manual_input'
    }))

    const { data: insertedProps, error: insertError } = await supabase
      .from('prizepicks_props')
      .insert(propsToInsert)
      .select()

    if (insertError) {
      console.error('Insert error:', insertError)
      return NextResponse.json({ error: 'Failed to insert props' }, { status: 500 })
    }

    // Trigger AI analysis for high-value props
    const analysisPromises = insertedProps.map(async (prop) => {
      try {
        await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/prizepicks/analyze`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ propId: prop.id })
        })
      } catch (error) {
        console.error(`Failed to trigger analysis for prop ${prop.id}:`, error)
      }
    })

    // Don't wait for analysis to complete
    Promise.all(analysisPromises).catch(console.error)

    return NextResponse.json({
      success: true,
      message: `Successfully imported ${insertedProps.length} props for ${inputDate}`,
      props: insertedProps.length,
      date: inputDate
    })

  } catch (error) {
    console.error('Manual input error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Parse text input from user (copy-paste format)
export async function PUT(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has authorized email
    const authorizedEmails = ['<EMAIL>', '<EMAIL>']
    if (!authorizedEmails.includes(user.email || '')) {
      return NextResponse.json({ error: 'Unauthorized email address' }, { status: 403 })
    }

    // Use admin client for database operations to bypass RLS
    const adminSupabase = createSupabaseAdminClient()

    const { rawInput, date } = await request.json()

    if (!rawInput || typeof rawInput !== 'string') {
      return NextResponse.json({ error: 'Raw input text is required' }, { status: 400 })
    }

    const lines = rawInput.split('\n').filter(line => line.trim())
    const props: PrizePicksProp[] = []

    // Support canonical pipe-delimited format:
    // SPORT | PLAYER_NAME | TEAM | OPPONENT | DOW | TIME_ET | PROP_TYPE | LINE_VALUE
    const pipeLines = rawInput.split('\n').map(l => l.trim()).filter(Boolean)
    const pipeCandidates = pipeLines.filter(l => l.includes('|'))
    if (pipeCandidates.length > 0 && pipeCandidates.every(l => l.split('|').length >= 8)) {
      const mapPropType = (label: string) => {
        const propTypeMap: Record<string, string> = {
          // NFL
          'Rush+Rec TDs': 'rush_rec_touchdowns',
          'Rush Yards': 'rushing_yards',
          'Pass Yards': 'passing_yards',
          'Rec Yards': 'receiving_yards',
          'Receiving Yards': 'receiving_yards',
          'Receptions': 'receptions',
          'FG Made': 'field_goals_made',
          'Pass TDs': 'passing_touchdowns',
          'Rush TDs': 'rushing_touchdowns',
          'Rec TDs': 'receiving_touchdowns',
          // Basketball
          'Points': 'points',
          'Rebounds': 'rebounds',
          'Assists': 'assists',
          'Pts+Rebs+Asts': 'points_rebounds_assists',
          'Pts+Rebs': 'points_rebounds',
          'Pts+Ast': 'points_assists',
          'Rebs+Asts': 'rebounds_assists',
          'Fantasy Score': 'fantasy_score',
          '3-PT Made': 'three_pointers_made',
          '3PT Made': 'three_pointers_made',
          '3PT': 'three_pointers_made',
          '3 Pointers Made': 'three_pointers_made'
        }
        return propTypeMap[label] || label.toLowerCase().replace(/[\s\+\-]/g, '_')
      }

      for (const line of pipeCandidates) {
        const parts = line.split('|').map(p => p.trim())
        const [sport, player_name, team, opponent, dow, timeStr, propLabel, lineVal] = parts
        const prop: PrizePicksProp = {
          external_id: `manual_${Date.now()}_${props.length}`,
          sport: sport || 'NFL',
          player_name,
          team: team || undefined,
          opponent: opponent || undefined,
          prop_type: mapPropType(propLabel),
          line_value: parseFloat(lineVal),
          game_time: parseGameTime(dow, timeStr),
          is_active: true
        }
        if (!isNaN(prop.line_value) && prop.player_name && prop.prop_type) {
          props.push(prop)
        }
      }
    }
    // Parse PrizePicks mobile app format - completely rewritten for robustness
    let currentProp: Partial<PrizePicksProp> = {}
    let state: 'seeking_player' | 'has_player' | 'has_team' | 'has_game' | 'has_line' = 'seeking_player'

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()

      // Skip empty lines
      if (!line) continue

      // Skip obvious noise lines (do NOT skip plain numeric lines like "4.5")
      // Only match trending counts like "12.3K" or "9K".
      if (line.match(/^(More|Less|Trending|\d+(?:\.\d+)?K|6d 23h|Swap)$/)) {
        continue
      }

      console.log(`Line ${i}: "${line}" (state: ${state})`)

      try {
        // State machine approach for more reliable parsing

        if (state === 'seeking_player') {
          // Look for player name (first occurrence, may have suffix)
          if (line.match(/^[A-Za-z\s\-\.\']+(?:Goblin|Demon|Money Mouth|Flash Sale)?$/)) {
            // Clean the name
            let cleanName = line
            const suffixes = ['Goblin', 'Demon', 'Money Mouth', 'Flash Sale']
            for (const suffix of suffixes) {
              if (line.endsWith(suffix)) {
                cleanName = line.substring(0, line.length - suffix.length).trim()
                break
              }
            }

            currentProp = {
              player_name: cleanName,
              external_id: `manual_${Date.now()}_${props.length}`,
              sport: 'NFL'
            }
            state = 'has_player'
            console.log(`Found player: ${cleanName}`)
            continue
          }
        }

        else if (state === 'has_player') {
          // Look for team-position line
          // Match team-position lines like "IND - G" or "IND - C-F" (allow hyphenated positions)
          if (line.match(/^[A-Z]{2,4}\s*-\s*/)) {
            const [team] = line.split('-').map(s => s.trim())
            currentProp.team = team
            state = 'has_team'
            console.log(`Found team: ${team}`)
            continue
          }
          // Sometimes we skip directly to clean name
          else if (line.match(/^[A-Za-z\s\-\.\']+$/) && !line.match(/^(vs|@)/)) {
            // This is probably the clean player name, continue
            continue
          }
        }

        else if (state === 'has_team') {
          // Look for clean player name (skip it) or game info
          if (line.match(/^(vs|@)\s+[A-Z]{2,4}\s+\w+\s+\d{1,2}:\d{2}[ap]m$/)) {
            const parts = line.split(/\s+/)
            currentProp.opponent = parts[1]

            // Parse game time
            const dayOfWeek = parts[2]
            const timeStr = parts[3]
            const gameTime = parseGameTime(dayOfWeek, timeStr)
            currentProp.game_time = gameTime

            state = 'has_game'
            console.log(`Found game: vs ${parts[1]} at ${gameTime}`)
            continue
          }
          // Skip clean player name
          else if (line.match(/^[A-Za-z\s\-\.\']+$/) && !line.match(/^(vs|@)/)) {
            continue
          }
        }

        else if (state === 'has_game') {
          // Look for line value
          if (line.match(/^\d+\.?\d*$/)) {
            currentProp.line_value = parseFloat(line)
            state = 'has_line'
            console.log(`Found line value: ${line}`)
            continue
          }
          // Handle malformed line values
          else if (line.match(/^\d+\.\d+[\d\.]+$/)) {
            const match = line.match(/^(\d+\.\d+)/)
            if (match) {
              currentProp.line_value = parseFloat(match[1])
              state = 'has_line'
              console.log(`Found malformed line value: ${line} -> ${match[1]}`)
              continue
            }
          }
        }

        else if (state === 'has_line') {
          // Look for prop type (allow letters, numbers, spaces, plus, and hyphen)
          if (line.match(/^[A-Za-z0-9\s\+\-]+$/)) {
            const propTypeMap: Record<string, string> = {
              // NFL
              'Rush+Rec TDs': 'rush_rec_touchdowns',
              'Rush Yards': 'rushing_yards',
              'Pass Yards': 'passing_yards',
              'Rec Yards': 'receiving_yards',
              'Receiving Yards': 'receiving_yards',
              'Receptions': 'receptions',
              'FG Made': 'field_goals_made',
              'Pass TDs': 'passing_touchdowns',
              'Rush TDs': 'rushing_touchdowns',
              'Rec TDs': 'receiving_touchdowns',

              // Basketball common
              'Points': 'points',
              'Rebounds': 'rebounds',
              'Assists': 'assists',
              'Pts+Rebs+Asts': 'points_rebounds_assists',
              'Pts+Rebs': 'points_rebounds',
              'Pts+Ast': 'points_assists',
              'Rebs+Asts': 'rebounds_assists',
              'Fantasy Score': 'fantasy_score',

              // 3-point variations
              '3-PT Made': 'three_pointers_made',
              '3PT Made': 'three_pointers_made',
              '3PT': 'three_pointers_made',
              '3 Pointers Made': 'three_pointers_made'
            }

            const mappedType = propTypeMap[line] || line.toLowerCase().replace(/[\s\+\-]/g, '_')
            currentProp.prop_type = mappedType

            // Prop is complete, save it
            if (currentProp.player_name && currentProp.line_value !== undefined && currentProp.prop_type) {
              props.push(currentProp as PrizePicksProp)
              console.log(`✅ Saved complete prop: ${currentProp.player_name} ${mappedType} ${currentProp.line_value}`)
            }

            // Reset for next prop
            currentProp = {}
            state = 'seeking_player'
            continue
          }
        }



      } catch (error) {
        console.error(`Error parsing line "${line}":`, error)
      }
    }

    // Don't forget the last prop
    if (currentProp.player_name && currentProp.line_value !== undefined && currentProp.prop_type) {
      props.push(currentProp as PrizePicksProp)
    }

    // Auto-detect sport based on prop types (expand MLB coverage)
    for (const prop of props) {
      if (!prop.sport) {
        const t = prop.prop_type || ''
        if (t.includes('passing_') || t.includes('rushing_') || t.includes('receiving_') || t.includes('field_goals_')) {
          prop.sport = 'NFL'
        } else if (t.includes('points') || t.includes('rebounds') || t.includes('assists') || t.includes('three_pointers_made')) {
          prop.sport = 'NBA' // Basketball
        } else if (
          t.includes('pitcher_strikeouts') ||
          t.includes('walks_allowed') ||
          t.includes('total_bases') ||
          t.includes('hits_runs_rbis') ||
          t.includes('innings_pitched') ||
          t.includes('earned_runs_allowed') ||
          t.includes('pitcher_wins') ||
          t.includes('stolen_bases') ||
          t.includes('hits') ||
          t.includes('runs_batted_in') ||
          t.includes('runs_scored') ||
          t.includes('home_runs')
        ) {
          prop.sport = 'MLB'
        } else {
          prop.sport = 'NFL' // Default
        }
      }
    }

    if (props.length === 0) {
      return NextResponse.json({
        error: 'No valid props found in input. Expected format: "Player Name Over/Under Value Prop Type vs Opponent"'
      }, { status: 400 })
    }

    // Save props to database
    let savedCount = 0
    const errors: string[] = []

    for (const prop of props) {
      try {
        // Upsert by natural key: player + prop_type + game_time
        const { data: existing } = await adminSupabase
          .from('prizepicks_props')
          .select('id, line_value')
          .eq('player_name', prop.player_name)
          .eq('prop_type', prop.prop_type)
          .eq('game_time', prop.game_time)
          .single()

        if (existing) {
          // Update line if it changed; keep is_active true
          if (existing.line_value !== prop.line_value) {
            const { error: updateError } = await adminSupabase
              .from('prizepicks_props')
              .update({
                line_value: prop.line_value,
                is_active: true,
                scraped_at: new Date().toISOString()
              })
              .eq('id', existing.id)

            if (updateError) {
              console.error('Update error:', updateError)
              errors.push(`Failed to update ${prop.player_name}: ${updateError.message}`)
            } else {
              savedCount++
            }
          } else {
            // Ensure it's active even if unchanged
            await adminSupabase
              .from('prizepicks_props')
              .update({ is_active: true })
              .eq('id', existing.id)
          }
          continue
        }

        // Insert new prop (only include fields that exist in the database)
        const { error: insertError } = await adminSupabase
          .from('prizepicks_props')
          .insert({
            external_id: prop.external_id,
            sport: prop.sport,
            player_name: prop.player_name,
            team: prop.team,
            opponent: prop.opponent,
            prop_type: prop.prop_type,
            line_value: prop.line_value,
            game_time: prop.game_time,
            is_active: prop.is_active ?? true
          })

        if (insertError) {
          console.error('Insert error:', insertError)
          errors.push(`Failed to save ${prop.player_name}: ${insertError.message}`)
        } else {
          savedCount++
        }
      } catch (error) {
        console.error('Error saving prop:', error)
        errors.push(`Error saving ${prop.player_name}: ${error}`)
      }
    }

    return NextResponse.json({
      success: true,
      parsed: props.length,
      props: savedCount,
      raw_lines: lines.length,
      date: date || 'auto',
      errors: errors.length > 0 ? errors : undefined
    })

  } catch (error) {
    console.error('Parse input error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}