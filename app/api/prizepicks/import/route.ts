// API endpoint for manual sports props import
// Handles NFL, WNBA, MLB, NBA data pasting

import { NextRequest, NextResponse } from 'next/server'
import { importManualProps, batchImportProps, detectSportFromData, formatImportSummary } from '@/lib/prizepicks/data-importer'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { sport, data, format = 'prizepicks', auto_detect = false } = body

    if (!data || typeof data !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Data is required and must be a string' },
        { status: 400 }
      )
    }

    let targetSport = sport

    // Auto-detect sport if requested
    if (auto_detect && !sport) {
      targetSport = detectSportFromData(data)
      if (!targetSport) {
        return NextResponse.json(
          { success: false, error: 'Could not auto-detect sport from data. Please specify sport manually.' },
          { status: 400 }
        )
      }
    }

    if (!targetSport) {
      return NextResponse.json(
        { success: false, error: 'Sport is required (NFL, WNBA, MLB, NBA)' },
        { status: 400 }
      )
    }

    // Validate sport
    const validSports = ['NFL', 'WNBA', 'MLB', 'NBA']
    if (!validSports.includes(targetSport.toUpperCase())) {
      return NextResponse.json(
        { success: false, error: `Invalid sport. Must be one of: ${validSports.join(', ')}` },
        { status: 400 }
      )
    }

    console.log(`Starting manual import for ${targetSport}`)
    console.log(`Data preview: ${data.substring(0, 200)}...`)

    // Import the data
    const result = await importManualProps(targetSport.toUpperCase(), data)

    console.log('Import completed:', result)

    return NextResponse.json({
      success: result.success,
      message: `Import completed: ${result.imported} props imported, ${result.skipped} skipped`,
      data: {
        sport: targetSport.toUpperCase(),
        imported: result.imported,
        skipped: result.skipped,
        errors: result.errors,
        props: result.props.map(prop => ({
          player_name: prop.player_name,
          team: prop.team,
          prop_type: prop.prop_type,
          line_value: prop.line_value
        }))
      }
    })

  } catch (error) {
    console.error('Import API error:', error)

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Import failed'
      },
      { status: 500 }
    )
  }
}

// Batch import endpoint
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { imports } = body

    if (!Array.isArray(imports) || imports.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Imports array is required' },
        { status: 400 }
      )
    }

    // Validate each import
    for (const importData of imports) {
      if (!importData.sport || !importData.data) {
        return NextResponse.json(
          { success: false, error: 'Each import must have sport and data fields' },
          { status: 400 }
        )
      }
    }

    console.log(`Starting batch import for ${imports.length} datasets`)

    const results = await batchImportProps(imports)
    const summary = formatImportSummary(results)

    console.log('Batch import completed:', summary)

    return NextResponse.json({
      success: results.some(r => r.success),
      message: summary,
      data: {
        results: results.map(result => ({
          imported: result.imported,
          skipped: result.skipped,
          errors: result.errors.length,
          success: result.success
        })),
        total_imported: results.reduce((sum, r) => sum + r.imported, 0),
        total_skipped: results.reduce((sum, r) => sum + r.skipped, 0),
        total_errors: results.reduce((sum, r) => sum + r.errors.length, 0)
      }
    })

  } catch (error) {
    console.error('Batch import API error:', error)

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Batch import failed'
      },
      { status: 500 }
    )
  }
}
