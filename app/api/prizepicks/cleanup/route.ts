import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { createSupabaseAdminClient } from '@/lib/supabase/admin'

// Run full cleanup/archiving pipeline via RPC
export async function POST(request: NextRequest) {
  try {
    // Allow either: (1) authenticated user; or (2) Vercel Cron with shared secret
    const cronKey = request.headers.get('x-cron-key') || ''
    const secret = process.env.PRIZEPICKS_CLEANUP_KEY || ''
    const allowCron = secret && cronKey === secret

    if (!allowCron) {
      const supabase = await createServerSupabaseClient()
      const { data: { user }, error: authError } = await supabase.auth.getUser()
      if (authError || !user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }
    }

    const admin = createSupabaseAdminClient()

    const { error } = await admin.rpc('run_prizepicks_cleanup')
    if (error) {
      console.error('run_prizepicks_cleanup RPC error:', error)
      return NextResponse.json({ ok: false, error: error.message }, { status: 500 })
    }

    return NextResponse.json({ ok: true })
  } catch (error) {
    console.error('Cleanup route error:', error)
    return NextResponse.json({ ok: false, error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 })
}

