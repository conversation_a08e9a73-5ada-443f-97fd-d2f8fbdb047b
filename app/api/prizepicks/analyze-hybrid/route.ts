import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/server'
import { analyzeWithDeepSeek, hybridAnalysis } from '@/lib/analysis/deepseek-analyzer'
import OpenAI from 'openai'

// Initialize DeepSeek
const deepseek = new OpenAI({
  baseURL: 'https://api.deepseek.com',
  apiKey: process.env.DEEPSEEK_API_KEY
})

interface PropData {
  player_name: string
  team: string
  opponent?: string
  sport: string
  prop_type: string
  line_value: number
  game_time: string
}

// Simulate getting player stats (you'd replace this with actual data fetching)
async function getPlayerStats(player: string, sport: string, propType: string) {
  // This is where you'd fetch from your downloaded CSV data or API
  // For now, returning mock data structure
  return {
    seasonStats: { [propType]: 25.4 }, // Season average
    last5Games: [28, 22, 31, 19, 27], // Last 5 games
    vsOpponentHistory: [23, 29, 21] // vs this opponent
  }
}

async function getOpponentDefenseStats(opponent: string, sport: string, propType: string) {
  // Mock opponent defensive data
  return {
    defensiveRanking: 15, // 1-32 ranking
    allowedPerGame: 22.8 // Average allowed to position
  }
}

async function getContextualData(sport: string, gameTime: string) {
  // Mock contextual data - you'd get this from APIs or Claude search
  return {
    weather: sport === 'NFL' ? 'Clear, 72°F, 5mph winds' : undefined,
    injuries: [],
    gameScript: 'Projected close game, normal pace'
  }
}

export async function POST(request: NextRequest) {
  try {
    const { propId, useHybrid = false } = await request.json()

    if (!propId) {
      return NextResponse.json({ error: 'Prop ID required' }, { status: 400 })
    }

    const supabase = await createSupabaseServerClient()

    // Get prop details
    const { data: prop, error: propError } = await supabase
      .from('prizepicks_props')
      .select('*')
      .eq('id', propId)
      .single()

    if (propError || !prop) {
      return NextResponse.json({ error: 'Prop not found' }, { status: 404 })
    }

    // Check for recent analysis (within 4 hours)
    const { data: existingAnalysis } = await supabase
      .from('ai_analyses')
      .select('*')
      .eq('prop_id', propId)
      .gte('created_at', new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    if (existingAnalysis) {
      return NextResponse.json(existingAnalysis)
    }

    console.log(`Analyzing with DeepSeek: ${prop.player_name} ${prop.prop_type} ${prop.line_value}`)

    // Gather data for analysis
    const [playerStats, opponentStats, contextData] = await Promise.all([
      getPlayerStats(prop.player_name, prop.sport, prop.prop_type),
      prop.opponent ? getOpponentDefenseStats(prop.opponent, prop.sport, prop.prop_type) : null,
      getContextualData(prop.sport, prop.game_time)
    ])

    // Prepare data for DeepSeek
    const analysisData = {
      player: {
        name: prop.player_name,
        position: getPositionFromPropType(prop.prop_type),
        team: prop.team || '',
        seasonStats: playerStats.seasonStats,
        last5Games: playerStats.last5Games,
        vsOpponentHistory: playerStats.vsOpponentHistory
      },
      opponent: {
        name: prop.opponent || '',
        defensiveRanking: opponentStats?.defensiveRanking || 16,
        allowedPerGame: opponentStats?.allowedPerGame || 20
      },
      prop: {
        type: prop.prop_type,
        line: prop.line_value,
        gameTime: prop.game_time
      },
      context: contextData
    }

    // Run analysis
    const analysisResult = useHybrid
      ? await hybridAnalysis(analysisData, true)
      : await analyzeWithDeepSeek(analysisData)

    // Save analysis to database
    const { data: savedAnalysis, error: saveError } = await supabase
      .from('ai_analyses')
      .insert([
        {
          prop_id: propId,
          analysis_text: analysisResult.analysis_text,
          confidence_rating: analysisResult.confidence_rating,
          recommendation: analysisResult.recommendation,
          reasoning_points: analysisResult.reasoning_points,
          created_at: new Date().toISOString(),
          expires_at: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString()
        }
      ])
      .select()
      .single()

    if (saveError) {
      console.error('Failed to save analysis:', saveError)
      return NextResponse.json({ error: 'Failed to save analysis' }, { status: 500 })
    }

    // Add cost info for debugging
    const response = {
      ...savedAnalysis,
      cost_estimate: analysisResult.cost_estimate,
      analysis_method: useHybrid ? 'DeepSeek + Claude' : 'DeepSeek'
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Analysis error:', error)

    if (error instanceof Error && error.message.includes('rate limit')) {
      return NextResponse.json(
        { error: 'AI service temporarily unavailable - please try again in a moment' },
        { status: 429 }
      )
    }

    return NextResponse.json(
      { error: 'Analysis failed - please try again' },
      { status: 500 }
    )
  }
}

function getPositionFromPropType(propType: string): string {
  if (propType.includes('passing')) return 'QB'
  if (propType.includes('rushing')) return 'RB'
  if (propType.includes('receiving')) return 'WR'
  if (propType.includes('receptions')) return 'WR'
  return 'PLAYER'
}