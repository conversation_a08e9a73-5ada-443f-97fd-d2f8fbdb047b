import { NextResponse } from 'next/server'
import { createSupabaseAdminClient } from '@/lib/supabase/admin'

export async function GET() {
  try {
    const supabase = createSupabaseAdminClient()
    
    const now = new Date().toISOString()

    // Test without time filter first
    const { data: allProps } = await supabase
      .from('prizepicks_props')
      .select('*')
      .eq('is_active', true)
      .limit(10)

    // Test with time filter
    const { data, error } = await supabase
      .from('prizepicks_props')
      .select('*')
      .eq('is_active', true)
      .gte('game_time', now)
      .limit(10)
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }
    
    return NextResponse.json({
      props: data,
      count: data?.length || 0,
      allProps: allProps,
      allCount: allProps?.length || 0,
      now: now,
      comparison: allProps?.map(p => ({
        player: p.player_name,
        game_time: p.game_time,
        is_future: p.game_time > now
      }))
    })
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch props' }, { status: 500 })
  }
}
