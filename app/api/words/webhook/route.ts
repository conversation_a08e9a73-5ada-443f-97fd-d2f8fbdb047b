import { NextRequest } from "next/server";
import { createClient } from "@supabase/supabase-js";
import <PERSON><PERSON> from "stripe";

export const runtime = "nodejs";

function createSbEdge() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    { auth: { persistSession: false } }
  );
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.text();
    const sig = req.headers.get("stripe-signature");
    
    if (!sig) {
      return new Response("Missing signature", { status: 400 });
    }

    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: "2025-08-27.basil"
    });

    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(
        body,
        sig,
        process.env.STRIPE_WEBHOOK_SECRET_WORDS || process.env.STRIPE_WEBHOOK_SECRET!
      );
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return new Response("Invalid signature", { status: 400 });
    }

    const supabase = createSbEdge();

    if (event.type === "checkout.session.completed") {
      const session = event.data.object as Stripe.Checkout.Session;
      const userId = session.metadata?.userId;
      const words = Number(session.metadata?.words ?? 0);
      const pack = session.metadata?.pack;
      const planType = session.metadata?.planType;

      if (!userId) {
        console.error('Missing userId in session metadata:', session.id);
        return new Response("Missing userId", { status: 400 });
      }

      // Handle unlimited subscription vs one-time word purchase differently
      if (planType === "unlimited_subscription") {
        // For subscriptions, set words to -1 (unlimited) and update tier
        try {
          const { error } = await supabase
            .from('genyus_user_words')
            .upsert({
              user_id: userId,
              words_remaining: -1, // Unlimited
              tier: 'unlimited',
              stripe_subscription_id: session.subscription,
              subscription_status: 'active',
              updated_at: new Date().toISOString()
            });

          if (error) {
            console.error('Error updating subscription:', error);
            return new Response("Database error", { status: 500 });
          }

          console.log(`Unlimited subscription activated for user: ${userId}`);
        } catch (err) {
          console.error('Subscription processing error:', err);
          return new Response("Processing error", { status: 500 });
        }
      } else if (!words) {
        console.error('Missing words in session metadata:', session.id);
        return new Response("Missing words", { status: 400 });
      }

      // Check if we've already processed this session (idempotency)
      const { data: existingPurchase } = await supabase
        .from('genyus_word_purchases')
        .select('id')
        .eq('stripe_session_id', session.id)
        .single();

      if (existingPurchase) {
        console.log('Session already processed:', session.id);
        return new Response("Already processed", { status: 200 });
      }

      try {
        // Insert purchase record
        const { error: purchaseError } = await supabase
          .from('genyus_word_purchases')
          .insert({
            user_id: userId,
            stripe_session_id: session.id,
            price_id: session.metadata?.priceId || 'unknown',
            words_granted: words
          });

        if (purchaseError) {
          console.error('Purchase insert error:', purchaseError);
          return new Response("Database error", { status: 500 });
        }

        // Add words to user balance
        const { error: balanceError } = await supabase
          .from('genyus_user_words')
          .upsert({
            user_id: userId,
            words_remaining: words,
            tier: pack?.toLowerCase() || 'starter'
          }, {
            onConflict: 'user_id',
            ignoreDuplicates: false
          });

        if (balanceError) {
          // If upsert failed, try to increment existing balance
          const { error: incrementError } = await supabase.rpc('increment_user_words', {
            p_user_id: userId,
            p_words: words
          });

          if (incrementError) {
            console.error('Balance update error:', incrementError);
            return new Response("Balance update failed", { status: 500 });
          }
        }

        console.log(`Successfully processed purchase for user ${userId}: ${words} words`);

      } catch (error) {
        console.error('Transaction error:', error);
        return new Response("Transaction failed", { status: 500 });
      }
    }

    // Handle subscription status changes
    if (event.type === "customer.subscription.updated" || event.type === "customer.subscription.deleted") {
      const subscription = event.data.object as Stripe.Subscription;
      const userId = subscription.metadata?.userId;

      if (!userId) {
        console.error('Missing userId in subscription metadata:', subscription.id);
        return new Response("Missing userId", { status: 400 });
      }

      const newStatus = subscription.status;
      console.log(`Subscription ${subscription.id} status changed to: ${newStatus} for user: ${userId}`);

      // Update subscription status in database
      if (newStatus === 'canceled' || newStatus === 'unpaid' || newStatus === 'past_due') {
        // Downgrade to free tier
        const { error } = await supabase
          .from('genyus_user_words')
          .update({
            words_remaining: 3000, // Free monthly allowance
            tier: 'free',
            subscription_status: newStatus,
            updated_at: new Date().toISOString()
          })
          .eq('stripe_subscription_id', subscription.id);

        if (error) {
          console.error('Error downgrading subscription:', error);
          return new Response("Database error", { status: 500 });
        }
      } else if (newStatus === 'active') {
        // Reactivate unlimited
        const { error } = await supabase
          .from('genyus_user_words')
          .update({
            words_remaining: -1,
            tier: 'unlimited',
            subscription_status: 'active',
            updated_at: new Date().toISOString()
          })
          .eq('stripe_subscription_id', subscription.id);

        if (error) {
          console.error('Error reactivating subscription:', error);
          return new Response("Database error", { status: 500 });
        }
      }
    }

    return new Response("OK", { status: 200 });

  } catch (error) {
    console.error('Webhook error:', error);
    return new Response("Internal error", { status: 500 });
  }
}
