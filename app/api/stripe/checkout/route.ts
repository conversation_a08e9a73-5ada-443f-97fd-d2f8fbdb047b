import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { stripe, calculateApplicationFee } from '@/lib/stripe'
import type { Stripe } from 'stripe'

export async function POST(request: NextRequest) {
  try {
    const { writerId, paymentType = 'subscription', amount, message, entryId, isStoryVenture } = await request.json()
    
    const supabase = await createSupabaseServerClient()
    
    // Get authenticated user (the payer)
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get writer information
    const { data: writer, error: writerError } = await supabase
      .from('users')
      .select('*')
      .eq('id', writerId)
      .single()

    if (writerError || !writer) {
      return NextResponse.json({ error: 'Writer not found' }, { status: 404 })
    }

    // Check if writer has completed Stripe Connect onboarding
    if (!writer.stripe_account_id || !writer.stripe_onboarding_complete) {
      return NextResponse.json({
        error: 'Writer has not completed payment setup. Please contact the writer.'
      }, { status: 400 })
    }

    // Determine amount
    let totalAmount: number
    if (paymentType === 'subscription') {
      totalAmount = writer.price_monthly || 999 // Default $9.99
    } else if (paymentType === 'donation') {
      totalAmount = amount
      if (!totalAmount || totalAmount < 100) {
        return NextResponse.json({ error: 'Minimum donation is $1.00' }, { status: 400 })
      }
    } else {
      return NextResponse.json({ error: 'Invalid payment type' }, { status: 400 })
    }

    // Calculate application fee using new "Percentage + Fixed Fee" model
    const { applicationFeeAmount, writerAmount } = calculateApplicationFee(totalAmount, paymentType)

    console.log('Payment setup:', {
      totalAmount,
      paymentType,
      applicationFeeAmount,
      writerAmount,
      applicationFeeInDollars: applicationFeeAmount / 100,
      writerAmountInDollars: writerAmount / 100
    })

    // Create Stripe Checkout Session
    const sessionConfig: Stripe.Checkout.SessionCreateParams = {
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: paymentType === 'subscription'
                ? `30 Posts from ${writer.name}`
                : `Donation to ${writer.name}`,
              description: paymentType === 'subscription'
                ? 'Access to 30 diary entries'
                : message || 'Thank you for your support!',
            },
            unit_amount: totalAmount,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/u/${writerId}`,
      metadata: {
        payer_id: user.id,
        writer_id: writerId,
        payment_type: paymentType,
      }
    }

    // Add Stripe Connect payment configuration
    sessionConfig.payment_intent_data = {
      application_fee_amount: applicationFeeAmount,
      transfer_data: {
        destination: writer.stripe_account_id,
      },
      metadata: {
        payer_id: user.id,
        writer_id: writerId,
        payment_type: paymentType,
        product_type: paymentType, // Add this for Stripe pricing rules
        platform_fee: applicationFeeAmount.toString(),
        writer_amount: writerAmount.toString(),
        ...(message && { donation_message: message }),
        ...(isStoryVenture && entryId && {
          is_story_venture: 'true',
          entry_id: entryId
        })
      }
    }

    console.log('Stripe session config:', {
      application_fee_amount: applicationFeeAmount,
      destination: writer.stripe_account_id,
      total_amount: totalAmount
    })

    // Verify the Stripe account exists before creating checkout session
    try {
      await stripe.accounts.retrieve(writer.stripe_account_id)
    } catch (accountError) {
      console.error('Stripe account verification failed:', accountError)
      return NextResponse.json(
        { error: 'Writer payment setup is invalid. Please contact the writer to update their payment settings.' },
        { status: 400 }
      )
    }

    const session = await stripe.checkout.sessions.create(sessionConfig)

    return NextResponse.json({ url: session.url })

  } catch (error: any) {
    console.error('Checkout error:', error)

    // Handle specific Stripe errors
    if (error && error.type === 'StripeInvalidRequestError') {
      if (error.code === 'resource_missing' && error.param?.includes('destination')) {
        return NextResponse.json(
          { error: 'Writer payment setup is incomplete. Please contact the writer to complete their Stripe setup.' },
          { status: 400 }
        )
      }
      if (error.code === 'url_invalid') {
        return NextResponse.json(
          { error: 'Payment configuration error. Please try again.' },
          { status: 500 }
        )
      }
    }

    // Generic error for other issues
    return NextResponse.json(
      { error: 'Failed to create donation checkout. Please try again.' },
      { status: 500 }
    )
  }
}
