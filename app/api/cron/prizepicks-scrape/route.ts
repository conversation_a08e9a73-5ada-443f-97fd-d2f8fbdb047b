// Vercel Cron Job for PrizePicks Scraping
// Add this to vercel.json:
// {
//   "crons": [
//     {
//       "path": "/api/cron/prizepicks-scrape",
//       "schedule": "0 */3 * * *"
//     }
//   ]
// }

import { NextRequest, NextResponse } from 'next/server'
import { updatePrizePicksDatabase } from '@/lib/prizepicks/scraper'
import { createSupabaseServerClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    // Verify this is a legitimate cron request
    const authHeader = request.headers.get('authorization')
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('Starting scheduled PrizePicks scrape...')

    // Scrape all sports
    const result = await updatePrizePicksDatabase()

    // Clean up old data while we're here
    const supabase = await createSupabaseServerClient()
    await supabase.rpc('cleanup_old_prizepicks_data')

    console.log('Scheduled scrape completed:', result)

    return NextResponse.json({
      success: true,
      message: 'Scheduled scrape completed',
      timestamp: new Date().toISOString(),
      result
    })

  } catch (error) {
    console.error('Scheduled scrape failed:', error)

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Scheduled scrape failed',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// Also allow manual triggering via POST
export async function POST(request: NextRequest) {
  return GET(request)
}