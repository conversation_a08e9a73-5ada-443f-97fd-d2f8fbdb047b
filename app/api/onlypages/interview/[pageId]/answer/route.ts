import { NextResponse } from 'next/server'
import { z } from 'zod'
import { createSupabaseServerClient } from '@/lib/supabase/client'

const bodySchema = z.object({
  question_id: z.string().min(1),
  answer: z.string().optional().default(''),
  skip: z.boolean().optional().default(false),
  question_text: z.string().optional()
})

export async function POST(req: Request, { params }: { params: Promise<{ pageId: string }> }) {
  try {
    const { pageId } = await params
    const json = await req.json().catch(() => ({}))
    const parsed = bodySchema.safeParse(json)
    if (!parsed.success) return NextResponse.json({ error: 'Invalid body' }, { status: 400 })

    const supabase = await createSupabaseServerClient()
    const { data: auth } = await supabase.auth.getUser()
    const user = auth?.user
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    // Verify ownership
    const { data: page } = await supabase
      .from('onlypages')
      .select('id, user_id')
      .eq('id', pageId)
      .single()

    if (!page || page.user_id !== user.id) return NextResponse.json({ error: 'Not found' }, { status: 404 })

    // Update existing row for this question; if missing, insert with minimal fields
    const { data: existing } = await supabase
      .from('onlypages_interview')
      .select('id')
      .eq('page_id', pageId)
      .eq('question_id', parsed.data.question_id)
      .single()

    const toSave = parsed.data.skip ? '' : (parsed.data.answer || '')

    if (existing) {
      const { error: updateErr } = await supabase
        .from('onlypages_interview')
        .update({ answer_text: toSave })
        .eq('id', existing.id)
      if (updateErr) return NextResponse.json({ error: updateErr.message }, { status: 500 })
    } else {
      const { error: insertErr } = await supabase
        .from('onlypages_interview')
        .insert({
          page_id: pageId,
          question_id: parsed.data.question_id,
          question_text: parsed.data.question_text ?? (parsed.data.question_id === 'pre' ? 'Quick context' : '(auto)'),
          answer_text: toSave,
          is_optional: true,
          order_index: parsed.data.question_id === 'pre' ? -1 : 0
        })
      if (insertErr) return NextResponse.json({ error: insertErr.message }, { status: 500 })
    }

    return NextResponse.json({ ok: true })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Server error' }, { status: 500 })
  }
}

