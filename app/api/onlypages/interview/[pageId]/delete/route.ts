import { NextResponse } from 'next/server'
import { z } from 'zod'
import { createSupabaseServerClient } from '@/lib/supabase/client'

const bodySchema = z.object({
  question_id: z.string().min(1)
})

export async function POST(req: Request, { params }: { params: Promise<{ pageId: string }> }) {
  try {
    const { pageId } = await params

    const supabase = await createSupabaseServerClient()
    const { data: auth } = await supabase.auth.getUser()
    const user = auth?.user
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    // Verify ownership
    const { data: page } = await supabase
      .from('onlypages')
      .select('id, user_id')
      .eq('id', pageId)
      .single()

    if (!page || page.user_id !== user.id) return NextResponse.json({ error: 'Not found' }, { status: 404 })

    const body = await req.json().catch(() => ({}))
    const parsed = bodySchema.safeParse(body)
    if (!parsed.success) return NextResponse.json({ error: 'Invalid input' }, { status: 400 })

    const { question_id } = parsed.data

    const { error } = await supabase
      .from('onlypages_interview')
      .delete()
      .eq('page_id', pageId)
      .eq('question_id', question_id)

    if (error) return NextResponse.json({ error: error.message }, { status: 500 })

    return NextResponse.json({ ok: true })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Server error' }, { status: 500 })
  }
}

