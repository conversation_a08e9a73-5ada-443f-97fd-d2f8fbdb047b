import { NextResponse } from 'next/server'
import { z } from 'zod'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { callGroqDirect, callDeepSeekDirect } from '@/lib/genyus/providers'
import { toStrictJson } from '@/lib/utils'

const bodySchema = z.object({
  context: z.string().min(1).max(500), // What they want to be asked about
  followup_count: z.number().min(1).max(3).default(1) // How many follow-ups they've done
})

const InterviewQuestionsSchema = z.object({
  questions: z.array(z.object({
    id: z.string(),
    text: z.string(),
    intent: z.string().optional(),
    expected: z.string().optional(),
    optional: z.boolean().optional()
  }))
})

export async function POST(req: Request, { params }: { params: Promise<{ pageId: string }> }) {
  try {
    const { pageId } = await params
    const json = await req.json().catch(() => ({}))
    const parsed = bodySchema.safeParse(json)
    if (!parsed.success) return NextResponse.json({ error: 'Invalid body', details: parsed.error.format() }, { status: 400 })

    const supabase = await createSupabaseServerClient()
    const { data: auth } = await supabase.auth.getUser()
    const user = auth?.user
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    // Verify ownership
    const { data: page } = await supabase
      .from('onlypages')
      .select('id, user_id, type')
      .eq('id', pageId)
      .single()

    if (!page || page.user_id !== user.id) return NextResponse.json({ error: 'Not found' }, { status: 404 })

    // Load existing Q&A for context
    const { data: qa } = await supabase
      .from('onlypages_interview')
      .select('question_id, question_text, answer_text, is_optional, order_index')
      .eq('page_id', pageId)
      .order('order_index', { ascending: true })

    const answeredQAs = (qa || []).filter(q => q.answer_text !== null && q.answer_text !== undefined)
    const conversationFlow = answeredQAs.map((q, index) => 
      `${index + 1}. Q: "${q.question_text}"\n   A: "${q.answer_text}"`
    ).join('\n\n')

    const FOLLOWUP_SYS = `You are a WORLD-CLASS INTERVIEWER conducting a targeted follow-up based on the user's specific request.

The user has completed the core interview but wants to explore a specific aspect of their story in more depth. Your job is to ask 1-2 brilliant follow-up questions that dive deep into exactly what they want to discuss.

FOLLOW-UP EXCELLENCE:
- Ask questions that go deeper into their specified topic
- Build on what they've already shared in the main interview
- Create questions that will generate quotable, memorable content
- Focus on the human story, emotional truth, and unique insights
- Avoid repeating what was already covered in the main interview

QUESTION QUALITY:
- Each question should feel like "exactly what I wanted to be asked about"
- Generate content that adds significant value to the final article
- Focus on specifics, emotions, and proof points
- Create moments that reveal character and insight

Return ONLY valid JSON with 1-2 questions maximum. Format:
{
  "questions": [
    {
      "id": "unique_string_id",
      "text": "Your targeted follow-up question?",
      "intent": "followup",
      "expected": "paragraph|story",
      "optional": false
    }
  ]
}`

    const userPrompt = `USER-DIRECTED FOLLOW-UP for ${page.type}:

MAIN INTERVIEW COMPLETED:
${conversationFlow || '(No previous Q&A)'}

USER'S FOLLOW-UP REQUEST:
"${parsed.data.context}"

FOLLOW-UP MISSION:
The user specifically wants to be asked about: "${parsed.data.context}"

Create 1-2 brilliant follow-up questions that:
1. Dive deep into exactly what they want to discuss
2. Build on the foundation from the main interview
3. Generate content that will significantly enhance the final article
4. Focus on the human story, emotions, and unique insights around this topic
5. Avoid repeating what was already covered

This is follow-up #${parsed.data.followup_count} of 3 maximum, so make it count.

Return JSON with 1-2 targeted questions that explore their requested topic in depth.`

    // Use Groq for fast follow-up generation with DeepSeek fallback
    let getFinalText
    try {
      console.log('🚀 Generating follow-up questions with Groq...')
      const groqResult = await callGroqDirect(userPrompt, [
        { role: 'system', content: FOLLOWUP_SYS }
      ], { temperature: 0.3, max_tokens: 500 })
      getFinalText = groqResult.getFinalText
    } catch (error) {
      console.warn('⚠️ Groq failed for follow-up, using DeepSeek:', error.message)
      const deepseekResult = await callDeepSeekDirect(userPrompt, [
        { role: 'system', content: FOLLOWUP_SYS }
      ], { sessionLength: (qa || []).length })
      getFinalText = deepseekResult.getFinalText
    }

    const raw = await getFinalText()
    console.log('🎯 Follow-up DeepSeek response:', raw.substring(0, 500))

    const parsedJson = toStrictJson(raw)
    if (!parsedJson) {
      return NextResponse.json({
        error: 'AI service temporarily unavailable. Please try again.'
      }, { status: 503 })
    }

    const validated = InterviewQuestionsSchema.safeParse(parsedJson)
    if (!validated.success) {
      return NextResponse.json({ error: 'Invalid questions generated' }, { status: 500 })
    }

    let questions = validated.data.questions
    if (questions.length > 2) questions = questions.slice(0, 2)

    // Ensure uniqueness vs existing questions
    const existingTexts = new Set((qa || []).map(q => (q.question_text || '').trim().toLowerCase()))
    const filtered = questions.filter(q => !existingTexts.has((q.text || '').trim().toLowerCase()))

    if (filtered.length === 0) {
      return NextResponse.json({
        error: 'Unable to generate unique follow-up questions. Try rephrasing your request.'
      }, { status: 400 })
    }

    // Insert the follow-up questions
    const baseOrder = (qa?.length || 0)
    const upserts = filtered.map((q, i) => ({
      page_id: pageId,
      question_id: q.id,
      question_text: q.text,
      is_optional: true,
      order_index: baseOrder + i
    }))

    for (const row of upserts) {
      await supabase.from('onlypages_interview').insert(row)
    }

    console.log('✅ Follow-up questions generated:', filtered.map(q => `"${q.text}"`))

    return NextResponse.json({ questions: filtered })
  } catch (e: any) {
    console.error('Follow-up generation error:', e)
    return NextResponse.json({ error: e?.message || 'Server error' }, { status: 500 })
  }
}
