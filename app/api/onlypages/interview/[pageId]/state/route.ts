import { NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function GET(req: Request, { params }: { params: Promise<{ pageId: string }> }) {
  try {
    const { pageId } = await params

    const supabase = await createSupabaseServerClient()
    const { data: auth } = await supabase.auth.getUser()
    const user = auth?.user
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    // Verify ownership
    const { data: page } = await supabase
      .from('onlypages')
      .select('id, user_id')
      .eq('id', pageId)
      .single()

    if (!page || page.user_id !== user.id) return NextResponse.json({ error: 'Not found' }, { status: 404 })

    const { data: qa, error } = await supabase
      .from('onlypages_interview')
      .select('question_id, question_text, answer_text, is_optional, order_index')
      .eq('page_id', pageId)
      .order('order_index', { ascending: true })

    if (error) return NextResponse.json({ error: error.message }, { status: 500 })

    const qaCore = (qa || []).filter(q => q.question_id !== 'pre')
    const answered_count = qaCore.filter(q => q.answer_text !== null && q.answer_text !== undefined).length

    return NextResponse.json({
      qa: qaCore,
      answered_count,
      total: qaCore.length
    })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Server error' }, { status: 500 })
  }
}

