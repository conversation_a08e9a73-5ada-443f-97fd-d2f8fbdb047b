import { NextResponse } from 'next/server'
import { z } from 'zod'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { callGroqDirect, callDeepSeekDirect } from '@/lib/genyus/providers'
import { InterviewQuestionsSchema } from '@/lib/onlypages/schemas'

// Simple in-memory cache for question generation
const questionCache = new Map<string, { questions: any[], timestamp: number }>()
const CACHE_TTL = 5 * 60 * 1000 // 5 minutes

const bodySchema = z.object({
  context: z.object({
    type: z.string().optional(),
    known_fields: z.record(z.string(), z.any()).optional(),
  }).optional().default({})
})

// Robust JSON extractor: returns parsed object if a top-level JSON block exists; otherwise null
function toStrictJson(content: string): any | null {
  if (typeof content !== 'string') return null
  const start = content.indexOf('{')
  const end = content.lastIndexOf('}')
  if (start !== -1 && end !== -1 && end > start) {
    try { return JSON.parse(content.slice(start, end + 1)) } catch {}
  }
  return null
}

// Create a stable question_id from the question text to avoid duplicates across retries
function stableQuestionIdFromText(text: string): string {
  const s = (text || '')
    .toLowerCase()
    .normalize('NFKD')
    .replace(/[^a-z0-9\s-]/g, '')
    .trim()
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '')
  return (s || 'q').slice(0, 64)
}

// Calculate similarity between two strings using Jaccard similarity
function calculateSimilarity(str1: string, str2: string): number {
  const words1 = new Set(str1.toLowerCase().split(/\s+/))
  const words2 = new Set(str2.toLowerCase().split(/\s+/))

  const intersection = new Set([...words1].filter(x => words2.has(x)))
  const union = new Set([...words1, ...words2])

  return intersection.size / union.size
}


export async function POST(req: Request, { params }: { params: Promise<{ pageId: string }> }) {
  try {
    const { pageId } = await params

    const supabase = await createSupabaseServerClient()
    const { data: auth } = await supabase.auth.getUser()
    const user = auth?.user
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    // Verify ownership and load type
    const { data: page } = await supabase
      .from('onlypages')
      .select('id, user_id, type')
      .eq('id', pageId)
      .single()

    if (!page || page.user_id !== user.id) return NextResponse.json({ error: 'Not found' }, { status: 404 })

    // Load existing answers in order
    const { data: qa } = await supabase
      .from('onlypages_interview')
      .select('question_id, question_text, answer_text, is_optional, order_index')
      .eq('page_id', pageId)
      .order('order_index', { ascending: true })


    // Exclude the pre-seed from core interview logic to avoid misclassifying the stage
    const qaCore = (qa || []).filter(q => q.question_id !== 'pre')

    // Consider any non-null answer_text (including empty string) as answered, excluding the 'pre' seed
    const answeredCount = qaCore.filter(q => q.answer_text !== null && q.answer_text !== undefined).length

    // STRATEGIC INTERVIEW FLOW: Cap at 7 core questions, then allow user-directed follow-ups
    const MAX_CORE_QUESTIONS = 7
    const desiredCount = answeredCount === 0 ? 1 : 1 // start with 1 natural opener, then 1-by-1 dynamically

    // If we've reached the cap, don't generate more questions automatically
    if (answeredCount >= MAX_CORE_QUESTIONS) {
      return NextResponse.json({
        questions: [],
        interview_complete: true,
        message: "Core interview complete. Ready for user-directed follow-ups."
      })
    }

    // Idempotency guard: if there are unanswered questions (answer_text IS NULL), return the next one
    const unanswered = qaCore
      .filter(q => q.answer_text === null || q.answer_text === undefined)
      .sort((a, b) => (a.order_index ?? 0) - (b.order_index ?? 0))
    if (unanswered.length > 0) {
      const nx = unanswered[0]
      return NextResponse.json({ questions: [{ id: nx.question_id, text: nx.question_text, optional: !!nx.is_optional }] })
    }

    const INTERVIEW_SYS = `You are an expert interviewer creating compelling questions. Extract the most engaging story possible.

PRINCIPLES:
- Ask questions that reveal character, motivation, and unique insights
- Focus on specific moments and emotional truth
- Avoid generic questions - make each one specific to their story

REQUIREMENTS:
- One focused question at a time
- Conversational and natural tone
- Return ONLY valid JSON, no other text

Format:
{
  "questions": [
    {
      "id": "unique_string_id",
      "text": "Your question here?",
      "intent": "origin|vision|detail|emotion|proof|impact|story|cta",
      "expected": "short|paragraph|story|url|list",
      "optional": false
    }
  ]
}

Generate exactly 1 question in JSON format.`

    const body = await req.json().catch(() => ({}))
    const _parsed = bodySchema.safeParse(body)

    // Load existing answers to understand context and narrative flow
    const existingAnswers = (qa || []).reduce((acc: any, q) => ({
      ...acc,
      [q.question_id]: q.answer_text || ''
    }), {})

    // Get pre-context if it exists (the one-liner they provided)
    const preContext = existingAnswers['pre'] || ''

    // Content-type specific guidance
    const typeGuidance: Record<string, string> = {
      press: 'Focus on breakthrough moments and industry impact.',
      news: 'Focus on immediate relevance and human impact.',
      book: 'Focus on personal transformation and reader value.',
      movie: 'Focus on emotional journey and universal themes.',
      album: 'Focus on emotional catalyst and listener experience.',
      site: 'Focus on problem solving and user transformation.',
      life: 'Focus on universal struggles and turning points.',
      wiki: 'Focus on defining moments and personal legacy.',
      custom: 'Focus on human truth and emotional connection.'
    }

    // Build a simpler, clearer prompt for DeepSeek
    let userPrompt = ''

    if (answeredCount === 0) {
      userPrompt = `Create an opening interview question for a ${page.type}.

Context: "${preContext || 'No context provided'}"

Guidance: ${typeGuidance[page.type] || typeGuidance.custom}

Requirements:
- Ask them to introduce their creation in their own words
- Conversational tone, avoid corporate language
- Return exactly 1 question in JSON format
- Avoid clichés like "pain point", "digital landscape"`
    } else {
      // Get ALL answered questions for context
      const answeredQAs = qaCore.filter(q => q.answer_text !== null && q.answer_text !== undefined)

      // Build conversation context (last 3 for context, but list ALL questions to avoid)
      const recentQAs = answeredQAs.slice(-3).map((q, index) =>
        `${index + 1}. Q: "${q.question_text}"\nA: "${q.answer_text?.substring(0, 150)}..."`
      ).join('\n\n')

      // List ALL previous questions to avoid duplicates
      const allPreviousQuestions = qaCore.map(q => q.question_text).filter(Boolean)

      const questionsRemaining = MAX_CORE_QUESTIONS - answeredCount
      const stage = answeredCount <= 2 ? 'OPENING' : answeredCount <= 4 ? 'DEVELOPMENT' : 'CLOSING'

      userPrompt = `Continue interview for ${page.type} (Question ${answeredCount + 1}/${MAX_CORE_QUESTIONS}).

Recent conversation:
${recentQAs || '(No recent Q&A)'}

Guidance: ${typeGuidance[page.type] || typeGuidance.custom}

Stage: ${stage}
${stage === 'OPENING' ? 'Focus on motivation and inspiration.' :
  stage === 'DEVELOPMENT' ? 'Focus on journey, obstacles, and proof.' :
  'Focus on vision, impact, and next steps.'}

CRITICAL: Avoid ANY similarity to these previous questions:
${allPreviousQuestions.map((q, i) => `${i + 1}. "${q}"`).join('\n')}

Create a COMPLETELY DIFFERENT question that explores a new angle.
Return exactly 1 strategic follow-up question in JSON format.`
    }


    // Enhanced caching to prevent duplicate calls
    const cacheKey = `${pageId}-${answeredCount}-${userPrompt.substring(0, 100).replace(/\s+/g, '-')}`

    // Check if request is in progress to prevent race conditions
    const inProgressKey = `${cacheKey}-in-progress`
    const inProgress = questionCache.get(inProgressKey)
    if (inProgress && Date.now() - inProgress.timestamp < 30000) { // 30 sec timeout
      console.log('⏳ Request already in progress, returning cached result or waiting...')
      const cached = questionCache.get(cacheKey)
      if (cached) {
        return NextResponse.json({ questions: cached.questions })
      }
      // If no cached result yet, return a "please wait" response
      return NextResponse.json({
        questions: [],
        message: "Question generation in progress, please try again in a moment"
      })
    }

    // Mark request as in progress
    questionCache.set(inProgressKey, { questions: [], timestamp: Date.now() })

    // Check existing cache
    const cached = questionCache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      console.log('🚀 Using cached questions')
      questionCache.delete(inProgressKey) // Clear in-progress flag
      return NextResponse.json({ questions: cached.questions })
    }

    // Use Groq for fast interview question generation, fallback to DeepSeek
    let getFinalText
    try {
      const groqResult = await callGroqDirect(userPrompt, [
        { role: 'system', content: INTERVIEW_SYS }
      ], { sessionLength: (qa || []).length, temperature: 0.1, max_tokens: 500 })
      getFinalText = groqResult.getFinalText
    } catch (error) {
      console.warn('⚠️ Groq failed, falling back to DeepSeek:', error.message)
      const deepseekResult = await callDeepSeekDirect(userPrompt, [
        { role: 'system', content: INTERVIEW_SYS }
      ], { sessionLength: (qa || []).length, temperature: 0.1, max_tokens: 500 })
      getFinalText = deepseekResult.getFinalText
    }

    const raw = await getFinalText()

    // Debug logging to see what Groq returns
    console.log('⚡ Groq raw response:', raw.substring(0, 500))
    console.log('📊 Interview context - Answered questions:', answeredCount, 'Total Q&A records:', (qa || []).length)
    console.log('🎭 Interview stage:', answeredCount === 0 ? 'OPENING QUESTIONS' : 'STRATEGIC FOLLOW-UP')

    // First attempt: strict JSON extraction
    let parsedJson: any = toStrictJson(raw)

    // If empty or not parseable, retry once with a stricter nudge
    if (!parsedJson) {
      console.warn('⚠️ First parse failed or empty. Retrying once...')
      try {
        const retryCall = await callGroqDirect(
          userPrompt + '\n\nCRITICAL: Output ONLY valid JSON with exactly ' + desiredCount + ' questions. No markdown. No prose.',
          [{ role: 'system', content: INTERVIEW_SYS }],
          { sessionLength: (qa || []).length }
        )
        const raw2 = await retryCall.getFinalText()
        console.log('⚡ Groq raw response (retry):', (raw2 || '').substring(0, 500))
        parsedJson = toStrictJson(raw2 || '')
      } catch (error) {
        console.warn('⚠️ Groq retry failed, using DeepSeek for retry:', error.message)
        const deepseekRetry = await callDeepSeekDirect(
          userPrompt + '\n\nCRITICAL: Output ONLY valid JSON with exactly ' + desiredCount + ' questions. No markdown. No prose.',
          [{ role: 'system', content: INTERVIEW_SYS }],
          { sessionLength: (qa || []).length }
        )
        const raw2 = await deepseekRetry.getFinalText()
        console.log('🎯 DeepSeek raw response (retry):', (raw2 || '').substring(0, 500))
        parsedJson = toStrictJson(raw2 || '')
      }
    }

    // Log what we parsed
    console.log('📝 Parsed result:', parsedJson ? 'SUCCESS' : 'FAILED')
    if (parsedJson) console.log('Questions found:', parsedJson.questions?.length)

    // If Groq still fails or returns an error object, return an error
    if (!parsedJson || parsedJson.error) {
      console.error('🔴 Groq failed to return valid JSON after retry:', parsedJson?.details || 'No details')
      return NextResponse.json({
        error: parsedJson?.error || 'AI service temporarily unavailable. Please try again.'
      }, { status: 503 })
    }

    const parsed = InterviewQuestionsSchema.safeParse(parsedJson)
    if (!parsed.success) return NextResponse.json({ error: 'Invalid questions', details: parsed.error.format() }, { status: 500 })

    let questions = parsed.data.questions
    if (questions.length > desiredCount) questions = questions.slice(0, desiredCount)

    // Simple uniqueness check
    const existingIds = new Set(qaCore.map(q => q.question_id))
    const existingTexts = new Set(qaCore.map(q => (q.question_text || '').trim().toLowerCase()))

    // Enhanced filtering for uniqueness and quality
    let filtered = questions.filter(q => {
      const questionText = (q.text || '').trim().toLowerCase()

      // Check exact matches
      if (existingIds.has(q.id) || existingTexts.has(questionText)) return false

      // Check for similar questions (fuzzy matching)
      for (const existingText of existingTexts) {
        const similarity = calculateSimilarity(questionText, existingText)
        if (similarity > 0.7) { // 70% similarity threshold
          console.log('🚫 Rejecting similar question:', questionText, 'vs', existingText, 'similarity:', similarity)
          return false
        }
      }

      // Avoid basic clichés
      const cliches = ['pain point', 'digital landscape', 'desperately needed']
      if (cliches.some(cliche => questionText.includes(cliche))) return false

      return true
    })

    console.log('🔍 Generated questions:', questions.map(q => `"${q.text}" (intent: ${q.intent})`))
    console.log('🚫 Existing questions:', qaCore.map(q => q.question_text).filter(Boolean))
    console.log('📊 Questions after filtering:', filtered.length)

    if (filtered.length === 0) {
      console.warn('⚠️ All generated questions were filtered out as duplicates. Using original questions...')
      filtered = questions.slice(0, 1) // Just use the first question if filtering fails
    }

    if (filtered.length > 0) {
      questions = filtered
      console.log('✅ Strategic questions generated:', questions.map(q => `"${q.text}" (intent: ${q.intent})`))
    } else {
      console.error('🔴 Failed to generate unique questions even after retry')
    }

    // Insert or reuse by TEXT to avoid duplicates; return rows with DB-backed IDs
    const baseOrder = (qaCore?.length || 0)
    const returned: { id: string; text: string; optional?: boolean }[] = []

    for (const q of questions) {
      const qText = (q.text || '').trim()
      if (!qText) continue

      // 1) If a question with the same text already exists for this page, reuse it
      const { data: byText } = await supabase
        .from('onlypages_interview')
        .select('id, question_id, question_text, is_optional, answer_text')
        .eq('page_id', pageId)
        .ilike('question_text', qText)
        .maybeSingle()

      if (byText) {
        returned.push({ id: byText.question_id, text: byText.question_text, optional: !!byText.is_optional })
        continue
      }

      // 2) Otherwise insert with a stable ID derived from the text
      const stableId = stableQuestionIdFromText(qText)
      const { data: existsById } = await supabase
        .from('onlypages_interview')
        .select('id')
        .eq('page_id', pageId)
        .eq('question_id', stableId)
        .maybeSingle()

      if (!existsById) {
        await supabase.from('onlypages_interview').insert({
          page_id: pageId,
          question_id: stableId,
          question_text: qText,
          is_optional: q.optional,
          order_index: baseOrder + returned.length
        })
      }

      returned.push({ id: stableId, text: qText, optional: !!q.optional })
    }

    // Cache the result for future use
    questionCache.set(cacheKey, { questions: returned, timestamp: Date.now() })

    // Clear in-progress flag
    questionCache.delete(inProgressKey)

    return NextResponse.json({ questions: returned })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Server error' }, { status: 500 })
  }
}
