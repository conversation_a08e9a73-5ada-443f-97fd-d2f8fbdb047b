import { NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function POST(req: Request) {
  try {
    const { pageId, visitorId, referrer } = await req.json()

    if (!pageId) {
      return NextResponse.json({ error: 'Page ID required' }, { status: 400 })
    }

    const supabase = await createSupabaseServerClient()

    // Call the increment_page_views function
    const { error } = await supabase.rpc('increment_page_views', {
      page_uuid: pageId,
      visitor_session: visitorId || null,
      referrer_url: referrer || null
    })

    if (error) {
      console.error('Error tracking view:', error)
      return NextResponse.json({ error: 'Failed to track view' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('View tracking error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}