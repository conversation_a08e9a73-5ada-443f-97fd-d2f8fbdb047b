import { NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function GET() {
  try {
    const supabase = await createSupabaseServerClient()
    const { data: auth } = await supabase.auth.getUser()
    const user = auth?.user
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    // Fetch user's OnlyPages
    const { data: pages, error: pagesError } = await supabase
      .from('onlypages')
      .select('id, slug, status, type, article, created_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (pagesError) {
      console.error('Error fetching OnlyPages:', pagesError)
      return NextResponse.json({ error: 'Failed to fetch pages' }, { status: 500 })
    }

    // Get waitlist counts for each page
    const pagesWithWaitlist = await Promise.all(
      (pages || []).map(async (page) => {
        const { count, error: countError } = await supabase
          .from('onlypages_waitlist')
          .select('*', { count: 'exact', head: true })
          .eq('page_id', page.id)

        if (countError) {
          console.error('Error counting waitlist:', countError)
          return { ...page, waitlist_count: 0 }
        }

        return { ...page, waitlist_count: count || 0 }
      })
    )

    return NextResponse.json({ pages: pagesWithWaitlist })
  } catch (e: any) {
    console.error('Dashboard API error:', e)
    return NextResponse.json({ error: e?.message || 'Server error' }, { status: 500 })
  }
}
