import { NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function POST(req: Request) {
  try {
    const supabase = await createSupabaseServerClient()

    // Check if user is authenticated (you might want to add admin role check)
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Run the recategorization function
    const { data, error } = await supabase.rpc('recategorize_all_pages')

    if (error) {
      console.error('Recategorization error:', error)
      return NextResponse.json({ error: 'Failed to recategorize pages' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      processedPages: data,
      message: `Successfully recategorized ${data} pages`
    })
  } catch (error) {
    console.error('Recategorization error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(req: Request) {
  try {
    const supabase = await createSupabaseServerClient()

    // Get current category stats
    const { data: categories } = await supabase
      .from('onlypages_categories')
      .select('*')
      .order('page_count', { ascending: false })

    // Get total uncategorized pages
    const { count: uncategorizedCount } = await supabase
      .from('onlypages')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'published')
      .or('categories.is.null,categories.eq.{}')

    return NextResponse.json({
      categories: categories || [],
      uncategorizedPages: uncategorizedCount || 0,
      totalCategories: categories?.length || 0
    })
  } catch (error) {
    console.error('Category stats error:', error)
    return NextResponse.json({ error: 'Failed to get category stats' }, { status: 500 })
  }
}