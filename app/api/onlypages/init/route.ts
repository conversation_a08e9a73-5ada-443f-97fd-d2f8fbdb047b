import { NextResponse } from 'next/server'
import { z } from 'zod'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { createClient } from '@supabase/supabase-js'

const bodySchema = z.object({
  type: z.string().min(2).max(32),
  slug_hint: z.string().min(1).max(120),
  creator_name: z.string().optional().nullable()
})

function slugify(input: string): string {
  return input
    .toLowerCase()
    .normalize('NFKD')
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
}

function shortid(len = 3): string {
  return Math.random().toString(36).slice(2, 2 + len)
}

async function uniqueSlug(supabase: any, base: string): Promise<string> {
  let candidate = `${base}-${shortid(3)}`
  for (let i = 0; i < 5; i++) {
    const { data } = await supabase
      .from('onlypages')
      .select('id')
      .eq('slug', candidate)
      .single()
    if (!data) return candidate
    candidate = `${base}-${shortid(3)}`
  }
  return `${base}-${Date.now().toString(36)}`
}

export async function POST(req: Request) {
  try {
    const json = await req.json().catch(() => ({}))
    const parsed = bodySchema.safeParse(json)
    if (!parsed.success) {
      return NextResponse.json({ error: 'Invalid body', details: parsed.error.format() }, { status: 400 })
    }

    const supabase = await createSupabaseServerClient()
    const { data: auth } = await supabase.auth.getUser()
    const user = auth?.user
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const type = parsed.data.type
    const base = slugify(parsed.data.slug_hint)
    if (!base) return NextResponse.json({ error: 'Invalid slug hint' }, { status: 400 })

    const slug = await uniqueSlug(supabase, base)

    // Create draft page
    const { data: page, error: insertErr } = await supabase
      .from('onlypages')
      .insert({
        user_id: user.id,
        type,
        slug_hint: parsed.data.slug_hint,
        slug,
        status: 'draft',
        creator_name: parsed.data.creator_name
      })
      .select('id, slug')
      .single()

    if (insertErr || !page) {
      return NextResponse.json({ error: insertErr?.message || 'Failed to create page' }, { status: 500 })
    }

    // Prepare signed upload URL for hero asset
    const service = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      { auth: { persistSession: false, autoRefreshToken: false } }
    )

    const bucket = 'onlypages-assets'
    const path = `u/${user.id}/onlypages/${page.id}/hero`

    const { data: signed } = await service
      .storage
      .from(bucket)
      .createSignedUploadUrl(path)

    // Even if signing fails, return page data; front-end can fallback to client upload
    const upload = signed ? { url: signed.signedUrl, token: signed.token, bucket, path } : null

    return NextResponse.json({ page, upload })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Server error' }, { status: 500 })
  }
}

