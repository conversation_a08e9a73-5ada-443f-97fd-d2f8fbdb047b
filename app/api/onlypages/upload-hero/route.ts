import { NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { createClient } from '@supabase/supabase-js'

export async function POST(req: Request) {
  try {
    const supabase = await createSupabaseServerClient()
    const { data: auth } = await supabase.auth.getUser()
    const user = auth?.user
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await req.formData()
    const file = formData.get('file') as File
    const pageId = formData.get('page_id') as string

    if (!file || !pageId) {
      return NextResponse.json({ error: 'File and page_id are required' }, { status: 400 })
    }

    // Verify user owns the page
    const { data: page } = await supabase
      .from('onlypages')
      .select('id, user_id')
      .eq('id', pageId)
      .single()

    if (!page || page.user_id !== user.id) {
      return NextResponse.json({ error: 'Page not found or access denied' }, { status: 404 })
    }

    // Create Supabase service client for storage operations
    const serviceClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      { auth: { persistSession: false, autoRefreshToken: false } }
    )

    const bucket = 'onlypages-assets'
    const fileExt = file.name.split('.').pop()
    const fileName = `hero-${Date.now()}.${fileExt}`
    const filePath = `u/${user.id}/onlypages/${pageId}/${fileName}`

    // Upload file to Supabase Storage
    const { data: uploadData, error: uploadError } = await serviceClient
      .storage
      .from(bucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true
      })

    if (uploadError) {
      console.error('Upload error:', uploadError)
      return NextResponse.json({ error: 'Failed to upload file' }, { status: 500 })
    }

    // Get public URL
    const { data: { publicUrl } } = serviceClient
      .storage
      .from(bucket)
      .getPublicUrl(filePath)

    // Update the page with the hero image URL
    const { error: updateError } = await supabase
      .from('onlypages')
      .update({ hero_image_url: publicUrl })
      .eq('id', pageId)

    if (updateError) {
      console.error('Update error:', updateError)
      return NextResponse.json({ error: 'Failed to update page with image' }, { status: 500 })
    }

    // Debug: log the public URL
    console.log('Generated public URL:', publicUrl)

    const responseData = { 
      success: true, 
      data: {
        imageUrl: publicUrl
      },
      message: 'Hero image uploaded successfully'
    }

    console.log('API response data:', responseData)

    return NextResponse.json(responseData)

  } catch (error: any) {
    console.error('Hero upload error:', error)
    return NextResponse.json({ error: error.message || 'Server error' }, { status: 500 })
  }
}
