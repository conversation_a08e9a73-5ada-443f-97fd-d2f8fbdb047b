import { NextResponse } from 'next/server'
import { z } from 'zod'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { callGroqDirect, callDeepSeekDirect } from '@/lib/genyus/providers'
import { ArticleSchema, ThemeTokensSchema } from '@/lib/onlypages/schemas'
import { selectTemplate, type Category } from '@/lib/onlypages/templates'
import { selectTheme } from '@/lib/onlypages/theme-presets'

const bodySchema = z.object({
  page_id: z.string().uuid(),
  regenerate: z.boolean().optional().default(false)
})

function toStrictJson(content: string): any | null {
  if (typeof content !== 'string') return null

  // Remove markdown code blocks if present
  let cleanContent = content.trim()
  if (cleanContent.startsWith('```json')) {
    cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '')
  } else if (cleanContent.startsWith('```')) {
    cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '')
  }

  // Try to extract the JSON block
  const start = cleanContent.indexOf('{')
  const end = cleanContent.lastIndexOf('}')
  if (start !== -1 && end !== -1 && end > start) {
    try {
      return JSON.parse(cleanContent.slice(start, end + 1))
    } catch (e) {
      console.error('JSON parse error:', e.message)
      console.error('Attempted to parse:', cleanContent.slice(start, end + 1).substring(0, 200))
    }
  }

  console.error('No valid JSON found in content:', cleanContent.substring(0, 200))
  return null
}

export async function POST(req: Request) {
  try {
    const json = await req.json().catch(() => ({}))
    const parsed = bodySchema.safeParse(json)
    if (!parsed.success) return NextResponse.json({ error: 'Invalid body' }, { status: 400 })

    const supabase = await createSupabaseServerClient()
    const { data: auth } = await supabase.auth.getUser()
    const user = auth?.user
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    // Load page and QA
    const { data: page } = await supabase
      .from('onlypages')
      .select('id, user_id, type, slug, creator_name')
      .eq('id', parsed.data.page_id)
      .single()
    if (!page || page.user_id !== user.id) return NextResponse.json({ error: 'Not found' }, { status: 404 })

    const { data: qa } = await supabase
      .from('onlypages_interview')
      .select('question_id, question_text, answer_text, is_optional')
      .eq('page_id', page.id)
      .order('order_index', { ascending: true })

    const totalRequired = (qa || []).filter(q => !q.is_optional).length
    const answeredRequired = (qa || []).filter(q => !q.is_optional && q.answer_text && q.answer_text.trim().length > 0).length
    const coverage = totalRequired === 0 ? 1 : answeredRequired / totalRequired
    if (coverage < 0.8) return NextResponse.json({ error: 'Need more answers', coverage }, { status: 400 })

    // Select optimal template based on content type and interview answers
    const interviewAnswers = (qa || []).map(q => ({
      question: q.question_text || '',
      answer: q.answer_text || ''
    }))

    const selectedTemplate = selectTemplate(
      page.type as Category,
      interviewAnswers,
      'medium' // TODO: detect content length from answers
    )

    console.log(`🎨 Selected template: ${selectedTemplate.name} (${selectedTemplate.id}) for ${page.type}`)

    // Select beautiful pre-defined theme (no AI needed!)
    const selectedTheme = selectTheme(selectedTemplate.id, page.type, selectedTemplate.emphasis)
    console.log(`🎨 Selected theme: ${selectedTheme.name} (${selectedTheme.id}) - ${selectedTheme.description}`)

    // Build Writer prompt (system + user strings)
    const WRITER_SYS = `You are the Executive Editor at OnlyPages, crafting articles that combine Wikipedia's comprehensive depth with The New Yorker's narrative brilliance.

YOUR WRITING MUST BE:
1. **COMPREHENSIVE** - Cover every angle, like Wikipedia, but with soul
2. **BRILLIANTLY WRITTEN** - Every sentence should feel crafted, not generated
3. **NARRATIVELY COMPELLING** - Find the story arc and make readers FEEL it
4. **INTELLECTUALLY SATISFYING** - Smart without being pretentious
5. **UNIQUELY VOICED** - Match tone to content type (celebratory for launches, investigative for news, intimate for personal stories)

STRUCTURE GUIDELINES:
- **Title**: Magnetic and memorable (≤70 chars) - make them WANT to read
- **Dek**: Sets the stage beautifully (≤180 chars) - promise the journey ahead  
- **Body**: 800-2000 words of excellence:
  - Opening: Hook immediately with scene, moment, or revelation
  - Development: Build narrative tension while delivering information
  - Deep Dive: Explore nuances, implications, behind-the-scenes
  - Resolution: Satisfy emotionally and intellectually
  - Future Forward: What this means, what comes next
- **Pull Quote**: The most powerful truth extracted
- **CTA**: Clear, compelling next step

WRITING TECHNIQUES:
- Use specific details that illuminate universal truths
- Vary sentence rhythm for musicality
- Deploy metaphors that clarify, not obscure
- Include data/proof points woven naturally into narrative
- Create scenes when possible, not just exposition

NEVER:
- Use AI clichés ("dive into", "leverage", "tap into", "cutting-edge")
- Write generic corporate speak
- Lose the human voice
- Sacrifice accuracy for style

Output ONLY valid JSON - no markdown code blocks, no comments, no extra text. Missing facts go in [brackets] within text and list in \`missing\`.`

    // Type-specific writing guidance
    const writingStyles: Record<string, string> = {
      press: 'Write like TechCrunch meets The Verge. Excitement balanced with credibility. Make the announcement feel like THE moment.',
      news: 'Channel BBC meets Wired. Authoritative but accessible. Context-rich. Help readers understand why this matters NOW.',
      book: 'Write like The New York Review of Books. Literary but not pretentious. Capture both the work and its significance.',
      movie: 'Channel Variety meets The Guardian. Industry insight with cultural commentary. Make readers feel the vision.',
      album: 'Write like Pitchfork at its best. Musically literate. Capture sonic textures in words. Make readers hear it.',
      site: 'Write like Product Hunt meets Fast Company. Show the problem-solution fit. Make the innovation tangible.',
      life: 'Channel The Atlantic meets This American Life. Deeply human. Universal themes from specific experiences.',
      wiki: 'Write like Wikipedia meets The New Yorker profile. Comprehensive yet engaging. Build the complete picture.',
      custom: 'Read the content deeply. Match the tone to the subject matter. Find the perfect voice.'
    }

    const userPayload = {
      PAGE_TYPE: page.type,
      WRITING_STYLE: writingStyles[page.type] || writingStyles.custom,
      OWNER: page.creator_name || 'the creator',
      INTERVIEW_QA: (qa || []).map(q => ({ 
        id: q.question_id, 
        question: q.question_text, 
        answer: q.answer_text || '[No answer provided]' 
      })),
      CTA_PREFERENCE: 'Generate a compelling waitlist/mailing list signup CTA. Examples: "Join the Waitlist", "Get Early Access", "Be the First to Know", "Stay Updated", "Get Notified When Available". Do NOT include URLs - the system handles that automatically.',
      SPECIAL_INSTRUCTIONS: [
        'Find the emotional core of this story and build from there',
        'Use ALL provided information to create comprehensive coverage',
        'Structure for both skimmers (subheads, pull quotes) and deep readers',
        'Include specific details, numbers, dates when available',
        'Write something the creator would be PROUD to share'
      ],
      JSON_SCHEMA: {
        title: 'Magnetic headline (string)',
        dek: 'Compelling subtitle/deck (string)',
        body_html: 'Full article HTML with <p>, <h2>, <h3>, <blockquote>, <strong>, <em> tags (string)',
        pull_quote: 'Most powerful excerpt (string, optional)',
        cta_label: 'Waitlist signup text like "Join the Waitlist" or "Get Early Access" (string, optional)',
        cta_url: 'Leave empty - system handles waitlist URLs automatically (string, optional)',
        tags: ['relevant', 'tags'],
        meta: {
          og_title: 'Social media title (optional)',
          og_desc: 'Social media description (optional)',
          meta_desc: 'SEO description (optional)'
        },
        missing: ['List any missing crucial information (optional)']
      },
      OUTPUT: 'JSON only. Create something brilliant.'
    }

    // Use Groq for blazing fast article generation with DeepSeek fallback
    let getFinalText
    try {
      console.log('🚀 Generating article with Groq for speed...')
      const groqResult = await callGroqDirect(JSON.stringify(userPayload), [
        { role: 'system', content: WRITER_SYS }
      ], { temperature: 0.7, max_tokens: 8000 })
      getFinalText = groqResult.getFinalText
    } catch (error) {
      console.warn('⚠️ Groq failed, falling back to DeepSeek:', error.message)
      const deepseekResult = await callDeepSeekDirect(JSON.stringify(userPayload), [
        { role: 'system', content: WRITER_SYS }
      ], { sessionLength: (qa || []).length })
      getFinalText = deepseekResult.getFinalText
    }

    const raw = await getFinalText()
    let jsonOut = toStrictJson(raw)

    if (!jsonOut) {
      console.warn('⚠️ Writer JSON parse failed. Retrying once with stricter instruction...')
      // Retry with whichever provider worked the first time
      let retry
      try {
        retry = await callGroqDirect(JSON.stringify({ ...userPayload, OUTPUT: 'STRICT JSON ONLY. No markdown, no code fences, no commentary.' }), [
          { role: 'system', content: WRITER_SYS }
        ], { temperature: 0.3, max_tokens: 8000 })
      } catch {
        retry = await callDeepSeekDirect(JSON.stringify({ ...userPayload, OUTPUT: 'STRICT JSON ONLY. No markdown, no code fences, no commentary.' }), [
          { role: 'system', content: WRITER_SYS }
        ], { sessionLength: (qa || []).length })
      }
      const raw2 = await retry.getFinalText()
      jsonOut = toStrictJson(raw2)
    }

    if (!jsonOut) {
      return NextResponse.json({ error: 'Writer temporarily unavailable. Please try again.' }, { status: 503 })
    }

    console.log('🔍 Attempting to parse article JSON:', JSON.stringify(jsonOut).substring(0, 500))

    const parsedArticle = ArticleSchema.safeParse(jsonOut)
    if (!parsedArticle.success) {
      console.error('❌ Article schema validation failed:', parsedArticle.error.format())
      console.error('📄 Raw JSON received:', JSON.stringify(jsonOut, null, 2))
      return NextResponse.json({
        error: 'Generated article does not match expected format',
        details: parsedArticle.error.format(),
        rawData: jsonOut
      }, { status: 500 })
    }

    const article = parsedArticle.data

    // Use beautiful pre-defined theme (instant, no AI cost!)
    const theme = {
      family: selectedTheme.family,
      accent: selectedTheme.accent,
      bg: selectedTheme.bg,
      ink: selectedTheme.ink,
      muted: selectedTheme.muted,
      shapes: { style: 'soft', weight: 2 },
      typography: { title: {}, dek: {}, body: {} }
    }

    // Save article, theme, and selected template
    const { error: saveErr } = await supabase
      .from('onlypages')
      .update({
        article,
        theme_tokens: theme,
        template_id: selectedTemplate.id,
        status: 'draft'
      })
      .eq('id', page.id)

    if (saveErr) return NextResponse.json({ error: saveErr.message }, { status: 500 })

    return NextResponse.json({ article, theme_tokens: theme })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Server error' }, { status: 500 })
  }
}
