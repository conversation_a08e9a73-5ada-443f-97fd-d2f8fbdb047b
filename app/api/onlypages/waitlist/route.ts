import { NextResponse } from 'next/server'
import { z } from 'zod'
import { createSupabaseServerClient } from '@/lib/supabase/client'

const bodySchema = z.object({
  page_id: z.string().uuid()
})

export async function POST(req: Request) {
  try {
    const json = await req.json().catch(() => ({}))
    const parsed = bodySchema.safeParse(json)
    if (!parsed.success) {
      return NextResponse.json({ error: 'Invalid body' }, { status: 400 })
    }

    const supabase = await createSupabaseServerClient()
    const { data: auth } = await supabase.auth.getUser()
    const user = auth?.user
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const { page_id } = parsed.data

    // Check if page exists and is published
    const { data: page } = await supabase
      .from('onlypages')
      .select('id, status')
      .eq('id', page_id)
      .single()

    if (!page || !['published', 'unlisted'].includes(page.status)) {
      return NextResponse.json({ error: 'Page not found' }, { status: 404 })
    }

    // Check if user is already on waitlist
    const { data: existing } = await supabase
      .from('onlypages_waitlist')
      .select('id')
      .eq('page_id', page_id)
      .eq('user_id', user.id)
      .single()

    if (existing) {
      return NextResponse.json({ error: 'Already on waitlist' }, { status: 409 })
    }

    // Add user to waitlist
    const { data: waitlistEntry, error: insertError } = await supabase
      .from('onlypages_waitlist')
      .insert({ page_id, user_id: user.id })
      .select('id, joined_at')
      .single()

    if (insertError) {
      console.error('Waitlist insert error:', insertError)
      return NextResponse.json({ error: 'Failed to join waitlist' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      waitlist_id: waitlistEntry.id,
      joined_at: waitlistEntry.joined_at
    })

  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Server error' }, { status: 500 })
  }
}

// GET endpoint to check if user is on waitlist for a page
export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url)
    const page_id = searchParams.get('page_id')
    
    if (!page_id) {
      return NextResponse.json({ error: 'page_id parameter required' }, { status: 400 })
    }

    const supabase = await createSupabaseServerClient()
    const { data: auth } = await supabase.auth.getUser()
    const user = auth?.user
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const { data: waitlistEntry } = await supabase
      .from('onlypages_waitlist')
      .select('id, joined_at')
      .eq('page_id', page_id)
      .eq('user_id', user.id)
      .single()

    return NextResponse.json({ 
      on_waitlist: !!waitlistEntry,
      joined_at: waitlistEntry?.joined_at || null
    })

  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Server error' }, { status: 500 })
  }
}
