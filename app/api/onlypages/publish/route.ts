import { NextResponse } from 'next/server'
import { z } from 'zod'
import { createSupabaseServerClient } from '@/lib/supabase/client'

const bodySchema = z.object({
  page_id: z.string().uuid(),
  slug: z.string().min(1).max(120).optional()
})

function slugify(input: string): string {
  const s = (input || '')
    .toLowerCase()
    .normalize('NFKD')
    .replace(/[^a-z0-9\s-]/g, '')
    .trim()
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '')
  return s.slice(0, 80) || 'page'
}

export async function POST(req: Request) {
  try {
    const json = await req.json().catch(() => ({}))
    const parsed = bodySchema.safeParse(json)
    if (!parsed.success) return NextResponse.json({ error: 'Invalid body' }, { status: 400 })

    const supabase = await createSupabaseServerClient()
    const { data: auth } = await supabase.auth.getUser()
    const user = auth?.user
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    // Load page
    const { data: page } = await supabase
      .from('onlypages')
      .select('id, user_id, slug, article, slug_hint, type')
      .eq('id', parsed.data.page_id)
      .single()

    if (!page || page.user_id !== user.id) return NextResponse.json({ error: 'Not found' }, { status: 404 })

    // Determine base slug
    const articleTitle = (page as any)?.article?.title as string | undefined
    const provided = parsed.data.slug?.trim()
    let base = provided && provided.length > 0 ? provided : (page.slug || articleTitle || (page as any)?.slug_hint || 'onlypage')
    let candidate = slugify(base)

    // Ensure uniqueness robustly under RLS by attempting the update and retrying on unique violation
    const baseSlug = slugify(base)

    for (let attempt = 0; attempt < 12; attempt++) {
      const trySlug = attempt === 0 ? baseSlug : `${baseSlug}-${attempt + 1}`
      const { error: upErr } = await supabase
        .from('onlypages')
        .update({ slug: trySlug, status: 'published' })
        .eq('id', page.id)

      if (!upErr) {
        return NextResponse.json({ slug: trySlug, url: `/p/${trySlug}` })
      }

      // 23505 = unique_violation; for anything else, bubble the error
      if (upErr?.code !== '23505') {
        return NextResponse.json({ error: upErr.message }, { status: 500 })
      }
    }

    return NextResponse.json({ error: 'Could not allocate a unique slug. Please try again with a different title.' }, { status: 409 })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Server error' }, { status: 500 })
  }
}

