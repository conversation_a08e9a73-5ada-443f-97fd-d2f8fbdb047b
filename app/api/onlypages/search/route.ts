import { NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url)
    const query = searchParams.get('q')
    const category = searchParams.get('category')
    const type = searchParams.get('type')
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100)
    const offset = parseInt(searchParams.get('offset') || '0')

    const supabase = await createSupabaseServerClient()

    let queryBuilder = supabase
      .from('onlypages')
      .select(`
        id,
        slug,
        article,
        creator_name,
        categories,
        type,
        total_views,
        created_at
      `)
      .eq('status', 'published')

    // Apply filters
    if (query) {
      queryBuilder = queryBuilder.or(`
        article->>title.ilike.%${query}%,
        article->>dek.ilike.%${query}%,
        creator_name.ilike.%${query}%
      `)
    }

    if (category) {
      queryBuilder = queryBuilder.contains('categories', [category])
    }

    if (type) {
      queryBuilder = queryBuilder.eq('type', type)
    }

    // Get total count for pagination
    const { count } = await supabase
      .from('onlypages')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'published')

    // Apply pagination and sorting
    const { data: pages, error } = await queryBuilder
      .order('total_views', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      throw error
    }

    // Transform pages to extract title and dek from article JSON
    const transformedPages = (pages || []).map(page => ({
      id: page.id,
      slug: page.slug,
      title: page.article?.title || 'Untitled',
      dek: page.article?.dek || '',
      creator_name: page.creator_name,
      categories: page.categories || [],
      type: page.type,
      total_views: page.total_views || 0,
      created_at: page.created_at
    }))

    return NextResponse.json({
      pages: transformedPages,
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      }
    })
  } catch (error) {
    console.error('Search error:', error)
    return NextResponse.json({ error: 'Search failed' }, { status: 500 })
  }
}