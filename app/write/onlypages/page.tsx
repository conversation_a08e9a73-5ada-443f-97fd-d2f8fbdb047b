"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"

export default function OnlyPagesStart() {
  const router = useRouter()
  const [slugHint, setSlugHint] = useState("")
  const [type, setType] = useState("press")
  const [preContext, setPreContext] = useState("")
  const [creatorName, setCreatorName] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  async function start() {
    try {
      setLoading(true)
      setError(null)
      const res = await fetch('/api/onlypages/init', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type, slug_hint: slugHint || type, creator_name: creatorName.trim() || null })
      })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Failed to create draft')

      // Save pre-question answer (seed context) before entering interview
      if (preContext.trim().length > 0) {
        await fetch(`/api/onlypages/interview/${data.page.id}/answer`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ question_id: 'pre', answer: preContext.trim(), question_text: 'Quick context (one-liner)' })
        })
      }

      router.push(`/write/onlypages/${data.page.id}`)
    } catch (e: any) {
      setError(e.message || 'Failed to start')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-xl mx-auto px-4 sm:px-6 py-8 sm:py-12">
        <div className="text-center mb-8">
          <h1 className="text-2xl sm:text-3xl font-serif text-gray-900 mb-3">OnlyPages</h1>
          <p className="text-gray-600 font-serif">Interview → Generate → Publish</p>
        </div>

        {error && (
          <div className="mb-4 p-3 rounded border border-red-200 bg-red-50 text-red-700 text-sm">{error}</div>
        )}

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
            <select
              value={type}
              onChange={(e) => setType(e.target.value)}
              className="w-full border rounded-lg px-3 py-2"
            >
              <option value="press">Press</option>
              <option value="news">News</option>
              <option value="book">Book</option>
              <option value="movie">Movie</option>
              <option value="album">Album</option>
              <option value="site">Site</option>
              <option value="life">Life</option>
              <option value="wiki">Wiki (people, creators, businesses)</option>
              <option value="custom">Custom</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Your name (optional)</label>
            <input
              value={creatorName}
              onChange={(e) => setCreatorName(e.target.value)}
              placeholder="e.g. Sarah Chen"
              className="w-full border rounded-lg px-3 py-2"
            />
            <p className="text-xs text-gray-500 mt-1">This helps personalize the press release instead of using "The creator"</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Quick context (one-liner)</label>
            <input
              value={preContext}
              onChange={(e) => setPreContext(e.target.value)}
              placeholder="What are you promoting or announcing?"
              className="w-full border rounded-lg px-3 py-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Slug hint</label>
            <input
              value={slugHint}
              onChange={(e) => setSlugHint(e.target.value)}
              placeholder="e.g. bankroll squad forever"
              className="w-full border rounded-lg px-3 py-2"
            />
          </div>
          <button
            onClick={start}
            disabled={loading}
            className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg px-4 py-2.5 font-medium disabled:opacity-50"
          >
            {loading ? 'Creating…' : 'Create'}
          </button>
        </div>
      </div>
    </div>
  )
}

