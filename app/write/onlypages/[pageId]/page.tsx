"use client"

import { useEffect, useState } from 'react'
import { useParams, useRouter } from 'next/navigation'

type Q = { id: string; text: string; optional?: boolean }

function SpinnerStar({ className = '' }: { className?: string }) {
  return (
    <span className={`mr-2 inline-block animate-spin ${className}`} aria-hidden="true">★</span>
  )
}


export default function OnlyPagesInterview() {
  const params = useParams<{ pageId: string }>()
  const router = useRouter()
  const pageId = params?.pageId

  const [qs, setQs] = useState<Q[]>([])
  const [answers, setAnswers] = useState<Record<string, string>>({})
  const [busy, setBusy] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [generated, setGenerated] = useState<any | null>(null)
  const [askingId, setAskingId] = useState<string | null>(null)
  const [interviewComplete, setInterviewComplete] = useState(false)
  const [followupContext, setFollowupContext] = useState('')
  const [followupCount, setFollowupCount] = useState(0)
  const [showFollowupForm, setShowFollowupForm] = useState(false)
  const [generatingNext, setGeneratingNext] = useState(false)
  const [publishing, setPublishing] = useState(false)
  const [approved, setApproved] = useState(false)
  const [progress, setProgress] = useState<{ answered: number; total: number }>({ answered: 0, total: 0 })
  const [heroImage, setHeroImage] = useState<string | null>(null)
  const [uploading, setUploading] = useState(false)
  const [uploadError, setUploadError] = useState<string | null>(null)
  const [skippedHeroImage, setSkippedHeroImage] = useState(false)

  // Debug: log heroImage changes
  useEffect(() => {
    console.log('heroImage state changed:', heroImage)
  }, [heroImage])


  async function loadState(): Promise<{ nextUnanswered: Q | null }> {
    // Pull existing Q&A so answers are preserved and we can resume unanswered
    const res = await fetch(`/api/onlypages/interview/${pageId}/state`, { method: 'GET' })
    if (!res.ok) return { nextUnanswered: null }
    const data = await res.json()
    const qa = Array.isArray(data.qa) ? data.qa : []
    // Prefill answers map
    const map: Record<string, string> = {}
    for (const row of qa) {
      if (row.answer_text !== null && row.answer_text !== undefined) map[row.question_id] = row.answer_text || ''
    }
    setAnswers(map)
    // Update progress counts from server
    setProgress({ answered: Number(data.answered_count) || 0, total: Number(data.total) || qa.length || 0 })
    // Find first unanswered
    const nextRow = qa.find((q: any) => q.answer_text === null || q.answer_text === undefined)
    if (nextRow) return { nextUnanswered: { id: nextRow.question_id, text: nextRow.question_text, optional: !!nextRow.is_optional } }
    return { nextUnanswered: null }
  }

  const handleImageUpload = async (file: File) => {
    try {
      setUploading(true)
      setUploadError(null)
      
      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('File size must be less than 10MB')
      }

      // Check file type
      if (!file.type.startsWith('image/')) {
        throw new Error('Please upload an image file')
      }

      // Create form data
      const formData = new FormData()
      formData.append('file', file)
      formData.append('page_id', pageId)

      // Upload to API
      const response = await fetch('/api/onlypages/upload-hero', {
        method: 'POST',
        body: formData
      })

    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.error || 'Upload failed')
    }

    // Debug: log the API response
    console.log('API response data:', data)
    const imageUrl = data.data?.imageUrl || data.imageUrl
    console.log('Setting hero image to:', imageUrl)
    
    // Set the hero image URL
    setHeroImage(imageUrl)
      
    } catch (err: any) {
      setUploadError(err.message || 'Failed to upload image')
    } finally {
      setUploading(false)
    }
  }

  async function boot() {
    try {
      setBusy(true)
      setError(null)
      // 1) Try to resume from state
      const { nextUnanswered } = await loadState()
      if (nextUnanswered) {
        setQs([nextUnanswered])
        return
      }
      // 2) Otherwise, ask for the first question
      const res = await fetch(`/api/onlypages/interview/${pageId}/next`, { method: 'POST' })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Failed to start interview')
      const nextQ: Q | undefined = data?.questions?.[0]
      if (nextQ) setQs([nextQ])
    } catch (e: any) {
      setError(e.message)
    } finally {
      setBusy(false)
    }
  }

  async function saveAnswer(question_id: string, answer: string, skip: boolean = false) {
    const qtext = qs.find(q => q.id === question_id)?.text
    await fetch(`/api/onlypages/interview/${pageId}/answer`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ question_id, answer, skip, question_text: qtext })
    })
  }

  async function askNext(): Promise<Q | null> {
    const res = await fetch(`/api/onlypages/interview/${pageId}/next`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ context: {} })
    })
    const data = await res.json()

    // Check if interview is complete
    if (data.interview_complete) {
      setInterviewComplete(true)
      return null
    }

    if (res.ok && data.questions && data.questions.length > 0) {
      return data.questions[0] as Q
    }
    return null
  }

  async function askFollowup() {
    if (!followupContext.trim() || followupCount >= 3) return

    try {
      setBusy(true)
      setError(null)
      const res = await fetch(`/api/onlypages/interview/${pageId}/followup`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          context: followupContext.trim(),
          followup_count: followupCount + 1
        })
      })
      const data = await res.json()

      if (!res.ok) throw new Error(data.error || 'Failed to generate follow-up')

      if (data.questions && data.questions.length > 0) {
        setQs(prev => [...prev, ...data.questions])
        setFollowupCount(prev => prev + 1)
        setFollowupContext('')
        setShowFollowupForm(false)
      }
    } catch (e: any) {
      setError(e.message)
    } finally {
      setBusy(false)
    }
  }

  async function handleSaveAndNext(qid: string) {
    try {
      setAskingId(qid)
      setError(null)
      await saveAnswer(qid, answers[qid] || '')
      setGeneratingNext(true)

      // Update progress immediately to reflect the saved answer
      setProgress(prev => ({ ...prev, answered: prev.answered + 1 }))

      // First try to continue with any existing unanswered question
      const { nextUnanswered } = await loadState()
      if (nextUnanswered) {
        setQs([nextUnanswered])
        return
      }

      // Otherwise, generate the next strategic question
      const nextQ = await askNext()
      if (nextQ) {
        setQs(prev => (prev.find(x => x.id === nextQ.id) ? prev : [nextQ]))
      } else {
        // No next question available; keep current for now and show a gentle notice
        setError('No new question right now. Try Save & Next again in a moment, or click Generate Draft.')
      }
    } finally {
      setGeneratingNext(false)
      setAskingId(null)
    }
  }

  async function handleSkip(qid: string) {
    try {
      setAskingId(qid)
      setError(null)
      // Save empty + mark as skipped; then continue
      await saveAnswer(qid, '', true)

      // Update progress immediately to reflect the skipped answer
      setProgress(prev => ({ ...prev, answered: prev.answered + 1 }))

      const { nextUnanswered } = await loadState()
      if (nextUnanswered) {
        setQs([nextUnanswered])
        return
      }

      const nextQ = await askNext()
      if (nextQ) {
        setQs(prev => (prev.find(x => x.id === nextQ.id) ? prev : [nextQ]))
      } else {
        setError('No new question right now. Try again or continue to Generate Draft.')
      }
    } finally {
      setAskingId(null)
    }
  }



  async function generate(regenerate: boolean = false) {
    try {
      setBusy(true)
      setError(null)
      const res = await fetch('/api/onlypages/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ page_id: pageId, regenerate })
      })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Need more answers')
      setGenerated(data)
      setApproved(false) // new draft needs approval again
    } catch (e: any) {
      setError(e.message || 'Failed to generate')
    } finally {
      setBusy(false)
    }
  }

  async function publish() {
    try {
      setPublishing(true)
      setError(null)
      const res = await fetch('/api/onlypages/publish', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ page_id: pageId })
      })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Failed to publish')
      router.push(data.url)
    } catch (e: any) {
      setError(e.message || 'Failed to publish')
    } finally {
      setPublishing(false)
    }
  }

  useEffect(() => { boot().catch(() => {}) }, [])
  // Auto-generate a draft as soon as the interview is complete
  useEffect(() => {
    if (interviewComplete && !generated && !busy) {
      generate().catch(() => {})
    }
  }, [interviewComplete])


  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 py-8 sm:py-12">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-serif">OnlyPages Interview</h1>

          <div className="text-sm text-gray-600">
            {interviewComplete ? (
              <span className="text-green-600 font-medium">✓ Complete</span>
            ) : (
              <span>Question {progress.answered + 1} of ~7</span>
            )}
          </div>
        </div>
        {error && <div className="mb-4 p-3 rounded border border-red-200 bg-red-50 text-red-700 text-sm">{error}</div>}

        <div className="space-y-4">
          {qs.map((q) => (
            <div key={q.id} className="border rounded-lg p-3">
              <div className="text-sm text-gray-700 mb-2">{q.text}</div>
              <textarea
                className="w-full border rounded p-2 disabled:bg-gray-50 disabled:text-gray-500"
                rows={3}
                value={answers[q.id] || ''}
                onChange={(e) => setAnswers(prev => ({ ...prev, [q.id]: e.target.value }))}
                disabled={askingId === q.id}
              />
              <div className="mt-2 flex items-center gap-2">
                <button onClick={() => handleSaveAndNext(q.id)} disabled={!!askingId} className="px-3 py-1.5 rounded bg-gray-900 text-white text-sm disabled:opacity-50 flex items-center">
                  {askingId === q.id ? (<><SpinnerStar className="text-yellow-400" />{generatingNext ? 'Thinking…' : 'Saving…'}</>) : 'Save & Next'}
                </button>
                <button onClick={() => handleSkip(q.id)} disabled={!!askingId} className="px-3 py-1.5 rounded border text-sm disabled:opacity-50 flex items-center">
                  {askingId === q.id ? (<><SpinnerStar className="text-gray-400" />{'Skipping…'}</>) : 'Skip'}
                </button>
              </div>
            </div>
          ))}
        </div>

        {generatingNext && (
          <div className="mt-3 border rounded-lg p-3 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-2/3 mb-2" />
            <div className="h-20 bg-gray-100 rounded" />
          </div>
        )}

        {/* Hero Image Upload */}
        <div className="border rounded-lg p-4">
          <h3 className="font-medium text-gray-800 mb-3">Add a Hero Image (Optional)</h3>
          <p className="text-sm text-gray-600 mb-3">
            Upload a photo of the product, person, or subject to make your OnlyPage look more professional.
          </p>
          
          {heroImage ? (
            <div className="space-y-3">
              <img 
                src={heroImage} 
                alt="Hero image" 
                className="w-full h-48 object-cover rounded-lg border"
              />
              <button
                onClick={() => setHeroImage(null)}
                className="text-sm text-red-600 hover:text-red-800"
              >
                Remove Image
              </button>
            </div>
          ) : (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <input
                type="file"
                id="hero-upload"
                accept="image/*"
                className="hidden"
                onChange={(e) => {
                  const file = e.target.files?.[0]
                  if (file) handleImageUpload(file)
                }}
                disabled={uploading}
              />
              <label
                htmlFor="hero-upload"
                className={`cursor-pointer block ${uploading ? 'opacity-50' : 'hover:bg-gray-50'}`}
              >
                {uploading ? (
                  <div className="flex flex-col items-center">
                    <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mb-2"></div>
                    <span className="text-sm text-gray-600">Uploading...</span>
                  </div>
                ) : (
                  <div className="flex flex-col items-center">
                    <svg className="w-8 h-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="text-sm text-gray-600">Click to upload or drag and drop</span>
                    <span className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</span>
                  </div>
                )}
              </label>
              {uploadError && (
                <p className="text-sm text-red-600 mt-2">{uploadError}</p>
              )}
            </div>
          )}
        </div>


        {/* Interview Complete State */}
        {interviewComplete && (
          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-green-800 font-medium">Core Interview Complete</span>
            </div>

        {generatingNext && (
          <div className="border rounded-lg p-3 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-2/3 mb-2" />
            <div className="h-20 bg-gray-100 rounded" />
          </div>
        )}

            <p className="text-green-700 text-sm mb-3">
              Great! You've answered the core questions. You can now generate your article or ask for follow-up questions on specific topics.
            </p>

            {followupCount < 3 && !showFollowupForm && (
              <button
                onClick={() => setShowFollowupForm(true)}
                className="px-3 py-1.5 rounded bg-blue-600 text-white text-sm hover:bg-blue-700"
              >
                Ask More Questions ({3 - followupCount} remaining)
              </button>
            )}
          </div>
        )}

        {/* Follow-up Question Form */}
        {showFollowupForm && (
          <div className="mt-4 p-4 border border-blue-200 rounded-lg bg-blue-50">
            <h3 className="font-medium text-blue-900 mb-2">What would you like to be asked about?</h3>
            <p className="text-blue-700 text-sm mb-3">
              Tell us what aspect of your story you'd like to explore further. For example: "my biggest challenges," "the technical details," "future plans," etc.
            </p>
            <textarea
              className="w-full border rounded p-2 mb-3"
              rows={2}
              placeholder="I'd like to be asked about..."
              value={followupContext}
              onChange={(e) => setFollowupContext(e.target.value)}
              maxLength={500}
            />
            <div className="flex items-center gap-2">
              <button
                onClick={askFollowup}
                disabled={!followupContext.trim() || busy}
                className="px-3 py-1.5 rounded bg-blue-600 text-white text-sm disabled:opacity-50"
              >
                {busy ? (<><SpinnerStar className="text-blue-200" />Generating…</>) : 'Generate Follow-up Questions'}
              </button>
              <button
                onClick={() => setShowFollowupForm(false)}
                className="px-3 py-1.5 rounded border text-sm"
              >
                Cancel
              </button>
            </div>
            <div className="text-xs text-blue-600 mt-1">
              {followupContext.length}/500 characters • {3 - followupCount} follow-ups remaining
            </div>

          </div>
        )}

        {!generated && (
          <button
            onClick={() => generate()}
            disabled={busy}
            className={`mt-6 w-full rounded-lg px-4 py-2.5 font-medium disabled:opacity-50 ${
              interviewComplete
                ? 'bg-gradient-to-r from-green-600 to-emerald-600 text-white shadow-lg'
                : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white'
            }`}
          >
            {busy ? (<><SpinnerStar className="text-yellow-400" />Preparing Your Interview…</>) : (interviewComplete ? '✨ Generate Your Article' : 'Generate Draft')}
          </button>
        )}

        {interviewComplete && (
          <p className="text-center text-sm text-gray-600 mt-2">
            Ready to create your compelling article from the interview
          </p>
        )}

        {generated && (
          <div className="mt-6 border rounded-lg p-4">
            <div className="text-lg font-serif mb-2">{generated.article?.title}</div>
            <div className="text-gray-600 mb-3">{generated.article?.dek}</div>
            <div className="prose" dangerouslySetInnerHTML={{ __html: generated.article?.body_html || '' }} />

            <div className="mt-4 flex items-center gap-2">
              <button
                onClick={() => setApproved(true)}
                disabled={approved}
                className="px-3 py-1.5 rounded bg-green-600 text-white text-sm disabled:opacity-50"
              >
                {approved ? 'Approved' : 'Approve Draft'}
              </button>
              <button
                onClick={() => generate(true)}
                disabled={busy}
                className="px-3 py-1.5 rounded border text-sm disabled:opacity-50"
              >
                {busy ? (<><SpinnerStar className="text-gray-400" />Rewriting…</>) : 'Ask for Rewrite'}
              </button>
            </div>

            <div className="mt-3">
              <button
                onClick={publish}
                disabled={!approved || publishing}
                className="w-full rounded-lg px-4 py-2.5 font-medium bg-gradient-to-r from-indigo-600 to-purple-600 text-white disabled:opacity-50 flex items-center justify-center"
              >
                {publishing ? (<><SpinnerStar className="text-yellow-200" />Publishing…</>) : '🚀 Build Webpage'}
              </button>
              {!approved && (
                <p className="mt-2 text-xs text-gray-500 text-center">Approve the draft to enable publishing</p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
