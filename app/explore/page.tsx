import { Suspense } from 'react'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import OnlyPagesSearch from '@/components/OnlyPagesSearch'
import Link from 'next/link'

export const metadata = {
  title: 'Explore OnlyPages',
  description: 'Discover the most popular pages, trending content, and explore by category'
}

async function getTopPages() {
  const supabase = await createSupabaseServerClient()

  const { data: topPages } = await supabase
    .from('onlypages')
    .select(`
      id,
      slug,
      article,
      creator_name,
      categories,
      type,
      total_views,
      created_at
    `)
    .eq('status', 'published')
    .order('total_views', { ascending: false })
    .limit(20)

  return topPages || []
}

async function getCategories() {
  const supabase = await createSupabaseServerClient()

  const { data: categories } = await supabase
    .from('onlypages_categories')
    .select('*')
    .order('page_count', { ascending: false })

  return categories || []
}

async function getTrendingPages() {
  const supabase = await createSupabaseServerClient()

  const { data: trending } = await supabase
    .from('onlypages')
    .select(`
      id,
      slug,
      article,
      creator_name,
      categories,
      type,
      weekly_views,
      created_at
    `)
    .eq('status', 'published')
    .order('weekly_views', { ascending: false })
    .limit(10)

  return trending || []
}

function PageCard({ page }: { page: any }) {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'book': return '📚'
      case 'movie': return '🎬'
      case 'album': return '🎵'
      case 'press': return '📰'
      case 'site': return '🌐'
      case 'news': return '📢'
      default: return '📄'
    }
  }

  const formatViews = (views: number) => {
    if (views >= 1000000) return `${(views / 1000000).toFixed(1)}M`
    if (views >= 1000) return `${(views / 1000).toFixed(1)}K`
    return views?.toString() || '0'
  }

  return (
    <Link
      href={`/p/${page.slug}`}
      className="block group bg-white rounded-lg border border-gray-200 hover:border-violet-300
                 hover:shadow-lg transition-all duration-200 p-3 sm:p-4 touch-manipulation"
    >
      <div className="flex items-start space-x-3">
        <span className="text-xl sm:text-2xl flex-shrink-0">{getTypeIcon(page.type)}</span>
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-gray-900 group-hover:text-violet-700 text-sm sm:text-base line-clamp-2 sm:truncate">
            {page.article?.title || 'Untitled'}
          </h3>
          <p className="text-xs sm:text-sm text-gray-600 line-clamp-2 mt-1">
            {page.article?.dek || ''}
          </p>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-2 sm:mt-3 space-y-1 sm:space-y-0">
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500 truncate">by {page.creator_name || 'Anonymous'}</span>
              {page.categories?.[0] && (
                <span className="text-xs bg-violet-100 text-violet-700 px-2 py-1 rounded-full flex-shrink-0">
                  {page.categories[0]}
                </span>
              )}
            </div>
            <span className="text-xs text-gray-400 flex-shrink-0">
              {formatViews(page.total_views || page.weekly_views || 0)} views
            </span>
          </div>
        </div>
      </div>
    </Link>
  )
}

function CategoryCard({ category }: { category: any }) {
  return (
    <Link
      href={`/explore/category/${category.category.toLowerCase()}`}
      className="block group bg-gradient-to-br from-violet-50 to-purple-50 rounded-lg border border-violet-200
                 hover:from-violet-100 hover:to-purple-100 hover:border-violet-300
                 transition-all duration-200 p-3 sm:p-4 touch-manipulation"
    >
      <h3 className="font-semibold text-violet-900 group-hover:text-violet-700 mb-2 text-sm sm:text-base">
        {category.category}
      </h3>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between text-xs sm:text-sm text-violet-700 space-y-1 sm:space-y-0">
        <span>{category.page_count} pages</span>
        <span className="text-violet-600">{category.total_views || 0} views</span>
      </div>
    </Link>
  )
}

async function ExploreContent() {
  const [topPages, categories, trendingPages] = await Promise.all([
    getTopPages(),
    getCategories(),
    getTrendingPages()
  ])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-6 sm:py-8">
          <div className="text-center mb-6 sm:mb-8">
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4">
              Explore OnlyPages
            </h1>
            <p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto px-4">
              Discover the most popular pages, trending content, and explore by category
            </p>
          </div>

          {/* Wikipedia-style Search */}
          <div className="max-w-2xl mx-auto px-4 sm:px-0">
            <OnlyPagesSearch
              placeholder="Search OnlyPages..."
              className="w-full"
            />
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div className="grid lg:grid-cols-3 gap-6 sm:gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6 sm:space-y-8">
            {/* Trending This Week */}
            <section>
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 flex items-center px-1">
                🔥 Trending This Week
              </h2>
              <div className="space-y-3 sm:space-y-4">
                {trendingPages.slice(0, 5).map((page, index) => (
                  <div key={page.id} className="flex items-start space-x-3 sm:space-x-4">
                    <div className="flex-shrink-0 w-7 h-7 sm:w-8 sm:h-8 bg-violet-600 text-white rounded-full
                                  flex items-center justify-center text-xs sm:text-sm font-bold mt-1">
                      {index + 1}
                    </div>
                    <div className="flex-1 min-w-0">
                      <PageCard page={page} />
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Top 100 All Time */}
            <section>
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 flex items-center px-1">
                👑 Top Pages All Time
              </h2>
              <div className="space-y-3 sm:space-y-4">
                {topPages.map((page, index) => (
                  <div key={page.id} className="flex items-start space-x-3 sm:space-x-4">
                    <div className="flex-shrink-0 w-7 h-7 sm:w-8 sm:h-8 bg-amber-600 text-white rounded-full
                                  flex items-center justify-center text-xs sm:text-sm font-bold mt-1">
                      {index + 1}
                    </div>
                    <div className="flex-1 min-w-0">
                      <PageCard page={page} />
                    </div>
                  </div>
                ))}
              </div>

              {topPages.length >= 20 && (
                <div className="text-center mt-6 px-4">
                  <Link
                    href="/explore/top-100"
                    className="inline-flex items-center px-4 sm:px-6 py-2.5 sm:py-3 bg-violet-600 text-white font-semibold
                             rounded-lg hover:bg-violet-700 transition-colors duration-200 text-sm sm:text-base touch-manipulation"
                  >
                    View Full Top 100
                    <svg className="w-4 h-4 sm:w-5 sm:h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </Link>
                </div>
              )}
            </section>
          </div>

          {/* Sidebar */}
          <div className="space-y-6 sm:space-y-8">
            {/* Categories */}
            <section>
              <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4 px-1">
                Browse by Category
              </h2>
              <div className="space-y-2 sm:space-y-3">
                {categories.slice(0, 10).map((category) => (
                  <CategoryCard key={category.category} category={category} />
                ))}
              </div>
              {categories.length > 10 && (
                <div className="mt-3 sm:mt-4 px-1">
                  <Link
                    href="/explore/categories"
                    className="text-violet-600 hover:text-violet-700 font-medium text-sm touch-manipulation"
                  >
                    View all categories →
                  </Link>
                </div>
              )}
            </section>

            {/* Quick Stats */}
            <section className="bg-white rounded-lg border border-gray-200 p-4 sm:p-6">
              <h2 className="text-lg font-bold text-gray-900 mb-3 sm:mb-4">
                OnlyPages Stats
              </h2>
              <div className="space-y-2 sm:space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm sm:text-base">Total Pages</span>
                  <span className="font-semibold text-sm sm:text-base">{topPages.length}+</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm sm:text-base">Categories</span>
                  <span className="font-semibold text-sm sm:text-base">{categories.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm sm:text-base">Total Views</span>
                  <span className="font-semibold text-sm sm:text-base">
                    {categories.reduce((sum, cat) => sum + (cat.total_views || 0), 0).toLocaleString()}
                  </span>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function ExplorePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-2 border-violet-500 border-t-transparent rounded-full"></div>
      </div>
    }>
      <ExploreContent />
    </Suspense>
  )
}