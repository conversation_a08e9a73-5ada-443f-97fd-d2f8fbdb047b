import { createSupabaseServerClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { notFound } from 'next/navigation'

interface CategoryPageProps {
  params: Promise<{ category: string }>
}

export async function generateMetadata({ params }: CategoryPageProps) {
  const { category } = await params
  const categoryName = decodeURIComponent(category)

  return {
    title: `${categoryName} OnlyPages`,
    description: `Explore the best ${categoryName} pages on OnlyPages`
  }
}

async function getCategoryPages(category: string) {
  const supabase = await createSupabaseServerClient()

  const { data: pages } = await supabase
    .from('onlypages')
    .select(`
      id,
      slug,
      article,
      creator_name,
      categories,
      type,
      total_views,
      created_at
    `)
    .eq('status', 'published')
    .contains('categories', [category])
    .order('total_views', { ascending: false })
    .limit(50)

  return pages || []
}

async function getCategoryStats(category: string) {
  const supabase = await createSupabaseServerClient()

  const { data: stats } = await supabase
    .from('onlypages_categories')
    .select('*')
    .eq('category', category)
    .single()

  return stats
}

function getTypeIcon(type: string) {
  switch (type) {
    case 'book': return '📚'
    case 'movie': return '🎬'
    case 'album': return '🎵'
    case 'press': return '📰'
    case 'site': return '🌐'
    case 'news': return '📢'
    default: return '📄'
  }
}

function formatViews(views: number) {
  if (views >= 1000000) return `${(views / 1000000).toFixed(1)}M`
  if (views >= 1000) return `${(views / 1000).toFixed(1)}K`
  return views?.toString() || '0'
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { category } = await params
  const categoryName = decodeURIComponent(category)

  const [pages, stats] = await Promise.all([
    getCategoryPages(categoryName),
    getCategoryStats(categoryName)
  ])

  if (!stats && pages.length === 0) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center space-x-4 mb-6">
            <Link
              href="/explore"
              className="text-violet-600 hover:text-violet-700 font-medium"
            >
              ← Back to Explore
            </Link>
          </div>

          <div className="text-center">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              {categoryName}
            </h1>
            <p className="text-lg text-gray-600 mb-6">
              Explore the best {categoryName.toLowerCase()} pages on OnlyPages
            </p>

            {stats && (
              <div className="flex justify-center space-x-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-violet-600">{stats.page_count}</div>
                  <div className="text-sm text-gray-600">Pages</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-violet-600">
                    {formatViews(stats.total_views || 0)}
                  </div>
                  <div className="text-sm text-gray-600">Total Views</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Pages */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {pages.length > 0 ? (
          <div className="space-y-4">
            {pages.map((page, index) => (
              <div
                key={page.id}
                className="bg-white rounded-lg border border-gray-200 hover:border-violet-300
                         hover:shadow-lg transition-all duration-200 overflow-hidden"
              >
                <Link href={`/p/${page.slug}`} className="block p-4">
                  <div className="flex items-center space-x-4">
                    {/* Rank */}
                    <div className="flex-shrink-0 w-8 h-8 bg-violet-100 text-violet-700 rounded-full
                                  flex items-center justify-center text-sm font-bold">
                      {index + 1}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start space-x-3">
                        <span className="text-2xl">{getTypeIcon(page.type)}</span>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-gray-900 hover:text-violet-700 truncate text-lg">
                            {page.article?.title || 'Untitled'}
                          </h3>
                          <p className="text-gray-600 line-clamp-2 mt-1">
                            {page.article?.dek || ''}
                          </p>
                          <div className="flex items-center justify-between mt-3">
                            <div className="flex items-center space-x-3">
                              <span className="text-sm text-gray-500">
                                by {page.creator_name || 'Anonymous'}
                              </span>
                              <span className="text-xs text-gray-400">
                                {new Date(page.created_at).toLocaleDateString()}
                              </span>
                            </div>
                            <span className="text-sm font-semibold text-gray-900">
                              {formatViews(page.total_views || 0)} views
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No pages found</h3>
            <p className="text-gray-600">No pages have been categorized under {categoryName} yet.</p>
          </div>
        )}
      </div>
    </div>
  )
}