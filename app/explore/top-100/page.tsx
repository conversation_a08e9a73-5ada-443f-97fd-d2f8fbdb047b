import { createSupabaseServerClient } from '@/lib/supabase/client'
import Link from 'next/link'

export const metadata = {
  title: 'Top 100 OnlyPages',
  description: 'The most popular pages on OnlyPages, ranked by total views'
}

async function getTop100Pages() {
  const supabase = await createSupabaseServerClient()

  const { data: pages } = await supabase
    .from('onlypages_rankings')
    .select(`
      rank_position,
      score,
      page_id,
      onlypages!inner (
        slug,
        article,
        creator_name,
        categories,
        type,
        created_at
      )
    `)
    .eq('rank_type', 'overall')
    .eq('rank_key', 'all')
    .order('rank_position', { ascending: true })

  return pages || []
}

function getRankStyle(rank: number) {
  if (rank === 1) return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white'
  if (rank === 2) return 'bg-gradient-to-r from-gray-400 to-gray-600 text-white'
  if (rank === 3) return 'bg-gradient-to-r from-amber-600 to-amber-800 text-white'
  if (rank <= 10) return 'bg-gradient-to-r from-violet-600 to-purple-600 text-white'
  if (rank <= 25) return 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
  if (rank <= 50) return 'bg-gradient-to-r from-green-500 to-green-600 text-white'
  return 'bg-gray-100 text-gray-700 border border-gray-300'
}

function getTypeIcon(type: string) {
  switch (type) {
    case 'book': return '📚'
    case 'movie': return '🎬'
    case 'album': return '🎵'
    case 'press': return '📰'
    case 'site': return '🌐'
    case 'news': return '📢'
    default: return '📄'
  }
}

function formatViews(views: number) {
  if (views >= 1000000) return `${(views / 1000000).toFixed(1)}M`
  if (views >= 1000) return `${(views / 1000).toFixed(1)}K`
  return views.toString()
}

export default async function Top100Page() {
  const rankings = await getTop100Pages()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 py-6 sm:py-8">
          <div className="text-center">
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4">
              🏆 Top 100 OnlyPages
            </h1>
            <p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto mb-4 sm:mb-6 px-4">
              The most popular pages on OnlyPages, ranked by total views
            </p>
            <Link
              href="/explore"
              className="inline-flex items-center text-violet-600 hover:text-violet-700 font-medium text-sm sm:text-base touch-manipulation"
            >
              ← Back to Explore
            </Link>
          </div>
        </div>
      </div>

      {/* Rankings */}
      <div className="max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div className="space-y-2 sm:space-y-3">
          {rankings.map((ranking) => {
            const page = ranking.onlypages
            const rank = ranking.rank_position
            const views = ranking.score

            return (
              <div
                key={ranking.page_id}
                className="bg-white rounded-lg border border-gray-200 hover:border-violet-300
                         hover:shadow-lg transition-all duration-200 overflow-hidden"
              >
                <Link href={`/p/${page.slug}`} className="block p-3 sm:p-4 touch-manipulation">
                  <div className="flex items-center space-x-3 sm:space-x-4">
                    {/* Rank Badge */}
                    <div className={`flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center
                                   font-bold text-base sm:text-lg ${getRankStyle(rank)}`}>
                      {rank}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start space-x-2 sm:space-x-3">
                        <span className="text-lg sm:text-2xl flex-shrink-0">{getTypeIcon(page.type)}</span>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-gray-900 hover:text-violet-700 text-sm sm:text-lg line-clamp-2 sm:truncate">
                            {page.article?.title || 'Untitled'}
                          </h3>
                          <p className="text-gray-600 line-clamp-2 mt-1 text-xs sm:text-base">
                            {page.article?.dek || ''}
                          </p>
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-2 sm:mt-3 space-y-2 sm:space-y-0">
                            <div className="flex items-center space-x-2 sm:space-x-3">
                              <span className="text-xs sm:text-sm text-gray-500 truncate">
                                by {page.creator_name || 'Anonymous'}
                              </span>
                              {page.categories?.[0] && (
                                <span className="text-xs bg-violet-100 text-violet-700 px-2 py-1 rounded-full flex-shrink-0">
                                  {page.categories[0]}
                                </span>
                              )}
                            </div>
                            <div className="flex items-center justify-between sm:justify-end space-x-3 sm:space-x-4">
                              <span className="text-xs sm:text-sm font-semibold text-gray-900">
                                {formatViews(views)} views
                              </span>
                              {rank <= 3 && (
                                <span className="text-base sm:text-lg">
                                  {rank === 1 ? '🥇' : rank === 2 ? '🥈' : '🥉'}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            )
          })}

          {rankings.length === 0 && (
            <div className="text-center py-8 sm:py-12 px-4">
              <div className="text-gray-400 mb-4">
                <svg className="mx-auto h-10 w-10 sm:h-12 sm:w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33" />
                </svg>
              </div>
              <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">No rankings available yet</h3>
              <p className="text-sm sm:text-base text-gray-600">Rankings will appear as pages get more views.</p>
            </div>
          )}
        </div>

        {/* Pagination info */}
        {rankings.length > 0 && (
          <div className="mt-8 sm:mt-12 text-center px-4">
            <p className="text-sm sm:text-base text-gray-600">
              Showing top {rankings.length} pages • Rankings updated hourly
            </p>
          </div>
        )}
      </div>
    </div>
  )
}