-- PrizePicks Tables Creation
-- Run this SQL in your Supabase SQL editor

-- Core props tracking table
CREATE TABLE IF NOT EXISTS prizepicks_props (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  external_id text UNIQUE NOT NULL, -- PrizePicks prop ID
  sport text NOT NULL, -- 'NFL', 'NCAAF', 'Tennis', 'WNBA', 'NBA'
  player_name text NOT NULL,
  team text,
  opponent text,
  prop_type text NOT NULL, -- 'passing_yards', 'rushing_yards', 'receiving_yards', etc.
  line_value decimal NOT NULL,
  game_time timestamptz,
  is_active boolean DEFAULT true,
  scraped_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Line movement history
CREATE TABLE IF NOT EXISTS line_movements (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  prop_id uuid REFERENCES prizepicks_props(id) ON DELETE CASCADE,
  old_value decimal,
  new_value decimal NOT NULL,
  movement_size decimal GENERATED ALWAYS AS (new_value - old_value) STORED,
  detected_at timestamptz DEFAULT now()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_prizepicks_props_sport_active ON prizepicks_props(sport, is_active);
CREATE INDEX IF NOT EXISTS idx_prizepicks_props_game_time ON prizepicks_props(game_time) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_line_movements_prop_id ON line_movements(prop_id);

-- RLS Policies
ALTER TABLE prizepicks_props ENABLE ROW LEVEL SECURITY;
ALTER TABLE line_movements ENABLE ROW LEVEL SECURITY;

-- Allow read access for authenticated users
DROP POLICY IF EXISTS "Allow read access to props" ON prizepicks_props;
CREATE POLICY "Allow read access to props" ON prizepicks_props
  FOR SELECT TO authenticated USING (true);

DROP POLICY IF EXISTS "Allow read access to movements" ON line_movements;
CREATE POLICY "Allow read access to movements" ON line_movements
  FOR SELECT TO authenticated USING (true);

-- Allow service role full access for scraping
DROP POLICY IF EXISTS "Allow service insert props" ON prizepicks_props;
CREATE POLICY "Allow service insert props" ON prizepicks_props
  FOR INSERT TO service_role WITH CHECK (true);

DROP POLICY IF EXISTS "Allow service update props" ON prizepicks_props;
CREATE POLICY "Allow service update props" ON prizepicks_props
  FOR UPDATE TO service_role USING (true);