// Pre-curated beautiful theme combinations for OnlyPages
// Eliminates AI generation costs while ensuring design quality

export interface ThemePreset {
  id: string
  name: string
  description: string
  accent: string
  bg: string
  ink: string
  muted: string
  family: 'serif' | 'sans' | 'mono' | 'display'
  mood: 'elegant' | 'modern' | 'warm' | 'bold' | 'minimal' | 'creative'
  bestFor: string[]
}

export const THEME_PRESETS: ThemePreset[] = [
  // Elegant Themes
  {
    id: 'elegant-royal',
    name: 'Royal Purple',
    description: 'Sophisticated purple with elegant serif typography',
    accent: '#8b5cf6',
    bg: '#ffffff',
    ink: '#111827',
    muted: '#6b7280',
    family: 'serif',
    mood: 'elegant',
    bestFor: ['book', 'life', 'wiki']
  },
  {
    id: 'elegant-emerald',
    name: 'Emerald Classic',
    description: 'Refined emerald green with traditional typography',
    accent: '#10b981',
    bg: '#ffffff',
    ink: '#1f2937',
    muted: '#6b7280',
    family: 'serif',
    mood: 'elegant',
    bestFor: ['book', 'wiki', 'life']
  },
  {
    id: 'elegant-sapphire',
    name: 'Sapphire Elite',
    description: 'Deep blue elegance with premium feel',
    accent: '#1e40af',
    bg: '#ffffff',
    ink: '#111827',
    muted: '#64748b',
    family: 'serif',
    mood: 'elegant',
    bestFor: ['press', 'news', 'wiki']
  },

  // Modern Themes
  {
    id: 'modern-electric',
    name: 'Electric Blue',
    description: 'Vibrant modern blue with clean sans-serif',
    accent: '#3b82f6',
    bg: '#ffffff',
    ink: '#1f2937',
    muted: '#6b7280',
    family: 'sans',
    mood: 'modern',
    bestFor: ['site', 'press', 'custom']
  },
  {
    id: 'modern-cyber',
    name: 'Cyber Violet',
    description: 'Futuristic violet with modern typography',
    accent: '#7c3aed',
    bg: '#fafafa',
    ink: '#18181b',
    muted: '#71717a',
    family: 'sans',
    mood: 'modern',
    bestFor: ['site', 'press', 'custom']
  },
  {
    id: 'modern-mint',
    name: 'Fresh Mint',
    description: 'Clean mint green with contemporary feel',
    accent: '#06d6a0',
    bg: '#ffffff',
    ink: '#1f2937',
    muted: '#6b7280',
    family: 'sans',
    mood: 'modern',
    bestFor: ['site', 'life', 'custom']
  },

  // Warm Themes
  {
    id: 'warm-sunset',
    name: 'Sunset Amber',
    description: 'Warm amber with cream background',
    accent: '#f59e0b',
    bg: '#fffbeb',
    ink: '#1f2937',
    muted: '#78716c',
    family: 'serif',
    mood: 'warm',
    bestFor: ['life', 'book', 'album']
  },
  {
    id: 'warm-copper',
    name: 'Copper Glow',
    description: 'Rich copper with warm undertones',
    accent: '#ea580c',
    bg: '#fff7ed',
    ink: '#1c1917',
    muted: '#78716c',
    family: 'serif',
    mood: 'warm',
    bestFor: ['book', 'life', 'album']
  },
  {
    id: 'warm-rose',
    name: 'Rose Gold',
    description: 'Elegant rose with soft background',
    accent: '#e11d48',
    bg: '#fef7f7',
    ink: '#1f2937',
    muted: '#6b7280',
    family: 'serif',
    mood: 'warm',
    bestFor: ['album', 'movie', 'life']
  },

  // Bold Themes
  {
    id: 'bold-crimson',
    name: 'Crimson Power',
    description: 'Bold red for impactful statements',
    accent: '#dc2626',
    bg: '#ffffff',
    ink: '#111827',
    muted: '#6b7280',
    family: 'display',
    mood: 'bold',
    bestFor: ['press', 'news', 'movie']
  },
  {
    id: 'bold-ocean',
    name: 'Ocean Deep',
    description: 'Deep ocean blue with strong presence',
    accent: '#1e40af',
    bg: '#ffffff',
    ink: '#1e293b',
    muted: '#64748b',
    family: 'display',
    mood: 'bold',
    bestFor: ['press', 'site', 'news']
  },
  {
    id: 'bold-forest',
    name: 'Forest Strong',
    description: 'Bold forest green with authority',
    accent: '#166534',
    bg: '#ffffff',
    ink: '#14532d',
    muted: '#6b7280',
    family: 'display',
    mood: 'bold',
    bestFor: ['press', 'news', 'site']
  },

  // Minimal Themes
  {
    id: 'minimal-slate',
    name: 'Pure Slate',
    description: 'Minimal gray with perfect balance',
    accent: '#475569',
    bg: '#ffffff',
    ink: '#1e293b',
    muted: '#64748b',
    family: 'sans',
    mood: 'minimal',
    bestFor: ['wiki', 'site', 'custom']
  },
  {
    id: 'minimal-charcoal',
    name: 'Charcoal Zen',
    description: 'Sophisticated charcoal minimalism',
    accent: '#374151',
    bg: '#fafafa',
    ink: '#111827',
    muted: '#6b7280',
    family: 'sans',
    mood: 'minimal',
    bestFor: ['wiki', 'life', 'custom']
  },
  {
    id: 'minimal-storm',
    name: 'Storm Gray',
    description: 'Clean storm gray with subtle elegance',
    accent: '#6b7280',
    bg: '#ffffff',
    ink: '#1f2937',
    muted: '#9ca3af',
    family: 'sans',
    mood: 'minimal',
    bestFor: ['wiki', 'site', 'book']
  },

  // Creative Themes
  {
    id: 'creative-aurora',
    name: 'Aurora Purple',
    description: 'Vibrant purple with creative energy',
    accent: '#a855f7',
    bg: '#faf5ff',
    ink: '#581c87',
    muted: '#7c3aed',
    family: 'display',
    mood: 'creative',
    bestFor: ['album', 'movie', 'custom']
  },
  {
    id: 'creative-neon',
    name: 'Neon Lime',
    description: 'Electric lime with artistic flair',
    accent: '#84cc16',
    bg: '#f7fee7',
    ink: '#1a2e05',
    muted: '#4d7c0f',
    family: 'display',
    mood: 'creative',
    bestFor: ['album', 'movie', 'site']
  },
  {
    id: 'creative-magenta',
    name: 'Magenta Burst',
    description: 'Bold magenta with creative spirit',
    accent: '#ec4899',
    bg: '#fdf2f8',
    ink: '#831843',
    muted: '#be185d',
    family: 'display',
    mood: 'creative',
    bestFor: ['album', 'movie', 'custom']
  },

  // Tech/Code Themes
  {
    id: 'tech-matrix',
    name: 'Matrix Green',
    description: 'Coding-inspired green with monospace',
    accent: '#22c55e',
    bg: '#0f172a',
    ink: '#f1f5f9',
    muted: '#94a3b8',
    family: 'mono',
    mood: 'modern',
    bestFor: ['site', 'custom']
  },
  {
    id: 'tech-terminal',
    name: 'Terminal Blue',
    description: 'Developer-friendly blue terminal theme',
    accent: '#0ea5e9',
    bg: '#020617',
    ink: '#f8fafc',
    muted: '#cbd5e1',
    family: 'mono',
    mood: 'modern',
    bestFor: ['site', 'custom']
  }
]

// Smart theme selection based on template and content type
export function selectTheme(
  templateId: string,
  contentType: string,
  templateMood?: string
): ThemePreset {
  // Filter themes that work well for this content type
  const suitableThemes = THEME_PRESETS.filter(theme =>
    theme.bestFor.includes(contentType)
  )

  // If we have template mood preference, prioritize matching themes
  if (templateMood && suitableThemes.length > 0) {
    const moodMatches = suitableThemes.filter(theme => theme.mood === templateMood)
    if (moodMatches.length > 0) {
      // Return random theme from mood matches
      return moodMatches[Math.floor(Math.random() * moodMatches.length)]
    }
  }

  // Fall back to any suitable theme
  if (suitableThemes.length > 0) {
    return suitableThemes[Math.floor(Math.random() * suitableThemes.length)]
  }

  // Ultimate fallback to elegant royal (most versatile)
  return THEME_PRESETS[0]
}

// Get theme by ID
export function getThemeById(id: string): ThemePreset | null {
  return THEME_PRESETS.find(theme => theme.id === id) || null
}

// Get all themes for a specific mood
export function getThemesByMood(mood: string): ThemePreset[] {
  return THEME_PRESETS.filter(theme => theme.mood === mood)
}

// Get all themes suitable for a content type
export function getThemesForContentType(contentType: string): ThemePreset[] {
  return THEME_PRESETS.filter(theme => theme.bestFor.includes(contentType))
}