// OnlyPages Template System - 5 Beautiful Layouts Per Category
// Each template provides unique visual presentation optimized for content type

export interface TemplateConfig {
  id: string
  name: string
  description: string
  category: string
  heroStyle: 'banner' | 'side-by-side' | 'overlay' | 'minimal' | 'magazine'
  layout: 'standard' | 'split' | 'cards' | 'timeline' | 'showcase'
  typography: 'serif' | 'sans' | 'mono' | 'display'
  spacing: 'tight' | 'standard' | 'airy' | 'editorial'
  emphasis: 'visual' | 'text' | 'balanced' | 'immersive'
}

// Template Categories
export const CATEGORIES = [
  'press', 'news', 'book', 'movie', 'album',
  'site', 'life', 'wiki', 'custom'
] as const

export type Category = typeof CATEGORIES[number]

// Template Definitions - 5 per category = 45 total
export const TEMPLATES: Record<Category, TemplateConfig[]> = {
  press: [
    {
      id: 'press-announce',
      name: 'The Announcement',
      description: 'Bold launch presentation with dramatic visuals',
      category: 'press',
      heroStyle: 'banner',
      layout: 'showcase',
      typography: 'sans',
      spacing: 'airy',
      emphasis: 'visual'
    },
    {
      id: 'press-feature',
      name: 'Feature Story',
      description: 'Magazine-style deep dive with rich formatting',
      category: 'press',
      heroStyle: 'magazine',
      layout: 'editorial',
      typography: 'serif',
      spacing: 'editorial',
      emphasis: 'balanced'
    },
    {
      id: 'press-timeline',
      name: 'Journey Timeline',
      description: 'Chronological story with milestone highlights',
      category: 'press',
      heroStyle: 'minimal',
      layout: 'timeline',
      typography: 'sans',
      spacing: 'standard',
      emphasis: 'text'
    },
    {
      id: 'press-impact',
      name: 'Impact Report',
      description: 'Data-driven story with visual emphasis',
      category: 'press',
      heroStyle: 'side-by-side',
      layout: 'cards',
      typography: 'sans',
      spacing: 'tight',
      emphasis: 'visual'
    },
    {
      id: 'press-intimate',
      name: 'Behind the Scenes',
      description: 'Personal perspective with warm typography',
      category: 'press',
      heroStyle: 'overlay',
      layout: 'standard',
      typography: 'serif',
      spacing: 'airy',
      emphasis: 'text'
    }
  ],

  news: [
    {
      id: 'news-breaking',
      name: 'Breaking News',
      description: 'Urgent, clear presentation with strong hierarchy',
      category: 'news',
      heroStyle: 'banner',
      layout: 'standard',
      typography: 'sans',
      spacing: 'tight',
      emphasis: 'text'
    },
    {
      id: 'news-investigation',
      name: 'Deep Investigation',
      description: 'Long-form journalism with rich media integration',
      category: 'news',
      heroStyle: 'magazine',
      layout: 'split',
      typography: 'serif',
      spacing: 'editorial',
      emphasis: 'balanced'
    },
    {
      id: 'news-analysis',
      name: 'Expert Analysis',
      description: 'Thoughtful commentary with clear structure',
      category: 'news',
      heroStyle: 'minimal',
      layout: 'cards',
      typography: 'serif',
      spacing: 'standard',
      emphasis: 'text'
    },
    {
      id: 'news-live',
      name: 'Live Coverage',
      description: 'Real-time updates with timeline presentation',
      category: 'news',
      heroStyle: 'side-by-side',
      layout: 'timeline',
      typography: 'sans',
      spacing: 'tight',
      emphasis: 'visual'
    },
    {
      id: 'news-feature',
      name: 'Feature Story',
      description: 'Human interest with immersive storytelling',
      category: 'news',
      heroStyle: 'overlay',
      layout: 'showcase',
      typography: 'serif',
      spacing: 'airy',
      emphasis: 'immersive'
    }
  ],

  book: [
    {
      id: 'book-classic',
      name: 'Literary Classic',
      description: 'Elegant typography emphasizing the written word',
      category: 'book',
      heroStyle: 'minimal',
      layout: 'standard',
      typography: 'serif',
      spacing: 'editorial',
      emphasis: 'text'
    },
    {
      id: 'book-modern',
      name: 'Modern Review',
      description: 'Contemporary design with visual elements',
      category: 'book',
      heroStyle: 'side-by-side',
      layout: 'split',
      typography: 'sans',
      spacing: 'airy',
      emphasis: 'balanced'
    },
    {
      id: 'book-author',
      name: 'Author Spotlight',
      description: 'Personal perspective with intimate layout',
      category: 'book',
      heroStyle: 'overlay',
      layout: 'showcase',
      typography: 'serif',
      spacing: 'airy',
      emphasis: 'immersive'
    },
    {
      id: 'book-academic',
      name: 'Scholarly Analysis',
      description: 'Structured presentation for deep analysis',
      category: 'book',
      heroStyle: 'banner',
      layout: 'cards',
      typography: 'serif',
      spacing: 'standard',
      emphasis: 'text'
    },
    {
      id: 'book-journey',
      name: 'Reading Journey',
      description: 'Chapter-by-chapter exploration with timeline',
      category: 'book',
      heroStyle: 'magazine',
      layout: 'timeline',
      typography: 'serif',
      spacing: 'editorial',
      emphasis: 'balanced'
    }
  ],

  movie: [
    {
      id: 'movie-cinematic',
      name: 'Cinematic Experience',
      description: 'Immersive visual storytelling like film itself',
      category: 'movie',
      heroStyle: 'overlay',
      layout: 'showcase',
      typography: 'sans',
      spacing: 'airy',
      emphasis: 'immersive'
    },
    {
      id: 'movie-review',
      name: 'Critical Review',
      description: 'Professional critique with structured analysis',
      category: 'movie',
      heroStyle: 'side-by-side',
      layout: 'split',
      typography: 'serif',
      spacing: 'standard',
      emphasis: 'balanced'
    },
    {
      id: 'movie-behind',
      name: 'Behind the Camera',
      description: 'Production story with timeline presentation',
      category: 'movie',
      heroStyle: 'magazine',
      layout: 'timeline',
      typography: 'sans',
      spacing: 'editorial',
      emphasis: 'visual'
    },
    {
      id: 'movie-character',
      name: 'Character Study',
      description: 'Deep dive into characters with card layout',
      category: 'movie',
      heroStyle: 'banner',
      layout: 'cards',
      typography: 'serif',
      spacing: 'airy',
      emphasis: 'text'
    },
    {
      id: 'movie-festival',
      name: 'Festival Spotlight',
      description: 'Elegant presentation for film festivals',
      category: 'movie',
      heroStyle: 'minimal',
      layout: 'standard',
      typography: 'display',
      spacing: 'editorial',
      emphasis: 'balanced'
    }
  ],

  album: [
    {
      id: 'album-sonic',
      name: 'Sonic Journey',
      description: 'Visual interpretation of musical experience',
      category: 'album',
      heroStyle: 'overlay',
      layout: 'showcase',
      typography: 'sans',
      spacing: 'airy',
      emphasis: 'immersive'
    },
    {
      id: 'album-track',
      name: 'Track by Track',
      description: 'Song-by-song analysis with timeline flow',
      category: 'album',
      heroStyle: 'side-by-side',
      layout: 'timeline',
      typography: 'serif',
      spacing: 'standard',
      emphasis: 'text'
    },
    {
      id: 'album-artist',
      name: 'Artist Portrait',
      description: 'Personal story behind the music',
      category: 'album',
      heroStyle: 'magazine',
      layout: 'split',
      typography: 'serif',
      spacing: 'editorial',
      emphasis: 'balanced'
    },
    {
      id: 'album-vinyl',
      name: 'Vinyl Collection',
      description: 'Classic album presentation with rich typography',
      category: 'album',
      heroStyle: 'minimal',
      layout: 'standard',
      typography: 'serif',
      spacing: 'editorial',
      emphasis: 'text'
    },
    {
      id: 'album-genre',
      name: 'Genre Explorer',
      description: 'Musical context with card-based layout',
      category: 'album',
      heroStyle: 'banner',
      layout: 'cards',
      typography: 'sans',
      spacing: 'tight',
      emphasis: 'visual'
    }
  ],

  site: [
    {
      id: 'site-showcase',
      name: 'Product Showcase',
      description: 'Clean, modern presentation highlighting features',
      category: 'site',
      heroStyle: 'side-by-side',
      layout: 'showcase',
      typography: 'sans',
      spacing: 'airy',
      emphasis: 'visual'
    },
    {
      id: 'site-story',
      name: 'Founder Story',
      description: 'Personal narrative behind the product',
      category: 'site',
      heroStyle: 'overlay',
      layout: 'standard',
      typography: 'serif',
      spacing: 'editorial',
      emphasis: 'text'
    },
    {
      id: 'site-technical',
      name: 'Technical Deep Dive',
      description: 'Developer-focused with code-friendly typography',
      category: 'site',
      heroStyle: 'minimal',
      layout: 'split',
      typography: 'mono',
      spacing: 'tight',
      emphasis: 'text'
    },
    {
      id: 'site-journey',
      name: 'Development Journey',
      description: 'Timeline of product evolution',
      category: 'site',
      heroStyle: 'banner',
      layout: 'timeline',
      typography: 'sans',
      spacing: 'standard',
      emphasis: 'balanced'
    },
    {
      id: 'site-community',
      name: 'Community Impact',
      description: 'User stories and testimonials',
      category: 'site',
      heroStyle: 'magazine',
      layout: 'cards',
      typography: 'sans',
      spacing: 'airy',
      emphasis: 'visual'
    }
  ],

  life: [
    {
      id: 'life-memoir',
      name: 'Personal Memoir',
      description: 'Intimate storytelling with warm typography',
      category: 'life',
      heroStyle: 'overlay',
      layout: 'standard',
      typography: 'serif',
      spacing: 'editorial',
      emphasis: 'text'
    },
    {
      id: 'life-milestone',
      name: 'Life Milestones',
      description: 'Key moments presented in timeline format',
      category: 'life',
      heroStyle: 'side-by-side',
      layout: 'timeline',
      typography: 'serif',
      spacing: 'airy',
      emphasis: 'balanced'
    },
    {
      id: 'life-wisdom',
      name: 'Life Lessons',
      description: 'Thoughtful reflections with elegant layout',
      category: 'life',
      heroStyle: 'minimal',
      layout: 'cards',
      typography: 'serif',
      spacing: 'editorial',
      emphasis: 'text'
    },
    {
      id: 'life-adventure',
      name: 'Adventure Story',
      description: 'Dynamic presentation for exciting journeys',
      category: 'life',
      heroStyle: 'magazine',
      layout: 'showcase',
      typography: 'sans',
      spacing: 'airy',
      emphasis: 'immersive'
    },
    {
      id: 'life-legacy',
      name: 'Living Legacy',
      description: 'Comprehensive life story with rich media',
      category: 'life',
      heroStyle: 'banner',
      layout: 'split',
      typography: 'serif',
      spacing: 'editorial',
      emphasis: 'balanced'
    }
  ],

  wiki: [
    {
      id: 'wiki-comprehensive',
      name: 'Complete Reference',
      description: 'Wikipedia-style comprehensive coverage',
      category: 'wiki',
      heroStyle: 'minimal',
      layout: 'standard',
      typography: 'serif',
      spacing: 'standard',
      emphasis: 'text'
    },
    {
      id: 'wiki-timeline',
      name: 'Historical Timeline',
      description: 'Chronological presentation of facts',
      category: 'wiki',
      heroStyle: 'banner',
      layout: 'timeline',
      typography: 'serif',
      spacing: 'tight',
      emphasis: 'text'
    },
    {
      id: 'wiki-visual',
      name: 'Visual Encyclopedia',
      description: 'Image-rich presentation with detailed captions',
      category: 'wiki',
      heroStyle: 'magazine',
      layout: 'cards',
      typography: 'sans',
      spacing: 'airy',
      emphasis: 'visual'
    },
    {
      id: 'wiki-academic',
      name: 'Scholarly Article',
      description: 'Academic paper style with citations',
      category: 'wiki',
      heroStyle: 'side-by-side',
      layout: 'split',
      typography: 'serif',
      spacing: 'editorial',
      emphasis: 'text'
    },
    {
      id: 'wiki-modern',
      name: 'Modern Knowledge',
      description: 'Contemporary take on reference material',
      category: 'wiki',
      heroStyle: 'overlay',
      layout: 'showcase',
      typography: 'sans',
      spacing: 'airy',
      emphasis: 'balanced'
    }
  ],

  custom: [
    {
      id: 'custom-elegant',
      name: 'Elegant Presentation',
      description: 'Sophisticated layout for any content type',
      category: 'custom',
      heroStyle: 'minimal',
      layout: 'standard',
      typography: 'serif',
      spacing: 'editorial',
      emphasis: 'balanced'
    },
    {
      id: 'custom-bold',
      name: 'Bold Statement',
      description: 'Eye-catching design for maximum impact',
      category: 'custom',
      heroStyle: 'banner',
      layout: 'showcase',
      typography: 'display',
      spacing: 'airy',
      emphasis: 'visual'
    },
    {
      id: 'custom-narrative',
      name: 'Story Flow',
      description: 'Natural storytelling with immersive layout',
      category: 'custom',
      heroStyle: 'overlay',
      layout: 'timeline',
      typography: 'serif',
      spacing: 'airy',
      emphasis: 'immersive'
    },
    {
      id: 'custom-modern',
      name: 'Modern Clean',
      description: 'Contemporary design with clear hierarchy',
      category: 'custom',
      heroStyle: 'side-by-side',
      layout: 'split',
      typography: 'sans',
      spacing: 'standard',
      emphasis: 'text'
    },
    {
      id: 'custom-creative',
      name: 'Creative Expression',
      description: 'Artistic layout for unique presentations',
      category: 'custom',
      heroStyle: 'magazine',
      layout: 'cards',
      typography: 'display',
      spacing: 'airy',
      emphasis: 'visual'
    }
  ]
}

// Template Selection Logic
export function selectTemplate(
  category: Category,
  interviewAnswers: Array<{ question: string; answer: string }>,
  contentLength: 'short' | 'medium' | 'long' = 'medium'
): TemplateConfig {
  const categoryTemplates = TEMPLATES[category]

  // Analyze interview content for template selection
  const answers = interviewAnswers.map(qa => qa.answer.toLowerCase()).join(' ')

  // Keywords that suggest specific template types
  const visualKeywords = ['image', 'photo', 'visual', 'design', 'art', 'beautiful', 'stunning']
  const personalKeywords = ['personal', 'journey', 'story', 'experience', 'life', 'growing up']
  const technicalKeywords = ['technical', 'development', 'code', 'system', 'architecture', 'build']
  const timelineKeywords = ['timeline', 'history', 'evolution', 'progress', 'journey', 'development']
  const impactKeywords = ['impact', 'change', 'transform', 'revolutionary', 'breakthrough']

  const hasVisual = visualKeywords.some(keyword => answers.includes(keyword))
  const hasPersonal = personalKeywords.some(keyword => answers.includes(keyword))
  const hasTechnical = technicalKeywords.some(keyword => answers.includes(keyword))
  const hasTimeline = timelineKeywords.some(keyword => answers.includes(keyword))
  const hasImpact = impactKeywords.some(keyword => answers.includes(keyword))

  // Select template based on content analysis
  if (hasTimeline) {
    return categoryTemplates.find(t => t.layout === 'timeline') || categoryTemplates[2]
  }
  if (hasVisual) {
    return categoryTemplates.find(t => t.emphasis === 'visual') || categoryTemplates[0]
  }
  if (hasTechnical) {
    return categoryTemplates.find(t => t.typography === 'mono') || categoryTemplates[1]
  }
  if (hasPersonal) {
    return categoryTemplates.find(t => t.emphasis === 'text' && t.spacing === 'editorial') || categoryTemplates[4]
  }
  if (hasImpact) {
    return categoryTemplates.find(t => t.emphasis === 'immersive') || categoryTemplates[0]
  }

  // Default to first template for category
  return categoryTemplates[0]
}

// Get template by ID
export function getTemplate(templateId: string): TemplateConfig | null {
  for (const category of CATEGORIES) {
    const template = TEMPLATES[category].find(t => t.id === templateId)
    if (template) return template
  }
  return null
}

// Get all templates for a category
export function getTemplatesForCategory(category: Category): TemplateConfig[] {
  return TEMPLATES[category] || []
}