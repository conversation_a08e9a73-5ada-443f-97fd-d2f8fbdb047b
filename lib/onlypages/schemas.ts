import { z } from 'zod'

export const InterviewQuestionsSchema = z.object({
  questions: z.array(z.object({
    id: z.string(),
    text: z.string(),
    intent: z.string(),
    expected: z.string(),
    optional: z.boolean()
  }))
})

export const ArticleSchema = z.object({
  title: z.string().max(120),
  dek: z.string().max(220),
  body_html: z.string().optional(), // Allow either body_html or body
  body: z.string().optional(),
  pull_quote: z.string().optional(), // Make optional since some content might not have pull quotes
  cta_label: z.string().optional(), // Make optional
  cta_url: z.string().optional(), // Make optional
  tags: z.array(z.string()).optional().default([]),
  meta: z.object({
    og_title: z.string().optional(),
    og_desc: z.string().optional(),
    meta_desc: z.string().optional(),
  }).optional().default({}),
  missing: z.array(z.string()).optional().default([])
}).transform(data => {
  // Normalize body_html vs body field
  const body_html = data.body_html || data.body || ''
  return {
    ...data,
    body_html,
    meta: data.meta || {},
    tags: data.tags || []
  }
})

export const ThemeTokensSchema = z.object({
  family: z.string(),
  accent: z.string(),
  bg: z.string(),
  ink: z.string(),
  muted: z.string(),
  shapes: z.object({ style: z.string(), weight: z.number() }),
  typography: z.object({
    title: z.record(z.any()),
    dek: z.record(z.any()),
    body: z.record(z.any()),
  })
})

export type InterviewQuestions = z.infer<typeof InterviewQuestionsSchema>
export type Article = z.infer<typeof ArticleSchema>
export type ThemeTokens = z.infer<typeof ThemeTokensSchema>

