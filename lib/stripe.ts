import Stripe from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('Missing STRIPE_SECRET_KEY environment variable');
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-08-27.basil',
});

// Platform fee percentages - Updated to ensure profitability after Stripe fees
export const SUBSCRIPTION_FEE_PERCENTAGE = 0.20 // 20% for subscriptions
export const DONATION_FEE_PERCENTAGE = 0.15 // 15% for donations (increased from 10%)
export const BOOK_SALE_FEE_PERCENTAGE = 0.30 // 30% for book sales
export const PLATFORM_FIXED_FEE_CENTS = 35 // 35 cents fixed fee

// Calculate application fee using new "Percentage + Fixed Fee" model
//
// Profitability Analysis:
// - Stripe charges 2.9% + $0.30 per transaction
// - Our fees ensure we cover Stripe costs plus platform profit:
//   * $10 donation: Our fee $2.00 (15% + $0.50), Stripe fee $0.59, Net profit $1.41
//   * $5 donation: Our fee $1.25 (15% + $0.50), Stripe fee $0.45, Net profit $0.80
//   * $3 donation: Our fee $0.95 (15% + $0.50), Stripe fee $0.39, Net profit $0.56
export function calculateApplicationFee(
  totalAmountInCents: number,
  paymentType: 'subscription' | 'donation' | 'book'
): {
  applicationFeeAmount: number
  writerAmount: number
} {
  // Get the appropriate percentage based on payment type
  let feePercentage: number
  switch (paymentType) {
    case 'donation':
      feePercentage = DONATION_FEE_PERCENTAGE
      break
    case 'subscription':
      feePercentage = SUBSCRIPTION_FEE_PERCENTAGE
      break
    case 'book':
      feePercentage = BOOK_SALE_FEE_PERCENTAGE
      break
    default:
      throw new Error(`Invalid payment type: ${paymentType}`)
  }

  // Calculate platform's gross fee: (Total * Percentage) + Fixed Fee
  const percentageFee = Math.round(totalAmountInCents * feePercentage)
  const applicationFeeAmount = percentageFee + PLATFORM_FIXED_FEE_CENTS

  // Calculate writer's payout: Total - Platform's Gross Fee
  const writerAmount = totalAmountInCents - applicationFeeAmount

  // Ensure writer amount is not negative
  if (writerAmount < 0) {
    throw new Error(`Transaction amount too small. Minimum required: $${((percentageFee + PLATFORM_FIXED_FEE_CENTS) / 100).toFixed(2)}`)
  }

  return {
    applicationFeeAmount,
    writerAmount
  }
}

// Legacy function for backward compatibility - DEPRECATED
// Use calculateApplicationFee instead
export function calculatePlatformFee(totalAmount: number, paymentType: 'subscription' | 'donation' = 'subscription'): {
  platformFee: number
  writerAmount: number
  stripeFee: number
  netAfterStripe: number
} {
  // Map legacy payment types to new system
  const newPaymentType = paymentType === 'donation' ? 'donation' : 'subscription'
  const { applicationFeeAmount, writerAmount } = calculateApplicationFee(totalAmount, newPaymentType)

  // Calculate Stripe fees for backward compatibility
  const stripeFee = Math.round(totalAmount * 0.029 + 30)
  const netAfterStripe = totalAmount - stripeFee

  return {
    platformFee: applicationFeeAmount,
    writerAmount,
    stripeFee,
    netAfterStripe
  }
}

// Calculate exactly what the writer can withdraw (net amount after all fees)
export function calculateAvailableBalance(payments: Array<{ amount_cents: number; kind: string }>, withdrawals: Array<{ amount_cents: number }>): number {
  const totalWithdrawals = withdrawals.reduce((sum, w) => sum + w.amount_cents, 0)

  const totalNetEarnings = payments.reduce((sum, payment) => {
    // Use new fee calculation logic
    let paymentType: 'subscription' | 'donation' | 'book'
    if (payment.kind === 'donation') {
      paymentType = 'donation'
    } else if (payment.kind === 'book') {
      paymentType = 'book'
    } else {
      paymentType = 'subscription'
    }

    const { writerAmount } = calculateApplicationFee(payment.amount_cents, paymentType)
    return sum + writerAmount
  }, 0)

  return Math.max(0, totalNetEarnings - totalWithdrawals)
}

// Minimum amounts (in cents)
export const MIN_SUBSCRIPTION_AMOUNT = 299 // $2.99
export const MAX_SUBSCRIPTION_AMOUNT = 5000 // $50.00
export const MIN_DONATION_AMOUNT = 300 // $3.00 (ensures profitability)
export const MAX_DONATION_AMOUNT = 10000 // $100.00

// Format price for display
export function formatPrice(cents: number): string {
  return `$${(cents / 100).toFixed(2)}`
}
