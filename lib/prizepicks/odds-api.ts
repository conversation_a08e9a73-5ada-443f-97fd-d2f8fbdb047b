// The Odds API Integration for PrizePicks
// Official API to replace scraping approach

import { createServerSupabaseClient } from '@/lib/supabase/server'

interface OddsAPIProps {
  id: string
  sport_key: string
  sport_title: string
  commence_time: string
  home_team: string
  away_team: string
  bookmakers: Array<{
    key: string
    title: string
    last_update: string
    markets: Array<{
      key: string
      outcomes: Array<{
        name: string
        description?: string
        price: number
        point?: number
      }>
    }>
  }>
}

interface PrizePicksProp {
  external_id: string
  sport: string
  player_name: string
  team?: string
  opponent?: string
  prop_type: string
  line_value: number
  game_time: string
  is_active: boolean
  odds?: number
}

// Rate limiting for 500 free API calls
interface APIUsage {
  calls_made: number
  calls_remaining: number
  reset_date: string
}

const ODDS_API_KEY = process.env.THE_ODDS_API_KEY!
const ODDS_API_BASE_URL = 'https://api.the-odds-api.com/v4'

// Track API usage
let apiUsage: APIUsage = {
  calls_made: 0,
  calls_remaining: 500,
  reset_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
}

// Get API usage status
export function getAPIUsage(): APIUsage {
  return apiUsage
}

// Update API usage after each call
function updateAPIUsage() {
  apiUsage.calls_made++
  apiUsage.calls_remaining = Math.max(0, 500 - apiUsage.calls_made)

  // Reset after 7 days
  const now = new Date()
  const resetDate = new Date(apiUsage.reset_date)
  if (now > resetDate) {
    apiUsage = {
      calls_made: 1,
      calls_remaining: 499,
      reset_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
    }
  }
}

// Check if we can make API calls
export function canMakeAPICall(): boolean {
  return apiUsage.calls_remaining > 0
}

// Supported sports mapping to The Odds API keys
const SPORT_MAPPING = {
  'NFL': 'americanfootball_nfl',
  'NCAAF': 'americanfootball_ncaaf',
  'NBA': 'basketball_nba',
  'WNBA': 'basketball_wnba',
  'MLB': 'baseball_mlb',
  'Tennis': 'tennis_atp', // ATP as primary
  'Soccer': 'soccer_epl' // Premier League as primary
}

// Map prop types from Odds API to PrizePicks format
function mapPropType(market: string, description?: string): string {
  const marketMap: Record<string, string> = {
    'player_pass_yds': 'passing_yards',
    'player_rush_yds': 'rushing_yards',
    'player_reception_yds': 'receiving_yards',
    'player_receptions': 'receptions',
    'player_pass_tds': 'passing_touchdowns',
    'player_rush_tds': 'rushing_touchdowns',
    'player_reception_tds': 'receiving_touchdowns',
    'player_points': 'points',
    'player_rebounds': 'rebounds',
    'player_assists': 'assists',
    'player_threes': 'three_pointers_made',
    'player_steals': 'steals',
    'player_blocks': 'blocks',
    'player_turnovers': 'turnovers',
    'player_double_double': 'double_double',
    'player_triple_double': 'triple_double'
  }

  return marketMap[market] || market
}

// Extract player name from outcome description
function extractPlayerName(description: string): string {
  // Remove common prefixes/suffixes
  return description
    .replace(/\s+(Over|Under).*$/, '')
    .replace(/^\s*/, '')
    .trim()
}

// Fetch PrizePicks props from The Odds API
export async function fetchPrizePicksFromOddsAPI(sport?: string): Promise<PrizePicksProp[]> {
  if (!canMakeAPICall()) {
    throw new Error(`API call limit reached. ${apiUsage.calls_remaining} calls remaining. Resets on ${apiUsage.reset_date}`)
  }

  const props: PrizePicksProp[] = []

  try {
    const sportsToFetch = sport ? [sport] : Object.keys(SPORT_MAPPING)

    for (const currentSport of sportsToFetch) {
      const sportKey = SPORT_MAPPING[currentSport as keyof typeof SPORT_MAPPING]
      if (!sportKey) continue

      // First, let's check what sports are available
      console.log(`Checking available bookmakers for ${sportKey}...`)
      const bookmakerUrl = `${ODDS_API_BASE_URL}/sports/${sportKey}/odds`
      const bookmakerParams = new URLSearchParams({
        apiKey: ODDS_API_KEY,
        regions: 'us_dfs', // Use us_dfs region for DFS sites
        oddsFormat: 'american',
        dateFormat: 'iso'
      })

      console.log(`Fetching bookmakers: ${bookmakerUrl}?${bookmakerParams}`)

      const bookmakerResponse = await fetch(`${bookmakerUrl}?${bookmakerParams}`)
      updateAPIUsage()

      if (!bookmakerResponse.ok) {
        console.error(`Failed to fetch ${currentSport} bookmakers: ${bookmakerResponse.statusText}`)

        // Try with regular US region as fallback
        const fallbackParams = new URLSearchParams({
          apiKey: ODDS_API_KEY,
          regions: 'us',
          bookmakers: 'prizepicks',
          oddsFormat: 'american',
          dateFormat: 'iso'
        })

        console.log(`Trying fallback with us region: ${bookmakerUrl}?${fallbackParams}`)
        const fallbackResponse = await fetch(`${bookmakerUrl}?${fallbackParams}`)
        updateAPIUsage()

        if (!fallbackResponse.ok) {
          console.error(`Fallback also failed for ${currentSport}: ${fallbackResponse.statusText}`)
          continue
        }

        const fallbackData = await fallbackResponse.json()
        console.log(`Fallback response for ${currentSport}:`, JSON.stringify(fallbackData, null, 2))
        continue
      }

      const bookmakerData: OddsAPIProps[] = await bookmakerResponse.json()
      console.log(`Received ${bookmakerData.length} games for ${currentSport}`)
      console.log(`Sample response:`, JSON.stringify(bookmakerData.slice(0, 1), null, 2))

      // Check available bookmakers
      const availableBookmakers = new Set<string>()
      for (const game of bookmakerData) {
        if (game.bookmakers) {
          for (const bookmaker of game.bookmakers) {
            availableBookmakers.add(bookmaker.key)
          }
        }
      }
      console.log(`Available bookmakers for ${currentSport}:`, Array.from(availableBookmakers))

      // Now fetch player props specifically
      const propsUrl = `${ODDS_API_BASE_URL}/sports/${sportKey}/odds`
      const propsParams = new URLSearchParams({
        apiKey: ODDS_API_KEY,
        regions: 'us_dfs',
        markets: 'player_pass_yds,player_rush_yds,player_reception_yds,player_receptions,player_pass_tds,player_rush_tds,player_reception_tds,player_points,player_rebounds,player_assists,player_threes,player_steals,player_blocks,player_turnovers,player_double_double,player_triple_double,player_points_alternate',
        bookmakers: 'prizepicks',
        oddsFormat: 'american',
        dateFormat: 'iso'
      })

      console.log(`Fetching ${currentSport} player props: ${propsUrl}?${propsParams}`)

      const propsResponse = await fetch(`${propsUrl}?${propsParams}`)
      updateAPIUsage()

      if (!propsResponse.ok) {
        console.error(`Failed to fetch ${currentSport} props: ${propsResponse.statusText}`)
        continue
      }

      const propsData: OddsAPIProps[] = await propsResponse.json()
      console.log(`Received ${propsData.length} games with props for ${currentSport}`)

      // Process each game's props
      for (const game of propsData) {
        const prizePicksBookmaker = game.bookmakers?.find(b => b.key === 'prizepicks')
        if (!prizePicksBookmaker) {
          console.log(`No PrizePicks bookmaker found for game ${game.id}`)
          continue
        }

        console.log(`Processing ${prizePicksBookmaker.markets.length} markets for game ${game.id}`)

        // Process each market (prop type)
        for (const market of prizePicksBookmaker.markets) {
          for (const outcome of market.outcomes) {
            if (!outcome.description || outcome.point === undefined) continue

            const prop: PrizePicksProp = {
              external_id: `${game.id}-${market.key}-${outcome.name}`,
              sport: currentSport,
              player_name: extractPlayerName(outcome.description),
              team: outcome.name === game.home_team ? game.home_team : game.away_team,
              opponent: outcome.name === game.home_team ? game.away_team : game.home_team,
              prop_type: mapPropType(market.key, outcome.description),
              line_value: outcome.point,
              game_time: game.commence_time,
              is_active: true,
              odds: outcome.price
            }

            props.push(prop)
          }
        }
      }
    }

    console.log(`Successfully fetched ${props.length} props from Odds API`)
    console.log(`API Usage: ${apiUsage.calls_made} calls made, ${apiUsage.calls_remaining} remaining`)

    return props

  } catch (error) {
    console.error('Odds API error:', error)
    throw new Error(`Failed to fetch from Odds API: ${error}`)
  }
}

// Update database with Odds API data
export async function updateDatabaseFromOddsAPI(sport?: string): Promise<{ updated: number, new: number, movements: number }> {
  const supabase = await createServerSupabaseClient()

  try {
    const freshProps = await fetchPrizePicksFromOddsAPI(sport)
    let updated = 0
    let newProps = 0
    let movements = 0

    for (const prop of freshProps) {
      // Check if prop exists
      const { data: existing } = await supabase
        .from('prizepicks_props')
        .select('*')
        .eq('external_id', prop.external_id)
        .single()

      if (existing) {
        // Update existing prop
        const { error } = await supabase
          .from('prizepicks_props')
          .update({
            line_value: prop.line_value,
            is_active: prop.is_active,
            scraped_at: new Date().toISOString()
          })
          .eq('id', existing.id)

        if (!error) {
          updated++
          // Track line movement
          if (existing.line_value !== prop.line_value) {
            movements++
            // Insert line movement record
            await supabase
              .from('line_movements')
              .insert({
                prop_id: existing.id,
                old_value: existing.line_value,
                new_value: prop.line_value
              })
          }
        }
      } else {
        // Insert new prop
        const { error } = await supabase
          .from('prizepicks_props')
          .insert([{
            external_id: prop.external_id,
            sport: prop.sport,
            player_name: prop.player_name,
            team: prop.team,
            opponent: prop.opponent,
            prop_type: prop.prop_type,
            line_value: prop.line_value,
            game_time: prop.game_time,
            is_active: prop.is_active
          }])

        if (!error) {
          newProps++
        }
      }
    }

    // Mark old props as inactive (only for sports we just updated)
    if (freshProps.length > 0) {
      const sportsUpdated = [...new Set(freshProps.map(p => p.sport))]
      for (const sportUpdated of sportsUpdated) {
        await supabase
          .from('prizepicks_props')
          .update({ is_active: false })
          .eq('sport', sportUpdated)
          .not('external_id', 'in', `(${freshProps.filter(p => p.sport === sportUpdated).map(p => `'${p.external_id}'`).join(',')})`)
          .eq('is_active', true)
      }
    }

    return { updated, new: newProps, movements }

  } catch (error) {
    console.error('Database update error:', error)
    throw error
  }
}

// Manual trigger for testing
export async function triggerOddsAPIFetch(sport?: string) {
  console.log(`Starting Odds API fetch for ${sport || 'all sports'}...`)
  console.log(`API Usage before: ${apiUsage.calls_made} calls made, ${apiUsage.calls_remaining} remaining`)

  try {
    const result = await updateDatabaseFromOddsAPI(sport)
    console.log('Odds API fetch completed:', result)
    console.log(`API Usage after: ${apiUsage.calls_made} calls made, ${apiUsage.calls_remaining} remaining`)
    return result
  } catch (error) {
    console.error('Odds API fetch failed:', error)
    throw error
  }
}