// Tests for sports data parsing
import { parseManualDataPaste, parsePropType, extractTeamAbbreviation, validateProp } from '../sports-parser'

describe('Sports Parser', () => {
  describe('parsePropType', () => {
    test('should parse NFL prop types correctly', () => {
      expect(parsePropType('NFL', 'pass_yards')).toBe('passing_yards')
      expect(parsePropType('NFL', 'rush_yards')).toBe('rushing_yards')
      expect(parsePropType('NFL', 'rec_yards')).toBe('receiving_yards')
      expect(parsePropType('NFL', 'rush+rec_tds')).toBe('rush_rec_touchdowns')
    })

    test('should parse WNBA prop types correctly', () => {
      expect(parsePropType('WNBA', 'points')).toBe('points')
      expect(parsePropType('WNBA', 'rebounds')).toBe('rebounds')
      expect(parsePropType('WNBA', 'pts+rebs+asts')).toBe('points_rebounds_assists')
      expect(parsePropType('WNBA', '3pt_made')).toBe('three_pointers_made')
    })

    test('should parse MLB prop types correctly', () => {
      expect(parsePropType('MLB', 'hits')).toBe('hits')
      expect(parsePropType('MLB', 'rbis')).toBe('runs_batted_in')
      expect(parsePropType('MLB', 'home_runs')).toBe('home_runs')
      expect(parsePropType('MLB', 'strikeouts')).toBe('strikeouts')
    })
  })

  describe('extractTeamAbbreviation', () => {
    test('should extract NFL team abbreviations', () => {
      expect(extractTeamAbbreviation('NFL', 'Kansas City Chiefs')).toBe('KC')
      expect(extractTeamAbbreviation('NFL', 'Las Vegas Raiders')).toBe('LV')
      expect(extractTeamAbbreviation('NFL', 'Green Bay Packers')).toBe('GB')
    })

    test('should extract WNBA team abbreviations', () => {
      expect(extractTeamAbbreviation('WNBA', 'Las Vegas Aces')).toBe('LVA')
      expect(extractTeamAbbreviation('WNBA', 'Indiana Fever')).toBe('IND')
      expect(extractTeamAbbreviation('WNBA', 'New York Liberty')).toBe('NY')
    })

    test('should extract MLB team abbreviations', () => {
      expect(extractTeamAbbreviation('MLB', 'New York Yankees')).toBe('NYY')
      expect(extractTeamAbbreviation('MLB', 'Los Angeles Dodgers')).toBe('LAD')
      expect(extractTeamAbbreviation('MLB', 'Boston Red Sox')).toBe('BOS')
    })
  })

  describe('parseManualDataPaste', () => {
    test('should parse WNBA data correctly', () => {
      const wnbaData = `Kelsey Mitchell
IND - G
Kelsey Mitchell
@ LVA Tue 9:30pm
20.5
Points
Less
More
Trending
1.9K
Jackie Young
LVA - G
Jackie Young
vs IND Tue 9:30pm
25.5
Pts+Rebs+Asts
Less
More
Trending
1.4K`

      const props = parseManualDataPaste('WNBA', wnbaData)
      
      expect(props).toHaveLength(2)
      
      expect(props[0]).toMatchObject({
        player_name: 'Kelsey Mitchell',
        team: 'IND',
        opponent: 'LVA',
        prop_type: 'points',
        line_value: 20.5,
        sport: 'WNBA'
      })
      
      expect(props[1]).toMatchObject({
        player_name: 'Jackie Young',
        team: 'LVA',
        opponent: 'IND',
        prop_type: 'points_rebounds_assists',
        line_value: 25.5,
        sport: 'WNBA'
      })
    })

    test('should parse NFL data correctly', () => {
      const nflData = `Lamar Jackson
BAL - QB
Lamar Jackson
@ PIT Mon 8:15pm
275.5
Pass Yards
Less
More
Trending
2.1K`

      const props = parseManualDataPaste('NFL', nflData)
      
      expect(props).toHaveLength(1)
      expect(props[0]).toMatchObject({
        player_name: 'Lamar Jackson',
        team: 'BAL',
        opponent: 'PIT',
        prop_type: 'passing_yards',
        line_value: 275.5,
        sport: 'NFL'
      })
    })

    test('should handle empty or invalid data', () => {
      expect(parseManualDataPaste('NFL', '')).toHaveLength(0)
      expect(parseManualDataPaste('NFL', 'invalid data')).toHaveLength(0)
    })
  })

  describe('validateProp', () => {
    test('should validate complete props', () => {
      const validProp = {
        external_id: 'test_1',
        sport: 'NFL',
        player_name: 'Test Player',
        prop_type: 'passing_yards',
        line_value: 250.5,
        game_time: new Date().toISOString(),
        is_active: true
      }
      
      expect(validateProp(validProp)).toBe(true)
    })

    test('should reject incomplete props', () => {
      const invalidProp = {
        external_id: 'test_1',
        sport: 'NFL',
        player_name: '',
        prop_type: 'passing_yards',
        line_value: 0,
        game_time: new Date().toISOString(),
        is_active: true
      }
      
      expect(validateProp(invalidProp)).toBe(false)
    })
  })
})
