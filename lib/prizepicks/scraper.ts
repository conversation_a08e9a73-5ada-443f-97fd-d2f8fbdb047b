// PrizePicks Scraper with IP Rotation
// Brilliant, simple, and resilient

import { createServerSupabaseClient } from '@/lib/supabase/server'
import { parsePropType, extractTeamAbbreviation } from './sports-parser'

interface PrizePicksProp {
  external_id: string
  sport: string
  player_name: string
  team?: string
  opponent?: string
  prop_type: string
  line_value: number
  game_time: string
  is_active: boolean
}

// Free IP rotation endpoints (deploy to multiple platforms)
const SCRAPING_ENDPOINTS = [
  process.env.SCRAPER_ENDPOINT_1, // Vercel US-East
  process.env.SCRAPER_ENDPOINT_2, // Vercel EU-West
  process.env.SCRAPER_ENDPOINT_3, // Railway
  process.env.SCRAPER_ENDPOINT_4, // Render
].filter(Boolean)

let currentEndpointIndex = 0

function getNextEndpoint(): string | null {
  if (SCRAPING_ENDPOINTS.length === 0) return null
  const endpoint = SCRAPING_ENDPOINTS[currentEndpointIndex]
  currentEndpointIndex = (currentEndpointIndex + 1) % SCRAPING_ENDPOINTS.length
  return endpoint
}

// Parse PrizePicks prop type from their naming (now uses sports-parser)
function parsePropTypeLocal(pickType: string, statType: string, sport: string = 'NFL'): string {
  const type = `${pickType}_${statType}`.toLowerCase()
  return parsePropType(sport, type)
}

// Extract team abbreviation from team name (now uses sports-parser)
function extractTeamAbbreviationLocal(teamName: string, sport: string = 'NFL'): string {
  return extractTeamAbbreviation(sport, teamName)
}

// Core scraping function that can be deployed to multiple endpoints
export async function scrapePrizePicksProps(sport?: string): Promise<PrizePicksProp[]> {
  const props: PrizePicksProp[] = []

  try {
    // PrizePicks API endpoints (updated 2025)
    const baseUrl = 'https://api.prizepicks.com'

    // Try all active props without league filter first
    if (!sport) {
      console.log('Scraping all active props without league filter')
      const url = `${baseUrl}/projections?per_page=1000&single_stat=true&game_mode=pickem`
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Referer': 'https://app.prizepicks.com/',
          'Origin': 'https://app.prizepicks.com',
          'X-Device-ID': 'web-browser-' + Math.random().toString(36).substring(2, 15),
          'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"Windows"',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-site'
        }
      })

      if (response.ok) {
        const data = await response.json()
        console.log(`All active props response:`, JSON.stringify(data, null, 2))

        // Parse this data too if it has content
        if (data.data && data.data.length > 0) {
          for (const projection of data.data) {
            const included = data.included || []
            const player = included.find((item: any) => item.type === 'player' && item.id === projection.relationships?.player?.data?.id)
            const game = included.find((item: any) => item.type === 'game' && item.id === projection.relationships?.game?.data?.id)

            const prop: PrizePicksProp = {
              external_id: projection.id.toString(),
              sport: 'ALL', // We'll determine from league later
              player_name: player?.attributes?.display_name || projection.attributes?.player_name || 'Unknown Player',
              team: player?.attributes?.team_name ? extractTeamAbbreviationLocal(player.attributes.team_name, 'NFL') : undefined,
              opponent: undefined,
              prop_type: parsePropTypeLocal(projection.attributes?.pick_type || '', projection.attributes?.stat_type || '', 'NFL'),
              line_value: parseFloat(projection.attributes?.line_score || projection.attributes?.value || '0'),
              game_time: game?.attributes?.start_time || projection.attributes?.game_time,
              is_active: projection.attributes?.status === 'active'
            }

            props.push(prop)
          }
        }
      } else {
        console.error(`Failed to fetch all props: ${response.statusText}`)
      }
    }

    const endpoints = {
      'NFL': `${baseUrl}/projections?league_id=1&per_page=250&single_stat=true&game_mode=pickem`,
      'NCAAF': `${baseUrl}/projections?league_id=2&per_page=250&single_stat=true&game_mode=pickem`,
      'NBA': `${baseUrl}/projections?league_id=7&per_page=250&single_stat=true&game_mode=pickem`,
      'Tennis': `${baseUrl}/projections?league_id=13&per_page=250&single_stat=true&game_mode=pickem`,
      'WNBA': `${baseUrl}/projections?league_id=17&per_page=250&single_stat=true&game_mode=pickem`
    }

    const sportsToScrape = sport ? [sport] : []

    for (const currentSport of sportsToScrape) {
      const url = endpoints[currentSport as keyof typeof endpoints]
      if (!url) continue

      console.log(`Scraping ${currentSport} props from ${url}`)

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Referer': 'https://app.prizepicks.com/',
          'Origin': 'https://app.prizepicks.com',
          'X-Device-ID': 'web-browser-' + Math.random().toString(36).substring(2, 15),
          'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"Windows"',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-site'
        }
      })

      if (!response.ok) {
        console.error(`Failed to fetch ${currentSport} props: ${response.statusText}`)
        continue
      }

      const data = await response.json()
      console.log(`API Response for ${currentSport}:`, JSON.stringify(data, null, 2))

      // Parse the response structure (PrizePicks 2025 format)
      if (data.data) {
        for (const projection of data.data) {
          // Get related game and player data from included
          const included = data.included || []
          const player = included.find((item: any) => item.type === 'player' && item.id === projection.relationships?.player?.data?.id)
          const game = included.find((item: any) => item.type === 'game' && item.id === projection.relationships?.game?.data?.id)

          const prop: PrizePicksProp = {
            external_id: projection.id.toString(),
            sport: currentSport,
            player_name: player?.attributes?.display_name || projection.attributes?.player_name || 'Unknown Player',
            team: player?.attributes?.team_name ? extractTeamAbbreviationLocal(player.attributes.team_name, currentSport) : undefined,
            opponent: undefined, // Will need to parse from game data if available
            prop_type: parsePropTypeLocal(projection.attributes?.pick_type || '', projection.attributes?.stat_type || '', currentSport),
            line_value: parseFloat(projection.attributes?.line_score || projection.attributes?.value || '0'),
            game_time: game?.attributes?.start_time || projection.attributes?.game_time,
            is_active: projection.attributes?.status === 'active'
          }

          props.push(prop)
        }
      }
    }

    console.log(`Successfully scraped ${props.length} props`)
    return props

  } catch (error) {
    console.error('Scraping error:', error)
    throw new Error(`Failed to scrape PrizePicks: ${error}`)
  }
}

// Scraper with IP rotation
export async function scrapePrizePicksWithRotation(sport?: string): Promise<PrizePicksProp[]> {
  const maxRetries = SCRAPING_ENDPOINTS.length || 3

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      const endpoint = getNextEndpoint()

      if (endpoint) {
        // Use rotated endpoint
        const response = await fetch(`${endpoint}/api/scrape-prizepicks`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ sport })
        })

        if (response.ok) {
          return await response.json()
        }
      } else {
        // Fallback to direct scraping
        return await scrapePrizePicksProps(sport)
      }

    } catch (error) {
      console.error(`Scraping attempt ${attempt + 1} failed:`, error)

      if (attempt === maxRetries - 1) {
        throw error
      }

      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000))
    }
  }

  throw new Error('All scraping attempts failed')
}

// Update database with fresh props
export async function updatePrizePicksDatabase(sport?: string): Promise<{ updated: number, new: number, movements: number }> {
  const supabase = await createServerSupabaseClient()

  try {
    const freshProps = await scrapePrizePicksWithRotation(sport)
    let updated = 0
    let newProps = 0
    let movements = 0

    for (const prop of freshProps) {
      // Check if prop exists
      const { data: existing } = await supabase
        .from('prizepicks_props')
        .select('*')
        .eq('external_id', prop.external_id)
        .single()

      if (existing) {
        // Update existing prop
        const { error } = await supabase
          .from('prizepicks_props')
          .update({
            line_value: prop.line_value,
            is_active: prop.is_active,
            scraped_at: new Date().toISOString()
          })
          .eq('id', existing.id)

        if (!error) {
          updated++
          // Line movement will be automatically tracked by trigger
          if (existing.line_value !== prop.line_value) {
            movements++
          }
        }
      } else {
        // Insert new prop
        const { error } = await supabase
          .from('prizepicks_props')
          .insert([prop])

        if (!error) {
          newProps++
        }
      }
    }

    // Mark old props as inactive
    await supabase
      .from('prizepicks_props')
      .update({ is_active: false })
      .not('external_id', 'in', `(${freshProps.map(p => p.external_id).join(',')})`)
      .eq('is_active', true)

    return { updated, new: newProps, movements }

  } catch (error) {
    console.error('Database update error:', error)
    throw error
  }
}

// Manual trigger for scraping (for testing)
export async function triggerManualScrape(sport?: string) {
  console.log(`Starting manual scrape for ${sport || 'all sports'}...`)

  try {
    const result = await updatePrizePicksDatabase(sport)
    console.log('Scrape completed:', result)
    return result
  } catch (error) {
    console.error('Manual scrape failed:', error)
    throw error
  }
}