# PrizePicks Sports Data Parser

A comprehensive system for parsing and importing sports props data from multiple sources including NFL, WNBA, MLB, and NBA.

## Features

- **Multi-Sport Support**: NFL, WNBA, MLB, NBA
- **Manual Data Import**: Paste data from PrizePicks, DraftKings, FanDuel
- **Auto-Detection**: Automatically detect sport from pasted data
- **Prop Type Normalization**: Standardize prop types across different sources
- **Team Abbreviation Mapping**: Convert full team names to standard abbreviations
- **Data Validation**: Ensure imported data meets quality standards
- **Batch Import**: Import multiple sports datasets at once

## Supported Sports & Props

### NFL (Tier 1 - Excellent Coverage)
- **Passing Yards** - Universal coverage
- **Rushing Yards** - Universal coverage  
- **Receiving Yards** - Universal coverage
- **Rush+Rec TDs** - Very common
- **Pass TDs** - Universal coverage
- **Receptions** - Very common
- **Rush Attempts** - Good coverage
- **Pass Attempts** - Good coverage

### NFL (Tier 2 - Good Coverage)
- **Tackles/Assists** - Growing coverage
- **Sacks** - Good coverage for defensive players
- **Interceptions** - Decent coverage for QBs/DBs
- **Field Goals Made** - Good coverage for kickers
- **Longest Reception** - Available on many books
- **Completion Percentage** - Some books offer this

### WNBA/NBA (Excellent Coverage)
- **Points** - Universal coverage
- **Rebounds** - Universal coverage
- **Assists** - Universal coverage
- **Pts+Rebs+Asts** (Triple combo) - Very common
- **3-Pointers Made** - Good coverage
- **Pts+Asts** - Common combo bet
- **Pts+Rebs** - Common combo bet
- **Rebs+Asts** - Common combo bet

### MLB (Excellent Coverage)
- **Hits** - Universal coverage
- **Total Bases** - Very common
- **RBIs** - Universal coverage
- **Runs Scored** - Good coverage
- **Home Runs** - Universal coverage
- **Strikeouts** (pitchers) - Universal coverage
- **Walks** - Good coverage
- **Stolen Bases** - Good coverage

## Usage

### Manual Import API

```typescript
// Import single sport data
const response = await fetch('/api/prizepicks/import', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sport: 'WNBA',
    data: pastedData
  })
})

// Auto-detect sport
const response = await fetch('/api/prizepicks/import', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    auto_detect: true,
    data: pastedData
  })
})

// Batch import
const response = await fetch('/api/prizepicks/import', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    imports: [
      { sport: 'NFL', data: nflData },
      { sport: 'WNBA', data: wnbaData }
    ]
  })
})
```

### Direct Parser Usage

```typescript
import { parseManualDataPaste, parsePropType, extractTeamAbbreviation } from '@/lib/prizepicks/sports-parser'

// Parse pasted data
const props = parseManualDataPaste('WNBA', pastedData)

// Normalize prop types
const propType = parsePropType('NFL', 'pass_yards') // Returns 'passing_yards'

// Get team abbreviation
const teamAbbr = extractTeamAbbreviation('NFL', 'Kansas City Chiefs') // Returns 'KC'
```

### React Component

```tsx
import ManualImport from '@/components/prizepicks/ManualImport'

function MyPage() {
  return (
    <div>
      <ManualImport />
    </div>
  )
}
```

## Data Format Examples

### WNBA Format
```
Kelsey Mitchell
IND - G
Kelsey Mitchell
@ LVA Tue 9:30pm
20.5
Points
Less
More
Trending
1.9K
```

### NFL Format
```
Lamar Jackson
BAL - QB
Lamar Jackson
@ PIT Mon 8:15pm
275.5
Pass Yards
Less
More
Trending
2.1K
```

### MLB Format
```
Aaron Judge
NYY - OF
Aaron Judge
@ HOU Thu 8:00pm
1.5
Home Runs
Less
More
Trending
2.8K
```

## Database Schema

The system uses the existing `prizepicks_props` table:

```sql
CREATE TABLE prizepicks_props (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  external_id text UNIQUE NOT NULL,
  sport text NOT NULL,
  player_name text NOT NULL,
  team text,
  opponent text,
  prop_type text NOT NULL,
  line_value decimal NOT NULL,
  game_time timestamptz,
  is_active boolean DEFAULT true,
  scraped_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);
```

## Files Structure

```
lib/prizepicks/
├── sports-parser.ts      # Core parsing logic
├── data-importer.ts      # Import utilities
├── scraper.ts           # Updated scraper with new parser
├── odds-api.ts          # Odds API integration
└── __tests__/
    └── sports-parser.test.ts

components/prizepicks/
└── ManualImport.tsx     # React import component

app/api/prizepicks/
└── import/
    └── route.ts         # Import API endpoints
```

## Testing

Run the test suite:

```bash
npm test lib/prizepicks/__tests__/sports-parser.test.ts
```

## Error Handling

The system includes comprehensive error handling:

- **Validation Errors**: Invalid or incomplete props are skipped with detailed error messages
- **Duplicate Detection**: Existing props are detected and skipped to prevent duplicates
- **Format Detection**: Auto-detection falls back gracefully if sport cannot be determined
- **Batch Processing**: Individual failures don't stop the entire batch import

## Future Enhancements

- **Additional Sports**: Tennis, Soccer, Hockey support
- **More Data Sources**: ESPN, Yahoo Sports integration
- **Real-time Updates**: WebSocket integration for live data
- **Advanced Analytics**: Trend analysis and prediction models
- **Mobile App**: React Native component for mobile imports
