// PrizePicks NFL Scraper using Puppeteer (Node.js equivalent of Selenium)
const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function scrapePrizePicksNFL() {
  console.log('Starting PrizePicks NFL scrape...');

  const browser = await puppeteer.launch({
    headless: true,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--single-process',
      '--disable-gpu'
    ]
  });

  try {
    const page = await browser.newPage();

    // Set user agent to mimic real browser
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36');

    // Set viewport
    await page.setViewport({ width: 1920, height: 1080 });

    console.log('Navigating to PrizePicks...');
    await page.goto('https://app.prizepicks.com/board', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });

    // Wait for page to load
    await page.waitForFunction(() => document.readyState === 'complete');
    await new Promise(resolve => setTimeout(resolve, 5000));

    console.log('Looking for NFL section...');

    // Try to find and click NFL section
    const nflSelectors = [
      'button[data-testid="NFL"]',
      'button:contains("NFL")',
      '[data-sport="NFL"]',
      '.sport-button:contains("NFL")',
      'button[aria-label*="NFL"]'
    ];

    let nflFound = false;
    for (const selector of nflSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 3000 });
        await page.click(selector);
        nflFound = true;
        console.log(`Found NFL with selector: ${selector}`);
        break;
      } catch (e) {
        console.log(`Selector ${selector} not found, trying next...`);
      }
    }

    if (!nflFound) {
      console.log('NFL section not found, taking screenshot for debugging...');
      await page.screenshot({ path: 'prizepicks-debug.png', fullPage: true });
      throw new Error('NFL section not found');
    }

    await new Promise(resolve => setTimeout(resolve, 3000));

    const allProps = [];

    // Categories to scrape
    const categories = [
      'Popular', 'Pass Yards', 'Rush Yards', 'Receiving Yards',
      'Rush+Rec TDs', 'Assists', 'Pass TDs', 'FG Made',
      'Receptions', 'Rush+Rec Yds', 'Fantasy Score'
    ];

    for (const category of categories) {
      try {
        console.log(`Scraping category: ${category}`);

        // Try multiple selectors for category buttons
        const categorySelectors = [
          `button:contains("${category}")`,
          `[data-testid="${category}"]`,
          `button[aria-label*="${category}"]`,
          `.category-button:contains("${category}")`
        ];

        let categoryFound = false;
        for (const selector of categorySelectors) {
          try {
            await page.waitForSelector(selector, { timeout: 2000 });
            await page.click(selector);
            categoryFound = true;
            break;
          } catch (e) {
            // Try next selector
          }
        }

        if (!categoryFound) {
          console.log(`Category ${category} not found, skipping...`);
          continue;
        }

        await new Promise(resolve => setTimeout(resolve, 2000));

        // Extract props - we'll need to inspect the actual HTML structure
        const props = await page.evaluate((cat) => {
          const propElements = document.querySelectorAll('[data-testid*="prop"], .prop-card, .player-prop, .pick-card');
          const results = [];

          propElements.forEach(prop => {
            try {
              // Try multiple ways to extract data
              const playerName =
                prop.querySelector('.player-name')?.textContent ||
                prop.querySelector('[data-testid*="player"]')?.textContent ||
                prop.querySelector('.name')?.textContent ||
                '';

              const lineValue =
                prop.querySelector('.line-value')?.textContent ||
                prop.querySelector('[data-testid*="line"]')?.textContent ||
                prop.querySelector('.value')?.textContent ||
                '';

              const team =
                prop.querySelector('.team')?.textContent ||
                prop.querySelector('[data-testid*="team"]')?.textContent ||
                '';

              if (playerName && lineValue) {
                results.push({
                  player_name: playerName.trim(),
                  stat_type: cat,
                  line_value: lineValue.trim(),
                  team: team.trim(),
                  timestamp: new Date().toISOString(),
                  date_scraped: new Date().toISOString().split('T')[0]
                });
              }
            } catch (e) {
              console.log('Error extracting prop:', e);
            }
          });

          return results;
        }, category);

        allProps.push(...props);
        console.log(`Found ${props.length} props in ${category}`);

      } catch (error) {
        console.log(`Error scraping ${category}:`, error.message);
        continue;
      }
    }

    // Save data
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `prizepicks_nfl_${timestamp}.json`;
    const filepath = path.join(__dirname, '../../data', filename);

    // Ensure data directory exists
    const dataDir = path.dirname(filepath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    fs.writeFileSync(filepath, JSON.stringify(allProps, null, 2));

    console.log(`Scraped ${allProps.length} props saved to ${filename}`);

    return {
      success: true,
      props: allProps,
      count: allProps.length,
      filename: filename
    };

  } catch (error) {
    console.error('Scraping error:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

module.exports = { scrapePrizePicksNFL };

// For testing
if (require.main === module) {
  scrapePrizePicksNFL()
    .then(result => {
      console.log('Scraping completed:', result);
    })
    .catch(error => {
      console.error('Scraping failed:', error);
    });
}