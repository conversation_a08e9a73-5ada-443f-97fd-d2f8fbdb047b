interface InjuryReport {
  player_name: string
  team: string
  injury_status: 'OUT' | 'DOUBTFUL' | 'QUESTIONABLE' | 'PROBABLE' | 'HEALTHY'
  injury_description: string
  last_updated: string
  fantasy_impact: 'HIGH' | 'MEDIUM' | 'LOW' | 'NONE'
  return_timeline?: string
}

interface GameInjuryContext {
  home_team: string
  away_team: string
  home_injuries: InjuryReport[]
  away_injuries: InjuryReport[]
  key_absences: string[]
  lineup_uncertainty: boolean
}

// ESPN Injury API integration (free tier)
export class InjuryAPIClient {
  private baseUrl = 'https://site.api.espn.com/apis/site/v2/sports'

  async getTeamInjuries(sport: string, teamId?: string): Promise<InjuryReport[]> {
    try {
      // ESPN provides injury data in their team roster API
      const sportMapping: Record<string, string> = {
        'NFL': 'football/nfl',
        'NCAAF': 'football/college-football',
        'NBA': 'basketball/nba',
        'WNBA': 'basketball/wnba'
      }

      const espnSport = sportMapping[sport]
      if (!espnSport) return []

      // For now, return mock injury data as ESPN API structure can be complex
      // In production, this would parse actual ESPN injury reports
      return this.getMockInjuryData(sport)

    } catch (error) {
      console.error('Injury API error:', error)
      return []
    }
  }

  async getPlayerInjuryStatus(playerName: string, sport: string): Promise<InjuryReport | null> {
    try {
      const teamInjuries = await this.getTeamInjuries(sport)
      return teamInjuries.find(injury =>
        injury.player_name.toLowerCase().includes(playerName.toLowerCase()) ||
        playerName.toLowerCase().includes(injury.player_name.toLowerCase())
      ) || null

    } catch (error) {
      console.error('Player injury lookup error:', error)
      return null
    }
  }

  async getGameInjuryContext(homeTeam: string, awayTeam: string, sport: string): Promise<GameInjuryContext> {
    try {
      const homeInjuries = await this.getTeamInjuries(sport)
      const awayInjuries = await this.getTeamInjuries(sport)

      // Identify key absences (starters or high-impact players)
      const keyAbsences: string[] = []

      const highImpactInjuries = [...homeInjuries, ...awayInjuries].filter(injury =>
        injury.injury_status === 'OUT' && injury.fantasy_impact === 'HIGH'
      )

      keyAbsences.push(...highImpactInjuries.map(injury => injury.player_name))

      // Determine if there's significant lineup uncertainty
      const lineupUncertainty = [...homeInjuries, ...awayInjuries].some(injury =>
        ['DOUBTFUL', 'QUESTIONABLE'].includes(injury.injury_status) &&
        ['HIGH', 'MEDIUM'].includes(injury.fantasy_impact)
      )

      return {
        home_team: homeTeam,
        away_team: awayTeam,
        home_injuries: homeInjuries,
        away_injuries: awayInjuries,
        key_absences: keyAbsences,
        lineup_uncertainty: lineupUncertainty
      }

    } catch (error) {
      console.error('Game injury context error:', error)
      return {
        home_team: homeTeam,
        away_team: awayTeam,
        home_injuries: [],
        away_injuries: [],
        key_absences: [],
        lineup_uncertainty: false
      }
    }
  }

  // Mock injury data for development/testing
  private getMockInjuryData(sport: string): InjuryReport[] {
    const mockInjuries: InjuryReport[] = []

    if (sport === 'NFL') {
      mockInjuries.push(
        {
          player_name: 'Sample Player',
          team: 'TBD',
          injury_status: 'QUESTIONABLE',
          injury_description: 'Ankle sprain',
          last_updated: new Date().toISOString(),
          fantasy_impact: 'MEDIUM',
          return_timeline: 'Game-time decision'
        }
      )
    }

    return mockInjuries
  }

  // Get injury impact for prop betting
  getInjuryImpactForProp(injury: InjuryReport, propType: string): {
    impact_level: 'HIGH' | 'MEDIUM' | 'LOW' | 'NONE'
    reasoning: string
    recommendation: 'AVOID' | 'MONITOR' | 'OPPORTUNITY'
  } {
    if (injury.injury_status === 'HEALTHY') {
      return {
        impact_level: 'NONE',
        reasoning: 'Player is healthy with no injury concerns',
        recommendation: 'MONITOR'
      }
    }

    // Injury impacts vary by prop type
    const propImpacts: Record<string, string[]> = {
      'rushing_yards': ['leg', 'ankle', 'knee', 'hamstring', 'quad'],
      'passing_yards': ['shoulder', 'arm', 'hand', 'finger', 'elbow'],
      'receiving_yards': ['hand', 'finger', 'hamstring', 'ankle', 'knee'],
      'receptions': ['hand', 'finger', 'concussion']
    }

    const relevantInjuries = propImpacts[propType] || []
    const injuryAffectsProp = relevantInjuries.some(bodyPart =>
      injury.injury_description.toLowerCase().includes(bodyPart)
    )

    if (injury.injury_status === 'OUT') {
      return {
        impact_level: 'HIGH',
        reasoning: `Player is ruled out, directly eliminates prop bet opportunity`,
        recommendation: 'AVOID'
      }
    }

    if (injury.injury_status === 'DOUBTFUL' && injuryAffectsProp) {
      return {
        impact_level: 'HIGH',
        reasoning: `Doubtful status with injury affecting ${propType} performance`,
        recommendation: 'AVOID'
      }
    }

    if (injury.injury_status === 'QUESTIONABLE' && injuryAffectsProp) {
      return {
        impact_level: 'MEDIUM',
        reasoning: `Questionable status may limit effectiveness in ${propType}`,
        recommendation: 'MONITOR'
      }
    }

    if (injury.injury_status === 'QUESTIONABLE' && !injuryAffectsProp) {
      return {
        impact_level: 'LOW',
        reasoning: `Injury unlikely to significantly impact ${propType} performance`,
        recommendation: 'MONITOR'
      }
    }

    return {
      impact_level: 'LOW',
      reasoning: 'Minor injury concern with limited prop impact',
      recommendation: 'MONITOR'
    }
  }
}

export const injuryAPI = new InjuryAPIClient()

// Integration with prop analysis
export async function getInjuryContextForProp(prop: any): Promise<{
  player_injury?: InjuryReport
  game_injury_context: GameInjuryContext
  injury_impact: ReturnType<InjuryAPIClient['getInjuryImpactForProp']>
}> {
  try {
    // Get specific player injury status
    const playerInjury = await injuryAPI.getPlayerInjuryStatus(prop.player_name, prop.sport)

    // Get overall game injury context
    const gameContext = await injuryAPI.getGameInjuryContext(
      prop.team || 'TBD',
      prop.opponent || 'TBD',
      prop.sport
    )

    // Assess injury impact on this specific prop
    const injuryImpact = playerInjury
      ? injuryAPI.getInjuryImpactForProp(playerInjury, prop.prop_type)
      : {
          impact_level: 'NONE' as const,
          reasoning: 'No injury concerns identified',
          recommendation: 'MONITOR' as const
        }

    return {
      player_injury: playerInjury || undefined,
      game_injury_context: gameContext,
      injury_impact: injuryImpact
    }

  } catch (error) {
    console.error('Injury context error:', error)
    return {
      game_injury_context: {
        home_team: prop.team || 'TBD',
        away_team: prop.opponent || 'TBD',
        home_injuries: [],
        away_injuries: [],
        key_absences: [],
        lineup_uncertainty: false
      },
      injury_impact: {
        impact_level: 'NONE',
        reasoning: 'Unable to fetch injury data',
        recommendation: 'MONITOR'
      }
    }
  }
}