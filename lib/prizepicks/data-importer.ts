// Data Importer for Manual Sports Props Pasting
// Handles NFL, WNBA, MLB, NBA data from various sources

import { createServerSupabaseClient } from '@/lib/supabase/server'
import { parseManualDataPaste, validateProp, type SportsProp } from './sports-parser'

export interface ImportResult {
  success: boolean
  imported: number
  skipped: number
  errors: string[]
  props: SportsProp[]
}

// Import props from manual paste
export async function importManualProps(sport: string, pastedData: string): Promise<ImportResult> {
  const result: ImportResult = {
    success: false,
    imported: 0,
    skipped: 0,
    errors: [],
    props: []
  }

  try {
    // Parse the pasted data
    const parsedProps = parseManualDataPaste(sport, pastedData)
    
    if (parsedProps.length === 0) {
      result.errors.push('No valid props found in pasted data')
      return result
    }

    // Validate each prop
    const validProps = parsedProps.filter(prop => {
      const isValid = validateProp(prop)
      if (!isValid) {
        result.skipped++
        result.errors.push(`Invalid prop for ${prop.player_name}: missing required fields`)
      }
      return isValid
    })

    if (validProps.length === 0) {
      result.errors.push('No valid props after validation')
      return result
    }

    // Save to database
    const supabase = await createServerSupabaseClient()
    
    for (const prop of validProps) {
      try {
        // Check if prop already exists
        const { data: existing } = await supabase
          .from('prizepicks_props')
          .select('id')
          .eq('player_name', prop.player_name)
          .eq('prop_type', prop.prop_type)
          .eq('line_value', prop.line_value)
          .eq('sport', prop.sport)
          .single()

        if (existing) {
          result.skipped++
          continue
        }

        // Insert new prop
        const { error } = await supabase
          .from('prizepicks_props')
          .insert([{
            external_id: prop.external_id,
            sport: prop.sport,
            player_name: prop.player_name,
            team: prop.team,
            opponent: prop.opponent,
            prop_type: prop.prop_type,
            line_value: prop.line_value,
            game_time: prop.game_time,
            is_active: prop.is_active,
            scraped_at: new Date().toISOString()
          }])

        if (error) {
          result.errors.push(`Failed to insert ${prop.player_name} ${prop.prop_type}: ${error.message}`)
          result.skipped++
        } else {
          result.imported++
          result.props.push(prop)
        }

      } catch (error) {
        result.errors.push(`Error processing ${prop.player_name}: ${error}`)
        result.skipped++
      }
    }

    result.success = result.imported > 0
    return result

  } catch (error) {
    result.errors.push(`Import failed: ${error}`)
    return result
  }
}

// Batch import from multiple sport data
export async function batchImportProps(imports: Array<{ sport: string, data: string }>): Promise<ImportResult[]> {
  const results: ImportResult[] = []
  
  for (const importData of imports) {
    const result = await importManualProps(importData.sport, importData.data)
    results.push(result)
  }
  
  return results
}

// Smart data detection - try to auto-detect sport from pasted data
export function detectSportFromData(pastedData: string): string | null {
  const data = pastedData.toLowerCase()
  
  // NFL indicators
  if (data.includes('pass') && (data.includes('yards') || data.includes('tds')) ||
      data.includes('rush') || data.includes('receiving')) {
    return 'NFL'
  }
  
  // WNBA/NBA indicators
  if (data.includes('points') && data.includes('rebounds') && data.includes('assists')) {
    // Check for WNBA teams
    if (data.includes('lva') || data.includes('ind') || data.includes('aces') || data.includes('fever')) {
      return 'WNBA'
    }
    return 'NBA'
  }
  
  // MLB indicators
  if (data.includes('hits') || data.includes('rbis') || data.includes('strikeouts') ||
      data.includes('home runs') || data.includes('stolen bases')) {
    return 'MLB'
  }
  
  return null
}

// Format import summary for display
export function formatImportSummary(results: ImportResult[]): string {
  const totalImported = results.reduce((sum, r) => sum + r.imported, 0)
  const totalSkipped = results.reduce((sum, r) => sum + r.skipped, 0)
  const totalErrors = results.reduce((sum, r) => sum + r.errors.length, 0)
  
  let summary = `Import Complete:\n`
  summary += `✅ ${totalImported} props imported\n`
  
  if (totalSkipped > 0) {
    summary += `⏭️ ${totalSkipped} props skipped (duplicates or invalid)\n`
  }
  
  if (totalErrors > 0) {
    summary += `❌ ${totalErrors} errors encountered\n`
  }
  
  // Sport breakdown
  const sportBreakdown = results.reduce((acc, result, index) => {
    const sport = results[index]?.props[0]?.sport || 'Unknown'
    if (!acc[sport]) acc[sport] = 0
    acc[sport] += result.imported
    return acc
  }, {} as Record<string, number>)
  
  if (Object.keys(sportBreakdown).length > 1) {
    summary += `\nBy Sport:\n`
    Object.entries(sportBreakdown).forEach(([sport, count]) => {
      summary += `${sport}: ${count} props\n`
    })
  }
  
  return summary
}

// Enhanced parsing for specific data formats
export function parseSpecificFormat(sport: string, format: 'prizepicks' | 'draftkings' | 'fanduel', data: string): SportsProp[] {
  switch (format) {
    case 'prizepicks':
      return parseManualDataPaste(sport, data)
    
    case 'draftkings':
      return parseDraftKingsFormat(sport, data)
    
    case 'fanduel':
      return parseFanDuelFormat(sport, data)
    
    default:
      return parseManualDataPaste(sport, data)
  }
}

// DraftKings format parser
function parseDraftKingsFormat(sport: string, data: string): SportsProp[] {
  // DraftKings typically has format: Player Name | Team | Prop | Line
  const props: SportsProp[] = []
  const lines = data.trim().split('\n')
  
  for (const line of lines) {
    if (line.includes('|')) {
      const parts = line.split('|').map(p => p.trim())
      if (parts.length >= 4) {
        const prop: SportsProp = {
          external_id: `dk_${Date.now()}_${props.length}`,
          sport: sport,
          player_name: parts[0],
          team: parts[1],
          prop_type: parts[2].toLowerCase().replace(/\s+/g, '_'),
          line_value: parseFloat(parts[3]),
          game_time: new Date().toISOString(),
          is_active: true
        }
        
        if (validateProp(prop)) {
          props.push(prop)
        }
      }
    }
  }
  
  return props
}

// FanDuel format parser
function parseFanDuelFormat(sport: string, data: string): SportsProp[] {
  // FanDuel typically has different format - implement based on their structure
  const props: SportsProp[] = []
  const lines = data.trim().split('\n')
  
  // This would need to be customized based on FanDuel's actual format
  // For now, fall back to manual parsing
  return parseManualDataPaste(sport, data)
}

// Export utility functions
export {
  parseManualDataPaste,
  validateProp,
  type SportsProp
}
