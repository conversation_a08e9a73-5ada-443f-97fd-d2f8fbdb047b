// Comprehensive Sports Data Parser for PrizePicks
// Handles NFL, WNBA, MLB, NBA prop parsing and normalization

export interface SportsProp {
  external_id: string
  sport: string
  player_name: string
  team?: string
  opponent?: string
  position?: string
  prop_type: string
  line_value: number
  game_time: string
  is_active: boolean
  trending_volume?: number
}

// Sport-specific prop type mappings
export const SPORT_PROP_MAPPINGS = {
  NFL: {
    // Tier 1 - Excellent Coverage
    'pass_yards': 'passing_yards',
    'passing_yards': 'passing_yards',
    'rush_yards': 'rushing_yards', 
    'rushing_yards': 'rushing_yards',
    'rec_yards': 'receiving_yards',
    'receiving_yards': 'receiving_yards',
    'rush_rec_tds': 'rush_rec_touchdowns',
    'rush+rec_tds': 'rush_rec_touchdowns',
    'pass_tds': 'passing_touchdowns',
    'passing_tds': 'passing_touchdowns',
    'receptions': 'receptions',
    'rush_attempts': 'rush_attempts',
    'rushing_attempts': 'rush_attempts',
    'pass_attempts': 'pass_attempts',
    'passing_attempts': 'pass_attempts',
    
    // Tier 2 - Good Coverage
    'assists': 'tackles_assists',
    'tackles': 'tackles_assists',
    'sacks': 'sacks',
    'int': 'interceptions',
    'interceptions': 'interceptions',
    'fg_made': 'field_goals_made',
    'field_goals': 'field_goals_made',
    'longest_reception': 'longest_reception',
    'completion_percentage': 'completion_percentage',
    'comp_pct': 'completion_percentage'
  },

  WNBA: {
    // Tier 1 - Excellent Coverage
    'points': 'points',
    'rebounds': 'rebounds',
    'assists': 'assists',
    'pts_rebs_asts': 'points_rebounds_assists',
    'pts+rebs+asts': 'points_rebounds_assists',
    'triple_double': 'points_rebounds_assists',
    '3pt_made': 'three_pointers_made',
    '3_pointers': 'three_pointers_made',
    'threes': 'three_pointers_made',
    'pts_asts': 'points_assists',
    'pts+asts': 'points_assists',
    'pts_rebs': 'points_rebounds',
    'pts+rebs': 'points_rebounds',
    'rebs_asts': 'rebounds_assists',
    'rebs+asts': 'rebounds_assists',
    
    // Tier 2 - Worth Including
    'points_combo': 'points_alternate',
    '3pt_combo': 'three_pointers_alternate',
    'assists_combo': 'assists_alternate',
    'rebounds_combo': 'rebounds_alternate'
  },

  MLB: {
    // Tier 1 - Excellent Coverage
    'hits': 'hits',
    'total_bases': 'total_bases',
    'rbis': 'runs_batted_in',
    'rbi': 'runs_batted_in',
    'runs': 'runs_scored',
    'runs_scored': 'runs_scored',
    'home_runs': 'home_runs',
    'hr': 'home_runs',
    'strikeouts': 'strikeouts',
    'so': 'strikeouts',
    'k': 'strikeouts',
    'walks': 'walks',
    'bb': 'walks',
    'stolen_bases': 'stolen_bases',
    'sb': 'stolen_bases',
    
    // Tier 2 - Worth Including
    'hits_runs_rbis': 'hits_runs_rbis',
    'h+r+rbi': 'hits_runs_rbis',
    'pitcher_wins': 'pitcher_wins',
    'wins': 'pitcher_wins',
    'innings_pitched': 'innings_pitched',
    'ip': 'innings_pitched',
    'earned_runs': 'earned_runs_allowed',
    'er': 'earned_runs_allowed'
  },

  NBA: {
    // Same as WNBA but with additional props
    'points': 'points',
    'rebounds': 'rebounds',
    'assists': 'assists',
    'pts_rebs_asts': 'points_rebounds_assists',
    'pts+rebs+asts': 'points_rebounds_assists',
    '3pt_made': 'three_pointers_made',
    '3_pointers': 'three_pointers_made',
    'pts_asts': 'points_assists',
    'pts+asts': 'points_assists',
    'pts_rebs': 'points_rebounds',
    'pts+rebs': 'points_rebounds',
    'rebs_asts': 'rebounds_assists',
    'rebs+asts': 'rebounds_assists',
    'steals': 'steals',
    'blocks': 'blocks',
    'turnovers': 'turnovers',
    'double_double': 'double_double',
    'triple_double': 'triple_double'
  }
}

// Team abbreviation mappings
export const TEAM_MAPPINGS = {
  NFL: {
    'Arizona Cardinals': 'ARI', 'Atlanta Falcons': 'ATL', 'Baltimore Ravens': 'BAL',
    'Buffalo Bills': 'BUF', 'Carolina Panthers': 'CAR', 'Chicago Bears': 'CHI',
    'Cincinnati Bengals': 'CIN', 'Cleveland Browns': 'CLE', 'Dallas Cowboys': 'DAL',
    'Denver Broncos': 'DEN', 'Detroit Lions': 'DET', 'Green Bay Packers': 'GB',
    'Houston Texans': 'HOU', 'Indianapolis Colts': 'IND', 'Jacksonville Jaguars': 'JAX',
    'Kansas City Chiefs': 'KC', 'Las Vegas Raiders': 'LV', 'Los Angeles Chargers': 'LAC',
    'Los Angeles Rams': 'LAR', 'Miami Dolphins': 'MIA', 'Minnesota Vikings': 'MIN',
    'New England Patriots': 'NE', 'New Orleans Saints': 'NO', 'New York Giants': 'NYG',
    'New York Jets': 'NYJ', 'Philadelphia Eagles': 'PHI', 'Pittsburgh Steelers': 'PIT',
    'San Francisco 49ers': 'SF', 'Seattle Seahawks': 'SEA', 'Tampa Bay Buccaneers': 'TB',
    'Tennessee Titans': 'TEN', 'Washington Commanders': 'WAS'
  },

  WNBA: {
    'Atlanta Dream': 'ATL', 'Chicago Sky': 'CHI', 'Connecticut Sun': 'CONN',
    'Dallas Wings': 'DAL', 'Indiana Fever': 'IND', 'Las Vegas Aces': 'LVA',
    'Minnesota Lynx': 'MIN', 'New York Liberty': 'NY', 'Phoenix Mercury': 'PHX',
    'Seattle Storm': 'SEA', 'Washington Mystics': 'WAS'
  },

  MLB: {
    'Arizona Diamondbacks': 'ARI', 'Atlanta Braves': 'ATL', 'Baltimore Orioles': 'BAL',
    'Boston Red Sox': 'BOS', 'Chicago Cubs': 'CHC', 'Chicago White Sox': 'CWS',
    'Cincinnati Reds': 'CIN', 'Cleveland Guardians': 'CLE', 'Colorado Rockies': 'COL',
    'Detroit Tigers': 'DET', 'Houston Astros': 'HOU', 'Kansas City Royals': 'KC',
    'Los Angeles Angels': 'LAA', 'Los Angeles Dodgers': 'LAD', 'Miami Marlins': 'MIA',
    'Milwaukee Brewers': 'MIL', 'Minnesota Twins': 'MIN', 'New York Mets': 'NYM',
    'New York Yankees': 'NYY', 'Oakland Athletics': 'OAK', 'Philadelphia Phillies': 'PHI',
    'Pittsburgh Pirates': 'PIT', 'San Diego Padres': 'SD', 'San Francisco Giants': 'SF',
    'Seattle Mariners': 'SEA', 'St. Louis Cardinals': 'STL', 'Tampa Bay Rays': 'TB',
    'Texas Rangers': 'TEX', 'Toronto Blue Jays': 'TOR', 'Washington Nationals': 'WAS'
  },

  NBA: {
    'Atlanta Hawks': 'ATL', 'Boston Celtics': 'BOS', 'Brooklyn Nets': 'BKN',
    'Charlotte Hornets': 'CHA', 'Chicago Bulls': 'CHI', 'Cleveland Cavaliers': 'CLE',
    'Dallas Mavericks': 'DAL', 'Denver Nuggets': 'DEN', 'Detroit Pistons': 'DET',
    'Golden State Warriors': 'GSW', 'Houston Rockets': 'HOU', 'Indiana Pacers': 'IND',
    'Los Angeles Clippers': 'LAC', 'Los Angeles Lakers': 'LAL', 'Memphis Grizzlies': 'MEM',
    'Miami Heat': 'MIA', 'Milwaukee Bucks': 'MIL', 'Minnesota Timberwolves': 'MIN',
    'New Orleans Pelicans': 'NO', 'New York Knicks': 'NYK', 'Oklahoma City Thunder': 'OKC',
    'Orlando Magic': 'ORL', 'Philadelphia 76ers': 'PHI', 'Phoenix Suns': 'PHX',
    'Portland Trail Blazers': 'POR', 'Sacramento Kings': 'SAC', 'San Antonio Spurs': 'SA',
    'Toronto Raptors': 'TOR', 'Utah Jazz': 'UTA', 'Washington Wizards': 'WAS'
  }
}

// Parse prop type based on sport
export function parsePropType(sport: string, propType: string, description?: string): string {
  const sportMappings = SPORT_PROP_MAPPINGS[sport as keyof typeof SPORT_PROP_MAPPINGS]
  if (!sportMappings) return propType.toLowerCase()

  const normalizedType = propType.toLowerCase().replace(/\s+/g, '_')
  return sportMappings[normalizedType as keyof typeof sportMappings] || normalizedType
}

// Extract team abbreviation
export function extractTeamAbbreviation(sport: string, teamName: string): string {
  const sportTeams = TEAM_MAPPINGS[sport as keyof typeof TEAM_MAPPINGS]
  if (!sportTeams) return teamName

  return sportTeams[teamName as keyof typeof sportTeams] || teamName
}

// Parse manual data paste (like your WNBA example)
export function parseManualDataPaste(sport: string, pastedData: string): SportsProp[] {
  const props: SportsProp[] = []
  const lines = pastedData.trim().split('\n')
  
  let currentPlayer: Partial<SportsProp> = {}
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    
    // Player name line (usually first line of each block)
    if (line && !line.includes('@') && !line.includes('vs') && !line.match(/^\d+\.?\d*$/)) {
      // Check if this looks like a player name
      if (!line.includes('Trending') && !line.includes('Less') && !line.includes('More')) {
        currentPlayer = {
          player_name: line,
          sport: sport
        }
      }
    }
    
    // Team and opponent line
    if (line.includes('@') || line.includes('vs')) {
      const parts = line.split(/(@|vs)/)
      if (parts.length >= 3) {
        const team = parts[0].trim()
        const opponent = parts[2].trim().split(' ')[0] // Remove time
        currentPlayer.team = team
        currentPlayer.opponent = opponent
        
        // Extract game time if present
        const timeMatch = line.match(/\w{3} \d{1,2}:\d{2}[ap]m/)
        if (timeMatch) {
          currentPlayer.game_time = timeMatch[0]
        }
      }
    }
    
    // Line value (number)
    if (line.match(/^\d+\.?\d*$/)) {
      currentPlayer.line_value = parseFloat(line)
    }
    
    // Prop type (Points, Rebounds, etc.)
    if (line && !line.match(/^\d+\.?\d*$/) && !line.includes('@') && !line.includes('vs') && 
        !line.includes('Trending') && !line.includes('Less') && !line.includes('More') &&
        currentPlayer.player_name && line !== currentPlayer.player_name) {
      
      currentPlayer.prop_type = parsePropType(sport, line)
      
      // If we have all required fields, create the prop
      if (currentPlayer.player_name && currentPlayer.prop_type && currentPlayer.line_value !== undefined) {
        props.push({
          external_id: `manual_${Date.now()}_${props.length}`,
          sport: sport,
          player_name: currentPlayer.player_name,
          team: currentPlayer.team,
          opponent: currentPlayer.opponent,
          prop_type: currentPlayer.prop_type,
          line_value: currentPlayer.line_value,
          game_time: currentPlayer.game_time || new Date().toISOString(),
          is_active: true
        })
        
        // Reset for next prop
        currentPlayer = {
          player_name: currentPlayer.player_name,
          team: currentPlayer.team,
          opponent: currentPlayer.opponent,
          game_time: currentPlayer.game_time,
          sport: sport
        }
      }
    }
    
    // Trending volume
    if (line.includes('Trending')) {
      const volumeMatch = line.match(/(\d+\.?\d*[KM]?)/)
      if (volumeMatch && currentPlayer) {
        const volume = volumeMatch[1]
        currentPlayer.trending_volume = parseVolume(volume)
      }
    }
  }
  
  return props
}

// Parse volume indicators (1.9K, 1.4K, etc.)
function parseVolume(volume: string): number {
  const num = parseFloat(volume.replace(/[KM]/g, ''))
  if (volume.includes('K')) return num * 1000
  if (volume.includes('M')) return num * 1000000
  return num
}

// Validate parsed prop
export function validateProp(prop: SportsProp): boolean {
  return !!(
    prop.player_name &&
    prop.sport &&
    prop.prop_type &&
    prop.line_value !== undefined &&
    prop.line_value > 0
  )
}
