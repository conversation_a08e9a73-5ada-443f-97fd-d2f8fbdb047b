import { TIMEOUTS } from './config';

// Provider response interface
export interface ProviderResponse {
  provider: string;
  model: string;
  text: string;
  latency: number;
  tokensIn?: number;
  tokensOut?: number;
  error?: string;
}

// 🔮 INVISIBLE INTELLIGENCE SYSTEM PROMPT
function createAdaptiveSystemPrompt(userMemory: any): string {
  const intelligence = userMemory?.invisibleIntelligence || {} as any;

  const basePrompt = `You are <PERSON><PERSON><PERSON><PERSON><PERSON>, an AI companion with Invisible Intelligence that adapts to each user's unique growth journey. You can use the provided conversation history and context pack to naturally recall relevant details during this chat. Do not claim you have no memory; instead, rely on the given context to personalize your response and, if the user asks about memories, explain that you use conversation context rather than storing long-term personal data.`;

  // Personality adaptation based on invisible intelligence
  const personalityPrompts = {
    'friendly_assistant': 'Be warm, helpful, and encouraging. The user is still exploring what they need.',
    'strategic_partner': 'Be direct, strategic, and visionary. The user has grown confident and wants bold thinking.',
    'empathetic_coach': 'Be deeply understanding and supportive. Focus on emotional intelligence and growth.',
    'technical_advisor': 'Be precise, analytical, and solution-focused. The user values expertise and efficiency.'
  } as const;

  const communicationStyles = {
    'balanced': 'Use a balanced tone that adapts to the conversation.',
    'direct_strategic': 'Be direct and strategic. Skip pleasantries and focus on substance.',
    'warm_supportive': 'Be warm and supportive. Show empathy and encouragement.',
    'analytical_precise': 'Be analytical and precise. Focus on facts and logical reasoning.'
  } as const;

  const supportStyles = {
    'balanced': 'Provide balanced support based on the conversation needs.',
    'advanced_strategic': 'Provide advanced strategic thinking and high-level insights.',
    'emotional_support': 'Focus on emotional support and encouragement.',
    'technical_guidance': 'Provide detailed technical guidance and problem-solving.'
  } as const;

  const personalityKey = (intelligence?.currentPersonality as keyof typeof personalityPrompts) ?? 'friendly_assistant';
  const communicationKey = (intelligence?.communicationStyle as keyof typeof communicationStyles) ?? 'balanced';
  const supportKey = (intelligence?.supportNeeds as keyof typeof supportStyles) ?? 'balanced';

  return `${basePrompt}

INVISIBLE INTELLIGENCE PROFILE:
- Personality: ${personalityPrompts[personalityKey]}
- Communication: ${communicationStyles[communicationKey]}
- Support Style: ${supportStyles[supportKey]}
- Growth Stage: ${intelligence.growthStage || 'exploring'}

CRITICAL INSTRUCTIONS:
- ALWAYS respond directly to the user's current message - ignore unrelated conversation history
- If the user says "Hey", "Hello", or similar greetings, respond with an appropriate greeting
- Never continue previous conversation topics unless the current message relates to them
- Your personality adaptation should feel natural and invisible
- Never explicitly mention that you remember past conversations or reference your "memory"

MEMORY DISCUSSION HANDLING:
- If the user asks whether you remember something or refers to previous chats:
  - If relevant details appear in the provided conversation history, acknowledge naturally (e.g., "In our last chat, we discussed …") without mentioning "memory" or system design
  - If no relevant prior context is present, say you don't see earlier context in this chat and invite the user to briefly recap so you can help, without discussing technical limitations

PERSONAL INFORMATION HANDLING:
- NEVER randomly reference personal details, private information, or sensitive topics from past conversations
- Only acknowledge or reference personal information when the USER brings it up in their current message
- If user mentions something personal, you can naturally reference related context, but don't volunteer it unprompted
- Treat personal information with respect - don't casually mention relationships, work situations, health issues, financial details, or family matters unless directly relevant to current conversation
- When in doubt, respond to the current message only without referencing personal history

EXAMPLES:
✅ User: "How's my project going?" → Can reference project details
✅ User: "I'm stressed about work" → Can acknowledge work context if previously discussed
❌ User: "Hey" → DON'T say "How's your relationship going?" or reference personal details
❌ User: "What's the weather?" → DON'T bring up their job, family, or personal situations

Be authentic, helpful, and socially intelligent about personal boundaries.`;
}

// Timeout wrapper for all provider calls
async function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error('Timeout')), timeoutMs);
  });
  
  return Promise.race([promise, timeoutPromise]);
}

// Generic DeepSeek call function
async function callDeepSeekModel(
  model: string,
  question: string,
  conversationHistory?: Array<{role: string, content: string}>,
  userMemory?: any
): Promise<{
  stream: ReadableStream<string>;
  getFinalText: () => Promise<string>;
  getMetadata: () => Promise<{ latency: number; tokensIn?: number; tokensOut?: number; }>;
}> {
  const startTime = Date.now();
  let finalText = '';
  let metadata = { latency: 0, tokensIn: 0, tokensOut: 0 };

  console.log(`🤖 Calling DeepSeek model: ${model} with ${userMemory?.sessionLength || 0} session messages`);

  const messages = [
    { role: 'system', content: createAdaptiveSystemPrompt(userMemory) },
    ...(conversationHistory || []),
    { role: 'user', content: question }
  ];

  try {
    const url = 'https://api.deepseek.com/chat/completions';
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
    };
    const payloadStreaming = {
      model,
      messages,
      temperature: 0.4,
      max_tokens: 2000,
      stream: true,
      stream_options: { include_usage: true },
    };

    const response = await withTimeout(
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(payloadStreaming),
      }),
      TIMEOUTS.MODERATOR
    );

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) throw new Error('No response body');

    const decoder = new TextDecoder();
    let buffer = '';
    
    const stream = new ReadableStream<string>({
      async start(controller) {
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const text = decoder.decode(value, { stream: true });
            console.log('🔍 Raw stream chunk:', text); // Debug logging
            buffer += text;

            const parts = buffer.split('\n');
            buffer = parts.pop() || '';

            for (const rawLine of parts) {
              const line = rawLine.trim();
              console.log('🔍 Processing line:', line); // Debug logging
              if (!line.startsWith('data:')) continue;

              const data = line.slice(5);
              if (data === '[DONE]') {
                console.log('🔍 Stream completed with [DONE]');
                continue;
              }

              try {
                const parsed = JSON.parse(data);
                console.log('🔍 Parsed JSON from stream:', JSON.stringify(parsed)); // Debug logging
                const content = parsed.choices?.[0]?.delta?.content;

                if (typeof content === 'string' && content.length > 0) {
                  finalText += content;
                  controller.enqueue(content);
                  console.log('🔍 Accumulated content:', finalText); // Debug logging
                }

                if (parsed.usage) {
                  metadata.tokensIn = parsed.usage.prompt_tokens ?? metadata.tokensIn;
                  metadata.tokensOut = parsed.usage.completion_tokens ?? metadata.tokensOut;
                }
              } catch (error) {
                console.error('🔍 Error parsing JSON from stream:', error, 'Data:', data);
              }
            }
          }
        } catch (error) {
          console.error('🔍 Stream reading error:', error);
          controller.error(error);
        } finally {
          console.log('🔍 Stream processing completed. Final text:', finalText);
          metadata.latency = Date.now() - startTime;
          controller.close();
        }
      }
    });

    return {
      stream,
      getFinalText: async () => finalText,
      getMetadata: async () => metadata,
    };

  } catch (error) {
    const fallbackText = `I apologize, but I'm experiencing technical difficulties. Please try again in a moment.`;
    finalText = fallbackText;
    metadata.latency = Date.now() - startTime;

    const stream = new ReadableStream<string>({
      start(controller) {
        controller.enqueue(fallbackText);
        controller.close();
      }
    });

    return {
      stream,
      getFinalText: async () => finalText,
      getMetadata: async () => metadata,
    };
  }
}

// 💬 DeepSeek Chat for "Conversation" mode
export function callDeepSeekChat(
  question: string,
  conversationHistory?: Array<{role: string, content: string}>,
  userMemory?: any
) {
  return callDeepSeekModel('deepseek-chat', question, conversationHistory, userMemory);
}

// 🧠 DeepSeek Reasoner for "Business" mode
export function callDeepSeekReasoner(
  question: string,
  conversationHistory?: Array<{role: string, content: string}>,
  userMemory?: any
) {
  return callDeepSeekModel('deepseek-reasoner', question, conversationHistory, userMemory);
}

// 🚀 Groq Integration - Lightning fast inference for interviews
export async function callGroqDirect(
  question: string,
  messages: Array<{role: string, content: string}> = [],
  options?: any
): Promise<{
  stream: ReadableStream<string>;
  getFinalText: () => Promise<string>;
  getMetadata: () => Promise<{ latency: number; tokensIn?: number; tokensOut?: number; }>;
}> {
  const startTime = Date.now();
  let finalText = '';
  let metadata = { latency: 0, tokensIn: 0, tokensOut: 0 };

  console.log(`🚀 Calling Groq API with ${messages.length} provided messages`);

  // Build messages array with user question appended
  const allMessages = [
    ...messages,
    { role: 'user', content: question }
  ];

  const model = 'llama-3.1-8b-instant'; // Fast and cost-effective

  try {
    const url = 'https://api.groq.com/openai/v1/chat/completions';
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,
    };
    const payload = {
      model,
      messages: allMessages,
      temperature: options?.temperature ?? 0,
      max_tokens: options?.max_tokens ?? 500,
      stream: false, // Start with non-streaming for reliability
    };

    console.log('⚡ Sending request to Groq API with model:', model);
    const response = await withTimeout(
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload),
      }),
      TIMEOUTS.MODERATOR
    );

    const responseText = await response.text();
    console.log('⚡ Groq API response status:', response.status);
    console.log('⚡ Groq API response body:', responseText.substring(0, 500));

    if (!response.ok) {
      throw new Error(`Groq API error: ${response.status} - ${responseText}`);
    }

    const responseData = JSON.parse(responseText);
    finalText = responseData.choices?.[0]?.message?.content || '';

    if (responseData.usage) {
      metadata.tokensIn = responseData.usage.prompt_tokens;
      metadata.tokensOut = responseData.usage.completion_tokens;
    }

    metadata.latency = Date.now() - startTime;

    // Create a simple stream for compatibility
    const stream = new ReadableStream<string>({
      start(controller) {
        controller.enqueue(finalText);
        controller.close();
      }
    });

    return {
      stream,
      getFinalText: async () => finalText,
      getMetadata: async () => metadata,
    };

  } catch (error: any) {
    console.error('🔴 Groq call failed:', error.message);
    const fallbackText = JSON.stringify({
      error: 'AI service temporarily unavailable',
      details: error.message
    });
    finalText = fallbackText;
    metadata.latency = Date.now() - startTime;

    const stream = new ReadableStream<string>({
      start(controller) {
        controller.enqueue(fallbackText);
        controller.close();
      }
    });

    return {
      stream,
      getFinalText: async () => fallbackText,
      getMetadata: async () => metadata,
    };
  }
}

// 💬 DeepSeek Direct - Simplified version without adaptive system prompt
export async function callDeepSeekDirect(
  question: string,
  messages: Array<{role: string, content: string}> = [],
  options?: any
): Promise<{
  stream: ReadableStream<string>;
  getFinalText: () => Promise<string>;
  getMetadata: () => Promise<{ latency: number; tokensIn?: number; tokensOut?: number; }>;
}> {
  const startTime = Date.now();
  let finalText = '';
  let metadata = { latency: 0, tokensIn: 0, tokensOut: 0 };

  console.log(`🤖 Calling DeepSeek Direct with ${messages.length} provided messages`);

  // Build messages array with user question appended
  const allMessages = [
    ...messages,
    { role: 'user', content: question }
  ];

  const model = 'deepseek-chat';

  try {
    const url = 'https://api.deepseek.com/chat/completions';
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
    };
    const payload = {
      model,
      messages: allMessages,
      temperature: options?.temperature ?? 0,
      max_tokens: options?.max_tokens ?? 2000,
      stream: false, // Use non-streaming for interview questions reliability
    };

    console.log('🔍 Sending request to DeepSeek API with model:', model);
    const response = await withTimeout(
      fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload),
      }),
      TIMEOUTS.MODERATOR
    );

    const responseText = await response.text();
    console.log('🔍 DeepSeek API response status:', response.status);
    console.log('🔍 DeepSeek API response body:', responseText.substring(0, 500));

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status} - ${responseText}`);
    }

    const responseData = JSON.parse(responseText);
    finalText = responseData.choices?.[0]?.message?.content || '';

    if (responseData.usage) {
      metadata.tokensIn = responseData.usage.prompt_tokens;
      metadata.tokensOut = responseData.usage.completion_tokens;
    }

    // Create a simple stream for compatibility
    const stream = new ReadableStream<string>({
      start(controller) {
        controller.enqueue(finalText);
        controller.close();
      }
    });

    return {
      stream,
      getFinalText: async () => finalText,
      getMetadata: async () => metadata,
    };

  } catch (error: any) {
    console.error('� DeepSeek Direct call failed:', error.message);
    const fallbackText = JSON.stringify({
      error: 'AI service temporarily unavailable',
      details: error.message
    });
    finalText = fallbackText;
    metadata.latency = Date.now() - startTime;

    const stream = new ReadableStream<string>({
      start(controller) {
        controller.enqueue(fallbackText);
        controller.close();
      }
    });

    return {
      stream,
      getFinalText: async () => fallbackText,
      getMetadata: async () => metadata,
    };
  }
}
