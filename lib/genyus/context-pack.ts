import { createClient } from "@supabase/supabase-js";

// Lightweight, scalable Context Pack for request-time use and background updates.
// - getContextPack(userId, question): returns a compact string to prepend to conversation history
// - updateContextPackAfterTurn(userId, question, answer): background merge with caps

const CONTEXT_CAP_TOKENS = 900; // rough cap; we enforce by characters
const MAX_CONTEXT_CHARS = 3500; // ~ 900-1100 tokens depending on text
const MAX_SAVED_MEM_LINES = 6; // keep it small
const MAX_SAVED_MEM_CHAR_PER_LINE = 180;
const MAX_RECENT_FACTS = 6;

function createSupabaseServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: { persistSession: false, autoRefreshToken: false },
    }
  );
}

function safeTruncate(text: string, maxChars: number): string {
  if (!text) return "";
  if (text.length <= maxChars) return text;
  // try to cut on sentence boundary
  const cut = text.slice(0, maxChars);
  const lastPeriod = cut.lastIndexOf(". ");
  return (lastPeriod > maxChars * 0.6 ? cut.slice(0, lastPeriod + 1) : cut) + "\n…";
}

function pickRelevantFacts(question: string, facts: string[], limit: number): string[] {
  if (!facts.length) return [];
  const q = (question || "").toLowerCase();
  const qTokens = new Set(q.split(/[^a-z0-9]+/i).filter(Boolean));
  const scored = facts.map((f, idx) => {
    const tokens = f.toLowerCase().split(/[^a-z0-9]+/i).filter(Boolean);
    let score = 0;
    for (const t of tokens) {
      if (qTokens.has(t)) score += 1;
    }
    // slight preference to newer (original order assumed newest first)
    return { f, score: score + (facts.length - idx) * 0.01 };
  });
  scored.sort((a, b) => b.score - a.score);
  return scored.slice(0, limit).map(s => s.f);
}

export async function getContextPack(userId: string, question: string): Promise<string> {
  try {
    const supabase = createSupabaseServiceClient();

    // Rolling summary (single row)
    let summaryText = "";
    try {
      const { data } = await supabase
        .from("genyus_rolling_memory")
        .select("summary_text")
        .eq("user_id", userId)
        .single();
      summaryText = data?.summary_text || "";
    } catch (_) {
      // table may not exist yet
      summaryText = "";
    }

    // Saved memories (active true)
    let savedFacts: string[] = [];
    try {
      const { data } = await supabase
        .from("genyus_saved_memories")
        .select("content, updated_at")
        .eq("user_id", userId)
        .eq("active", true)
        .order("updated_at", { ascending: false })
        .limit(50);
      const all = (data || []).map((r: any) => String(r.content || "").trim()).filter(Boolean);
      const relevant = pickRelevantFacts(question, all, MAX_RECENT_FACTS);
      savedFacts = relevant
        .map(line => safeTruncate(line, MAX_SAVED_MEM_CHAR_PER_LINE))
        .slice(0, MAX_SAVED_MEM_LINES);
    } catch (_) {
      // table may not exist yet
      savedFacts = [];
    }

    // Build compact context text with clear sections
    const parts: string[] = [];
    if (summaryText) {
      parts.push("[Profile]\n" + safeTruncate(summaryText, Math.floor(MAX_CONTEXT_CHARS * 0.6)));
    }
    if (savedFacts.length) {
      parts.push("[Saved Facts]\n- " + savedFacts.join("\n- "));
    }

    const context = parts.join("\n\n");
    return safeTruncate(context, MAX_CONTEXT_CHARS);
  } catch (error) {
    console.error("getContextPack error:", error);
    return ""; // never block the hot path
  }
}

// Simple delta summarization: fold the latest Q/A into rolling summary, with caps.
export async function updateContextPackAfterTurn(
  userId: string,
  question: string,
  answer: string
): Promise<void> {
  try {
    const supabase = createSupabaseServiceClient();

    // Load current summary
    let current = "";
    try {
      const { data } = await supabase
        .from("genyus_rolling_memory")
        .select("summary_text")
        .eq("user_id", userId)
        .single();
      current = data?.summary_text || "";
    } catch (_) {
      current = "";
    }

    // Compose a lightweight delta. Keep it simple and deterministic for speed.
    const delta = `Update:\n- Q: ${safeTruncate(question, 300)}\n- A: ${safeTruncate(answer, 500)}`;

    // Merge and cap; put newest first
    const merged = safeTruncate(`${delta}\n\n${current}`, MAX_CONTEXT_CHARS);

    // Upsert
    try {
      const { error } = await supabase
        .from("genyus_rolling_memory")
        .upsert({
          user_id: userId,
          summary_text: merged,
          updated_at: new Date().toISOString()
        }, { onConflict: "user_id" });
      if (error) throw error;
    } catch (e) {
      // If table is missing, just log once
      console.warn("updateContextPackAfterTurn upsert skipped (table missing?)", e);
    }

    // Extract simple saved facts candidates (pattern-based)
    try {
      const candidates: string[] = [];
      const lower = (question + "\n" + answer).toLowerCase();
      const patterns = ["i am ", "i'm ", "i prefer ", "i like ", "my project", "we decided ", "i need ", "we will "];
      for (const p of patterns) {
        const idx = lower.indexOf(p);
        if (idx !== -1) {
          const slice = (question + "\n" + answer).slice(Math.max(0, idx - 20), idx + 140);
          candidates.push(slice.replace(/\s+/g, " ").trim());
        }
      }
      const unique = Array.from(new Set(candidates)).slice(0, 5);
      if (unique.length) {
        // Insert as inactive=false? We insert active=true for now; can switch to review flow later
        const rows = unique.map(content => ({
          user_id: userId,
          content: safeTruncate(content, 200),
          active: true,
          confidence: 5,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }));
        // Best effort insert; swallow errors without throwing
        const { error: memErr } = await supabase.from("genyus_saved_memories").insert(rows);
        if (memErr) console.warn("Saved memories insert skipped:", memErr);
      }
    } catch (e) {
      console.warn("Saved memories extraction skipped:", e);
    }
  } catch (error) {
    console.error("updateContextPackAfterTurn error:", error);
  }
}

