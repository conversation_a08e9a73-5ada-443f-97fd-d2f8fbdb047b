import { createClient } from "@supabase/supabase-js";
import { callDeepSeekChat } from './providers';

function createSupabaseServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false
      }
    }
  );
}

export interface ConversationSession {
  id: string;
  title: string;
  topic_category: string;
  session_summary: string;
  started_at: string;
  ended_at: string | null;
  message_count: number;
}

// Generate conversation title using timestamp
async function generateConversationTitle(messages: Array<{role: string, content: string}>): Promise<{title: string, category: string, summary: string}> {
  try {
    // Use the first user message as title, truncated if necessary
    const firstUserMessage = messages.find(m => m.role === 'user')?.content || '';
    const title = firstUserMessage.length > 50 
      ? firstUserMessage.substring(0, 47) + '...'
      : firstUserMessage || 'Conversation';
    
    // Use current date/time for the summary to make it unique
    const now = new Date();
    const summary = `Conversation from ${now.toLocaleDateString()} ${now.toLocaleTimeString()}`;
    
    // Simple category detection based on content
    let category = 'other';
    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('work') || lowerTitle.includes('business') || lowerTitle.includes('career')) {
      category = 'work';
    } else if (lowerTitle.includes('personal') || lowerTitle.includes('life') || lowerTitle.includes('family')) {
      category = 'personal';
    } else if (lowerTitle.includes('learn') || lowerTitle.includes('education') || lowerTitle.includes('study')) {
      category = 'learning';
    } else if (lowerTitle.includes('creative') || lowerTitle.includes('art') || lowerTitle.includes('write')) {
      category = 'creative';
    } else if (lowerTitle.includes('plan') || lowerTitle.includes('goal') || lowerTitle.includes('project')) {
      category = 'planning';
    }

    return {
      title: title,
      category: category,
      summary: summary
    };
  } catch (error) {
    console.error('Error generating conversation title:', error);
    // Fallback to simple title generation
    const firstUserMessage = messages.find(m => m.role === 'user')?.content || '';
    const title = firstUserMessage.length > 50 
      ? firstUserMessage.substring(0, 47) + '...'
      : firstUserMessage || 'Untitled Conversation';
    
    return {
      title,
      category: 'other',
      summary: 'Conversation summary'
    };
  }
}

// Save conversation session with AI-generated title
export async function saveConversationSession(
  userId: string,
  messages: Array<{role: string, content: string}>,
  requestIds: string[]
): Promise<string | null> {
  if (messages.length < 2) return null; // Need at least one exchange
  
  const supabase = createSupabaseServiceClient();
  
  try {
    // Generate AI title and metadata
    const { title, category, summary } = await generateConversationTitle(messages);
    
    // Create conversation session
    const { data: session, error: sessionError } = await supabase
      .from('genyus_conversation_sessions')
      .insert({
        user_id: userId,
        title,
        topic_category: category,
        session_summary: summary,
        started_at: new Date().toISOString(),
        ended_at: new Date().toISOString(),
        message_count: messages.length
      })
      .select()
      .single();

    if (sessionError) {
      console.error('Error creating conversation session:', sessionError);
      return null;
    }

    // Link requests to this session
    if (requestIds.length > 0) {
      const { error: contextError } = await supabase
        .from('genyus_conversation_context')
        .insert(
          requestIds.map(requestId => ({
            user_id: userId,
            session_id: session.id,
            request_id: requestId,
            message_type: 'question'
          }))
        );

      if (contextError) {
        console.error('Error linking requests to session:', contextError);
      }
    }

    console.log(`💾 Saved conversation session: "${title}" (${messages.length} messages)`);
    return session.id;
    
  } catch (error) {
    console.error('Error saving conversation session:', error);
    return null;
  }
}

// Get conversation sessions for history
export async function getConversationSessions(
  userId: string,
  limit: number = 20,
  offset: number = 0
): Promise<{sessions: ConversationSession[], total: number}> {
  const supabase = createSupabaseServiceClient();
  
  try {
    // Get sessions with pagination
    const { data: sessions, error: sessionsError } = await supabase
      .from('genyus_conversation_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('started_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (sessionsError) {
      console.error('Error fetching conversation sessions:', sessionsError);
      return { sessions: [], total: 0 };
    }

    // Get total count
    const { count, error: countError } = await supabase
      .from('genyus_conversation_sessions')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (countError) {
      console.error('Error counting conversation sessions:', countError);
    }

    return {
      sessions: sessions || [],
      total: count || 0
    };
    
  } catch (error) {
    console.error('Error in getConversationSessions:', error);
    return { sessions: [], total: 0 };
  }
}

// Get messages for a specific conversation session
export async function getSessionMessages(
  userId: string,
  sessionId: string
): Promise<Array<{role: string, content: string, timestamp: string}>> {
  const supabase = createSupabaseServiceClient();
  
  try {
    // Get request IDs for this session
    const { data: context, error: contextError } = await supabase
      .from('genyus_conversation_context')
      .select('request_id')
      .eq('user_id', userId)
      .eq('session_id', sessionId);

    if (contextError || !context) {
      console.error('Error fetching session context:', contextError);
      return [];
    }

    const requestIds = context.map(c => c.request_id);
    
    if (requestIds.length === 0) return [];

    // Get messages for these requests
    const { data: requests, error: requestsError } = await supabase
      .from('genyus_requests')
      .select(`
        question,
        created_at,
        genyus_answers!inner(final_text, created_at)
      `)
      .in('id', requestIds)
      .order('created_at', { ascending: true });

    if (requestsError) {
      console.error('Error fetching session messages:', requestsError);
      return [];
    }

    // Format as conversation messages
    const messages: Array<{role: string, content: string, timestamp: string}> = [];
    
    for (const request of requests || []) {
      const answer = request.genyus_answers[0];
      if (answer) {
        messages.push({
          role: 'user',
          content: request.question,
          timestamp: request.created_at
        });
        messages.push({
          role: 'assistant',
          content: answer.final_text,
          timestamp: answer.created_at
        });
      }
    }

    return messages;
    
  } catch (error) {
    console.error('Error in getSessionMessages:', error);
    return [];
  }
}

// Auto-save conversation when it reaches certain thresholds
export async function autoSaveConversationIfNeeded(
  userId: string,
  currentMessages: Array<{role: string, content: string}>,
  requestIds: string[]
): Promise<void> {
  // Auto-save after 6+ messages (3+ exchanges) or if conversation seems complete
  if (currentMessages.length >= 6) {
    const lastMessage = currentMessages[currentMessages.length - 1];
    
    // Save if it's been a substantial conversation
    if (lastMessage && (
      lastMessage.content.includes('thank') ||
      lastMessage.content.includes('great') ||
      lastMessage.content.includes('perfect') ||
      currentMessages.length >= 10
    )) {
      await saveConversationSession(userId, currentMessages, requestIds);
    }
  }
}
