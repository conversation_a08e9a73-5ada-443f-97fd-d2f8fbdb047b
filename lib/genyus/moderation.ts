import { callDeepSeekChat } from './providers';

export interface ModerationResult {
  ok: boolean;
  flagged: boolean;
  categories?: Record<string, boolean>;
  categoryScores?: Record<string, number>;
  error?: string;
}

export async function moderateContent(text: string): Promise<ModerationResult> {
  // Fallback to a simple profanity check if Gemini moderation fails
  const basicProfanityCheck = (inputText: string) => {
    const profaneWords = ['profanity1', 'profanity2']; // Add more words
    return profaneWords.some(word => inputText.toLowerCase().includes(word));
  };

  try {
    // Use DeepSeek for moderation
    const moderationPrompt = `Analyze the following text for harmful content. Respond with a JSON object with "flagged" (true/false) and "categories" (a list of violated policies, if any). Respond with ONLY the JSON object. Text: "${text}"`;
    const deepSeekResponse = await callDeepSeekChat(moderationPrompt);
    const responseText = await deepSeekResponse.getFinalText();
    console.log('DeepSeek moderation raw response:', responseText); // DEBUG
    
    // Regex to find <PERSON><PERSON><PERSON> in markdown or plain text
    const jsonRegex = /```json\s*(\{[\s\S]*?\})\s*```|(\{[\s\S]*\})/;
    const jsonMatch = responseText.match(jsonRegex);

    if (!jsonMatch || (!jsonMatch[1] && !jsonMatch[2])) {
      console.warn('No JSON object found in Gemini moderation response, assuming content is safe.');
      return { ok: true, flagged: false }; // Graceful fallback
    }
    
    // Extract the JSON string from the correct capture group
    const jsonString = jsonMatch[1] || jsonMatch[2];
    const parsedResponse = JSON.parse(jsonString);
    
    return {
      ok: !parsedResponse.flagged,
      flagged: parsedResponse.flagged,
      categories: (parsedResponse.categories || []).reduce((acc: any, category: any) => {
        acc[category] = true;
        return acc;
      }, {}),
      categoryScores: (parsedResponse.categories || []).reduce((acc: any, category: any) => {
        acc[category] = 1.0; // Gemini doesn't provide scores, so set to 1.0
        return acc;
      }, {}),
    };
  } catch (error) {
    console.error('Gemini moderation error:', error);
    
    // Fallback to basic profanity check
    const flagged = basicProfanityCheck(text);
    
    return {
      ok: !flagged,
      flagged,
      error: 'Moderation fallback',
    };
  }
}

// Check if content should be blocked based on moderation
export function shouldBlockContent(moderation: ModerationResult): boolean {
  if (!moderation.flagged) return false;
  
  // Block high-confidence harmful content
  const highRiskCategories = [
    'hate',
    'hate/threatening', 
    'harassment/threatening',
    'self-harm',
    'self-harm/intent',
    'sexual/minors',
    'violence',
    'violence/graphic'
  ];

  if (moderation.categories && moderation.categoryScores) {
    for (const category of highRiskCategories) {
      if (moderation.categories[category] && moderation.categoryScores[category] > 0.8) {
        return true;
      }
    }
  }

  return false;
}

// Generate safe refusal message
export function generateRefusalMessage(moderation: ModerationResult): string {
  const flaggedCategories = moderation.categories 
    ? Object.keys(moderation.categories).filter(key => moderation.categories![key])
    : [];

  if (flaggedCategories.includes('hate') || flaggedCategories.includes('harassment')) {
    return "I can't provide responses that contain harmful or harassing content. Please rephrase your question in a respectful way.";
  }
  
  if (flaggedCategories.includes('violence')) {
    return "I can't provide information that could promote violence or harm. Please ask about something else.";
  }
  
  if (flaggedCategories.includes('sexual')) {
    return "I can't provide responses with sexual content. Please ask about a different topic.";
  }
  
  if (flaggedCategories.includes('self-harm')) {
    return "I'm concerned about your wellbeing. If you're having thoughts of self-harm, please reach out to a mental health professional or crisis helpline.";
  }

  // Generic refusal
  return "I can't provide a response to that question. Please try asking about something else.";
}
