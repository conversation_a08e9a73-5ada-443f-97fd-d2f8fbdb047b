import { createClient } from "@supabase/supabase-js";

// 🔮 INVISIBLE INTELLIGENCE SYSTEM
// Analyzes user growth patterns in background, influences AI personality invisibly

export interface InvisibleIntelligence {
  userId: string;
  currentPersonality: string;
  confidenceLevel: number;
  communicationStyle: string;
  energyLevel: string;
  supportNeeds: string;
  growthStage: string;
  lastAnalysis: string;
}

export interface GrowthPattern {
  userId: string;
  pattern: string;
  confidence: number;
  firstObserved: string;
  lastConfirmed: string;
  examples: string[];
}

export class InvisibleIntelligenceSystem {
  private supabase: any;

  constructor() {
    this.supabase = null;
  }

  async init() {
    // Use service-role client so backend can read/write memory tables securely
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      { auth: { persistSession: false, autoRefreshToken: false } }
    );
  }

  // Get current invisible intelligence profile (fast lookup)
  async getInvisibleIntelligence(userId: string): Promise<InvisibleIntelligence> {
    try {
      const { data, error } = await this.supabase
        .from('invisible_intelligence')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error || !data) {
        // Return default profile for new users
        return this.getDefaultProfile(userId);
      }

      return {
        userId: data.user_id,
        currentPersonality: data.current_personality || 'friendly_assistant',
        confidenceLevel: data.confidence_level || 5,
        communicationStyle: data.communication_style || 'balanced',
        energyLevel: data.energy_level || 'moderate',
        supportNeeds: data.support_needs || 'balanced',
        growthStage: data.growth_stage || 'exploring',
        lastAnalysis: data.last_analysis || new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting invisible intelligence:', error);
      return this.getDefaultProfile(userId);
    }
  }

  // Get session context (recent conversation in current session)
  async getSessionContext(userId: string): Promise<any[]> {
    try {
      // Get recent conversation context across all time (do not auto-reset by time)
      const { data, error } = await this.supabase
        .from('genyus_requests')
        .select(`
          question,
          created_at,
          genyus_answers(final_text)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(40); // Fetch 40 requests to support up to 40 messages (20 exchanges)


      if (error || !data) {
        return [];
      }

      // No automatic conversation resets - let users control with "New Chat" button

      // Convert to conversation format
      const sessionMessages = [];
      for (const item of data.reverse()) {
        sessionMessages.push(
          { role: 'user', content: item.question },
          { role: 'assistant', content: item.genyus_answers?.[0]?.final_text || '' }
        );
      }

      return sessionMessages; // Return all converted messages (up to 40)
    } catch (error) {
      console.error('Error getting session context:', error);
      return [];
    }
  }

  // Background analysis: Analyze user growth patterns (runs after conversations)
  async analyzeGrowthPatterns(userId: string, question: string, response: string): Promise<void> {
    try {
      // This runs in background, doesn't block responses
      setTimeout(async () => {
        await this.performGrowthAnalysis(userId, question, response);
      }, 100);
    } catch (error) {
      console.error('Error in background growth analysis:', error);
    }
  }

  // Perform deep growth analysis (background only)
  private async performGrowthAnalysis(userId: string, question: string, response: string): Promise<void> {
    try {
      console.log(`🔮 Background: Analyzing growth patterns for user ${userId}`);

      // Get recent conversation history for pattern analysis
      const recentHistory = await this.getRecentHistory(userId, 50);
      
      // Analyze patterns
      const patterns = this.detectGrowthPatterns(recentHistory);
      const personality = this.calculatePersonalityAdjustments(patterns);
      
      // Update invisible intelligence profile
      await this.updateInvisibleProfile(userId, personality, patterns);
      
      console.log(`✨ Background: Updated invisible intelligence for user ${userId}`);
    } catch (error) {
      console.error('Error in growth analysis:', error);
    }
  }

  // Detect growth patterns from conversation history
  private detectGrowthPatterns(history: any[]): GrowthPattern[] {
    const patterns: GrowthPattern[] = [];

    // Analyze confidence evolution
    const confidencePattern = this.analyzeConfidenceEvolution(history);
    if (confidencePattern) patterns.push(confidencePattern);

    // Analyze communication style evolution
    const communicationPattern = this.analyzeCommunicationEvolution(history);
    if (communicationPattern) patterns.push(communicationPattern);

    // Analyze complexity evolution
    const complexityPattern = this.analyzeComplexityEvolution(history);
    if (complexityPattern) patterns.push(complexityPattern);

    return patterns;
  }

  // Calculate personality adjustments based on patterns
  private calculatePersonalityAdjustments(patterns: GrowthPattern[]): Partial<InvisibleIntelligence> {
    const adjustments: Partial<InvisibleIntelligence> = {};

    // Adjust personality based on detected patterns
    const confidencePattern = patterns.find(p => p.pattern === 'confidence_growth');
    if (confidencePattern && confidencePattern.confidence > 0.7) {
      adjustments.currentPersonality = 'strategic_partner';
      adjustments.confidenceLevel = 8;
      adjustments.communicationStyle = 'direct_strategic';
    }

    const complexityPattern = patterns.find(p => p.pattern === 'complexity_growth');
    if (complexityPattern && complexityPattern.confidence > 0.7) {
      adjustments.supportNeeds = 'advanced_strategic';
      adjustments.growthStage = 'scaling';
    }

    return adjustments;
  }

  // Helper methods for pattern analysis
  private analyzeConfidenceEvolution(history: any[]): GrowthPattern | null {
    // Analyze if user's questions have become more confident over time
    const recentQuestions = history.slice(-10).map(h => h.question);
    const olderQuestions = history.slice(0, 10).map(h => h.question);

    const recentConfidence = this.calculateConfidenceScore(recentQuestions);
    const olderConfidence = this.calculateConfidenceScore(olderQuestions);

    if (recentConfidence > olderConfidence + 0.3) {
      return {
        userId: '',
        pattern: 'confidence_growth',
        confidence: 0.8,
        firstObserved: new Date().toISOString(),
        lastConfirmed: new Date().toISOString(),
        examples: recentQuestions.slice(0, 3)
      };
    }

    return null;
  }

  private analyzeCommunicationEvolution(history: any[]): GrowthPattern | null {
    // Analyze if communication style has evolved
    // Implementation would analyze language patterns, directness, etc.
    return null;
  }

  private analyzeComplexityEvolution(history: any[]): GrowthPattern | null {
    // Analyze if questions have become more complex/strategic
    // Implementation would analyze question complexity over time
    return null;
  }

  private calculateConfidenceScore(questions: string[]): number {
    let score = 0;
    const confidenceIndicators = ['I think', 'I believe', 'I want to', 'I need to', 'Let\'s', 'We should'];
    const uncertaintyIndicators = ['maybe', 'perhaps', 'I\'m not sure', 'could you help', 'I don\'t know'];

    questions.forEach(question => {
      const lowerQuestion = question.toLowerCase();
      confidenceIndicators.forEach(indicator => {
        if (lowerQuestion.includes(indicator)) score += 0.1;
      });
      uncertaintyIndicators.forEach(indicator => {
        if (lowerQuestion.includes(indicator)) score -= 0.1;
      });
    });

    return Math.max(0, Math.min(1, score / questions.length));
  }

  // Database operations
  private async getRecentHistory(userId: string, limit: number): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .from('genyus_requests')
        .select(`
          question,
          created_at,
          genyus_answers!inner(final_text)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      return data || [];
    } catch (error) {
      console.error('Error getting recent history:', error);
      return [];
    }
  }

  private async updateInvisibleProfile(userId: string, adjustments: Partial<InvisibleIntelligence>, patterns: GrowthPattern[]): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('invisible_intelligence')
        .upsert({
          user_id: userId,
          current_personality: adjustments.currentPersonality,
          confidence_level: adjustments.confidenceLevel,
          communication_style: adjustments.communicationStyle,
          energy_level: adjustments.energyLevel,
          support_needs: adjustments.supportNeeds,
          growth_stage: adjustments.growthStage,
          last_analysis: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error updating invisible profile:', error);
      }
    } catch (error) {
      console.error('Error in updateInvisibleProfile:', error);
    }
  }

  private getDefaultProfile(userId: string): InvisibleIntelligence {
    return {
      userId,
      currentPersonality: 'friendly_assistant',
      confidenceLevel: 5,
      communicationStyle: 'balanced',
      energyLevel: 'moderate',
      supportNeeds: 'balanced',
      growthStage: 'exploring',
      lastAnalysis: new Date().toISOString()
    };
  }
}

// Global functions for orchestrator
export async function getSessionContext(userId: string): Promise<any[]> {
  const system = new InvisibleIntelligenceSystem();
  await system.init();
  return system.getSessionContext(userId);
}

export async function getInvisibleIntelligence(userId: string): Promise<InvisibleIntelligence> {
  const system = new InvisibleIntelligenceSystem();
  await system.init();
  return system.getInvisibleIntelligence(userId);
}

export async function analyzeGrowthPatterns(userId: string, question: string, response: string): Promise<void> {
  const system = new InvisibleIntelligenceSystem();
  await system.init();
  return system.analyzeGrowthPatterns(userId, question, response);
}
