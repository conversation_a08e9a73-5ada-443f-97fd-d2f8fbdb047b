import { CACHE_TTL } from './config';

// Simple in-memory cache for edge runtime
// TODO: Replace with Redis for production scaling
class MemoryCache {
  private cache = new Map<string, { value: string; expires: number }>();
  
  set(key: string, value: string, ttlSeconds: number = CACHE_TTL): void {
    const expires = Date.now() + (ttlSeconds * 1000);
    this.cache.set(key, { value, expires });
    
    // Clean up expired entries periodically
    if (this.cache.size > 1000) {
      this.cleanup();
    }
  }
  
  get(key: string): string | null {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() > entry.expires) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.value;
  }
  
  delete(key: string): void {
    this.cache.delete(key);
  }
  
  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expires) {
        this.cache.delete(key);
      }
    }
  }
  
  clear(): void {
    this.cache.clear();
  }
  
  size(): number {
    return this.cache.size;
  }
}

// Global cache instance
const cache = new MemoryCache();

// Cache key generation
export function generateCacheKey(userId: string, question: string): string {
  // Simple hash function for cache keys
  let hash = 0;
  const input = `${userId}:${question.toLowerCase().trim()}`;
  
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  
  return `genyus:v1:${Math.abs(hash).toString(36)}`;
}

// Cache operations
export async function getCachedResponse(userId: string, question: string): Promise<string | null> {
  // In-memory caching disabled
  return null;
}

export async function setCachedResponse(
  userId: string,
  question: string,
  response: string,
  ttlSeconds: number = CACHE_TTL
): Promise<void> {
  // No-op: caching disabled
}

export async function deleteCachedResponse(userId: string, question: string): Promise<void> {
  // No-op: caching disabled
}

// Cache stats for monitoring
export function getCacheStats(): { size: number; hitRate?: number } {
  return {
    size: 0,
  };
}

// Clear cache (for testing/admin)
export function clearCache(): void {
  // No-op: caching disabled
}

// Redis implementation for production (when ready)
/*
import Redis from 'ioredis';

let redis: Redis | null = null;

function getRedis(): Redis {
  if (!redis) {
    redis = new Redis(process.env.UPSTASH_REDIS_REST_URL!, {
      password: process.env.UPSTASH_REDIS_REST_TOKEN,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
    });
  }
  return redis;
}

export async function getCachedResponse(userId: string, question: string): Promise<string | null> {
  try {
    const key = generateCacheKey(userId, question);
    const redis = getRedis();
    return await redis.get(key);
  } catch (error) {
    console.error('Cache get error:', error);
    return null;
  }
}

export async function setCachedResponse(
  userId: string, 
  question: string, 
  response: string,
  ttlSeconds: number = CACHE_TTL
): Promise<void> {
  try {
    const key = generateCacheKey(userId, question);
    const redis = getRedis();
    await redis.setex(key, ttlSeconds, response);
  } catch (error) {
    console.error('Cache set error:', error);
    // Don't throw - caching is not critical
  }
}
*/
