import { createClient, SupabaseClient } from '@supabase/supabase-js'
import { Database } from './database.types'

// This admin client is meant for server-side use only, in places like webhooks
// or other backend functions that need to bypass RLS.
// Never expose this client or the service role key to the browser.

let adminClient: SupabaseClient<Database> | null = null

export const createSupabaseAdminClient = () => {
  if (adminClient) {
    return adminClient
  }

  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    throw new Error('Missing Supabase URL or service role key for admin client.')
  }

  adminClient = createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    }
  )

  return adminClient
}
