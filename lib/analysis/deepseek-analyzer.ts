// DeepSeek V3 Sports Analysis Engine
// Cost-efficient prop analysis using local data

import OpenAI from 'openai'

const deepseek = new OpenAI({
  baseURL: 'https://api.deepseek.com',
  apiKey: process.env.DEEPSEEK_API_KEY
})

interface PropAnalysisData {
  player: {
    name: string
    position: string
    team: string
    seasonStats: Record<string, number>
    last5Games: number[]
    vsOpponentHistory: number[]
  }
  opponent: {
    name: string
    defensiveRanking: number
    allowedPerGame: number
  }
  prop: {
    type: string
    line: number
    gameTime: string
  }
  context: {
    weather?: string
    injuries?: string[]
    gameScript?: string
  }
}

function buildDeepSeekPrompt(data: PropAnalysisData): string {
  const { player, opponent, prop, context } = data

  return `You are an expert sports betting analyst. Analyze this prop bet using the provided data.

PLAYER DATA:
Name: ${player.name} (${player.position}, ${player.team})
Season Average: ${player.seasonStats[prop.type] || 'N/A'} per game
Last 5 Games: ${player.last5Games.join(', ')}
vs ${opponent.name} History: ${player.vsOpponentHistory.join(', ')}

MATCHUP DATA:
Opponent: ${opponent.name}
Defense Ranking vs ${player.position}: ${opponent.defensiveRanking}/32
Allows ${opponent.allowedPerGame} ${prop.type} per game

PROP DETAILS:
Line: ${prop.line} ${prop.type}
Game: ${new Date(prop.gameTime).toLocaleDateString()}

CONTEXT:
${context.weather ? `Weather: ${context.weather}` : ''}
${context.injuries?.length ? `Injuries: ${context.injuries.join(', ')}` : ''}
${context.gameScript ? `Game Script: ${context.gameScript}` : ''}

ANALYSIS FRAMEWORK:
1. Compare player's season average to prop line
2. Analyze recent form (last 5 games trend)
3. Historical performance vs this opponent
4. Defensive matchup strength
5. Contextual factors (weather, injuries, game script)

Provide your analysis in this exact JSON format:
{
  "recommendation": "OVER" | "UNDER" | "AVOID",
  "confidence": 1-5,
  "analysis": "2-3 sentence explanation of your reasoning",
  "keyFactors": [
    "Factor 1 with specific data point",
    "Factor 2 with specific data point",
    "Factor 3 with specific data point"
  ],
  "riskFactors": [
    "Risk factor 1",
    "Risk factor 2"
  ]
}

Be data-driven and specific. Cite exact numbers from the provided data.`
}

export async function analyzeWithDeepSeek(data: PropAnalysisData) {
  try {
    const prompt = buildDeepSeekPrompt(data)

    const response = await deepseek.chat.completions.create({
      model: 'deepseek-chat',
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 800,
      response_format: { type: 'json_object' }
    })

    const analysisText = response.choices[0]?.message?.content
    if (!analysisText) {
      throw new Error('No analysis returned from DeepSeek')
    }

    const analysis = JSON.parse(analysisText)

    // Validate response format
    if (!analysis.recommendation || !analysis.confidence || !analysis.analysis) {
      throw new Error('Invalid analysis format from DeepSeek')
    }

    return {
      analysis_text: analysis.analysis,
      confidence_rating: Math.min(Math.max(analysis.confidence, 1), 5),
      recommendation: analysis.recommendation,
      reasoning_points: analysis.keyFactors || [],
      risk_factors: analysis.riskFactors || [],
      cost_estimate: 0.003 // Approximate cost in USD
    }

  } catch (error) {
    console.error('DeepSeek analysis error:', error)
    throw new Error(`DeepSeek analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Fast batch analysis for multiple props
export async function batchAnalyzeWithDeepSeek(dataArray: PropAnalysisData[]) {
  const results = []

  // Process in batches of 5 to avoid rate limits
  for (let i = 0; i < dataArray.length; i += 5) {
    const batch = dataArray.slice(i, i + 5)
    const batchPromises = batch.map(data => analyzeWithDeepSeek(data))

    try {
      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)
    } catch (error) {
      console.error('Batch analysis error:', error)
      // Continue with next batch even if one fails
    }

    // Small delay between batches
    if (i + 5 < dataArray.length) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

  return results
}

// Hybrid analysis: DeepSeek + Claude for breaking news
export async function hybridAnalysis(data: PropAnalysisData, includeBreakingNews = false) {
  // Always start with DeepSeek for core analysis
  const deepseekResult = await analyzeWithDeepSeek(data)

  if (!includeBreakingNews) {
    return deepseekResult
  }

  // Use Claude for real-time research if needed
  try {
    const { analyzeWithClaude } = await import('./claude-analyzer')
    const claudeResult = await analyzeWithClaude(data)

    // Combine both analyses
    return {
      ...deepseekResult,
      analysis_text: `${deepseekResult.analysis_text}\n\nREAL-TIME UPDATE: ${claudeResult.analysis_text}`,
      reasoning_points: [...deepseekResult.reasoning_points, ...claudeResult.reasoning_points],
      confidence_rating: Math.round((deepseekResult.confidence_rating + claudeResult.confidence_rating) / 2),
      cost_estimate: deepseekResult.cost_estimate + (claudeResult.cost_estimate || 0.15)
    }
  } catch (error) {
    console.warn('Claude hybrid analysis failed, using DeepSeek only:', error)
    return deepseekResult
  }
}