'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface MonthlySummaryData {
  month: string
  currency: string
  sales: number
  refunds: number
  adjustments: number
  other: number
  gross_transactions: number
  net_transactions: number
  payout_fees: number
  gross_payouts: number
  monthly_net_activity: number
  month_end_balance: number
  sales_count: number
  payouts_count: number
  adjustments_count: number
}

export function MonthlySummary() {
  const [summary, setSummary] = useState<MonthlySummaryData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createSupabaseClient()

  useEffect(() => {
    const fetchSummary = async () => {
      try {
        const { data, error } = await supabase.rpc('get_monthly_stripe_summary')

        if (error) {
          throw new Error(`Error fetching summary: ${error.message}`)
        }

        setSummary(data || [])
      } catch (err: any) {
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchSummary()
  }, [supabase])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  if (loading) {
    return <div>Loading monthly summary...</div>
  }

  if (error) {
    return <div className="text-red-500">Error: {error}</div>
  }

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900">Monthly Financial Summary</h3>
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Net Activity</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sales</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Refunds</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payouts</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ending Balance</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {summary.map((row) => (
                <tr key={row.month} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{row.month}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{formatCurrency(row.monthly_net_activity)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{formatCurrency(row.sales)} ({row.sales_count})</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{formatCurrency(row.refunds)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{formatCurrency(row.gross_payouts)} ({row.payouts_count})</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{formatCurrency(row.month_end_balance)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {summary.length === 0 && (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">📊</div>
            <div className="text-gray-500">No summary data available.</div>
            <div className="text-sm text-gray-400 mt-2">
              Ensure the `stripe_balance_transactions` table is populated.
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
