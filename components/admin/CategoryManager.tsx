'use client'

import { useState, useEffect } from 'react'

interface CategoryStats {
  categories: Array<{
    category: string
    page_count: number
    total_views: number
  }>
  uncategorizedPages: number
  totalCategories: number
}

export default function CategoryManager() {
  const [stats, setStats] = useState<CategoryStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [recategorizing, setRecategorizing] = useState(false)
  const [message, setMessage] = useState('')

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/onlypages/admin/recategorize')
      const data = await response.json()
      setStats(data)
    } catch (error) {
      console.error('Failed to fetch stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRecategorize = async () => {
    setRecategorizing(true)
    setMessage('')

    try {
      const response = await fetch('/api/onlypages/admin/recategorize', {
        method: 'POST'
      })
      const data = await response.json()

      if (data.success) {
        setMessage(`✅ ${data.message}`)
        fetchStats() // Refresh stats
      } else {
        setMessage(`❌ ${data.error}`)
      }
    } catch (error) {
      setMessage('❌ Failed to recategorize pages')
    } finally {
      setRecategorizing(false)
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Category Management</h3>
        <button
          onClick={handleRecategorize}
          disabled={recategorizing}
          className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700
                   disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          {recategorizing ? (
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span>Recategorizing...</span>
            </div>
          ) : (
            'Recategorize All Pages'
          )}
        </button>
      </div>

      {message && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <p className="text-sm">{message}</p>
        </div>
      )}

      {stats && (
        <div className="space-y-6">
          {/* Summary Stats */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-violet-600">{stats.totalCategories}</div>
              <div className="text-sm text-gray-600">Total Categories</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {stats.categories.reduce((sum, cat) => sum + cat.page_count, 0)}
              </div>
              <div className="text-sm text-gray-600">Categorized Pages</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{stats.uncategorizedPages}</div>
              <div className="text-sm text-gray-600">Uncategorized Pages</div>
            </div>
          </div>

          {/* Category Breakdown */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Category Breakdown</h4>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {stats.categories.map((category) => (
                <div
                  key={category.category}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div>
                    <span className="font-medium text-gray-900">{category.category}</span>
                    <span className="text-sm text-gray-600 ml-2">
                      {category.page_count} pages
                    </span>
                  </div>
                  <div className="text-sm text-gray-600">
                    {category.total_views?.toLocaleString() || 0} views
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">How Auto-Categorization Works</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Analyzes page content (title, description, body) for keywords</li>
              <li>• Assigns pages to categories like Technology, Business, Arts, etc.</li>
              <li>• Updates category statistics and rankings automatically</li>
              <li>• Run recategorization after content updates or new category rules</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  )
}