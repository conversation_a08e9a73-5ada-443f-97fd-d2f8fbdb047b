'use client'

import { TemplateConfig } from '@/lib/onlypages/templates'

interface TemplateRendererProps {
  template: TemplateConfig
  article: {
    title: string
    dek?: string
    body_html: string
    pull_quote?: string
    cta_label?: string
    cta_url?: string
  }
  theme: {
    accent: string
    bg: string
    ink: string
    muted: string
    family: string
  }
  heroImageUrl?: string
  children?: React.ReactNode
}

export default function TemplateRenderer({
  template,
  article,
  theme,
  heroImageUrl,
  children
}: TemplateRendererProps) {
  const { accent, bg, ink, muted, family } = theme

  // Enhanced article body with proper formatting
  const getEnhancedBodyHtml = (bodyHtml: string) => {
    // Add proper spacing and formatting classes to the HTML
    return bodyHtml
      // Add spacing between paragraphs
      .replace(/<p>/g, '<p class="mb-6 leading-relaxed text-lg">')
      // Style headings
      .replace(/<h2>/g, '<h2 class="text-3xl font-bold mt-12 mb-6 leading-tight">')
      .replace(/<h3>/g, '<h3 class="text-2xl font-semibold mt-10 mb-4 leading-snug">')
      .replace(/<h4>/g, '<h4 class="text-xl font-medium mt-8 mb-3">')
      // Style blockquotes
      .replace(/<blockquote>/g, '<blockquote class="border-l-4 pl-6 my-8 text-xl italic font-medium" style="border-color: ' + accent + '; color: ' + muted + '">')
      // Style lists
      .replace(/<ul>/g, '<ul class="space-y-3 my-6 ml-6">')
      .replace(/<ol>/g, '<ol class="space-y-3 my-6 ml-6">')
      .replace(/<li>/g, '<li class="text-lg leading-relaxed">')
      // Style emphasis
      .replace(/<strong>/g, '<strong class="font-bold" style="color: ' + ink + '">')
      .replace(/<em>/g, '<em class="italic font-medium">')
      // Style links
      .replace(/<a /g, '<a class="font-medium underline decoration-2 hover:no-underline transition-all" style="color: ' + accent + '" ')
  }

  // Typography styles based on template
  const getTypographyStyles = () => {
    const baseFont = {
      serif: 'Georgia, "Times New Roman", serif',
      sans: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      mono: 'Monaco, "Fira Code", "Courier New", monospace',
      display: '"Inter", -apple-system, BlinkMacSystemFont, sans-serif'
    }
    return { fontFamily: baseFont[template.typography] || baseFont.serif }
  }

  // Spacing utilities based on template
  const getSpacing = () => {
    const spacingMap = {
      tight: { section: 'py-8', content: 'space-y-4', text: 'leading-snug' },
      standard: { section: 'py-12', content: 'space-y-6', text: 'leading-relaxed' },
      airy: { section: 'py-16', content: 'space-y-8', text: 'leading-loose' },
      editorial: { section: 'py-20', content: 'space-y-10', text: 'leading-relaxed' }
    }
    return spacingMap[template.spacing] || spacingMap.standard
  }

  const spacing = getSpacing()
  const typography = getTypographyStyles()

  // Render hero based on template style
  const renderHero = () => {
    const titleClasses = "font-bold leading-tight mb-6"
    const dekClasses = "leading-relaxed max-w-3xl"

    switch (template.heroStyle) {
      case 'banner':
        return (
          <div className="relative w-full bg-gradient-to-r from-gray-50 to-gray-100 mb-12 overflow-hidden">
            {heroImageUrl && (
              <div className="absolute inset-0 opacity-20">
                <img
                  src={heroImageUrl}
                  alt=""
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            <div className={`relative z-10 text-center ${spacing.section} px-6`}>
              <h1 className={`text-4xl md:text-6xl lg:text-7xl ${titleClasses}`} style={{ color: ink, ...typography }}>
                {article.title}
              </h1>
              {article.dek && (
                <p className={`text-xl md:text-2xl ${dekClasses} mx-auto mt-6`} style={{ color: muted }}>
                  {article.dek}
                </p>
              )}
            </div>
          </div>
        )

      case 'side-by-side':
        return (
          <div className={`grid md:grid-cols-2 gap-12 items-center ${spacing.section}`}>
            <div>
              <h1 className={`text-3xl md:text-5xl lg:text-6xl ${titleClasses}`} style={{ color: ink, ...typography }}>
                {article.title}
              </h1>
              {article.dek && (
                <p className={`text-lg md:text-xl ${dekClasses} mt-6`} style={{ color: muted }}>
                  {article.dek}
                </p>
              )}
            </div>
            {heroImageUrl && (
              <div className="relative">
                <img
                  src={heroImageUrl}
                  alt={article.title}
                  className="w-full h-auto rounded-lg shadow-xl"
                />
              </div>
            )}
          </div>
        )

      case 'overlay':
        return (
          <div className="relative h-screen max-h-96 md:max-h-[500px] mb-12 overflow-hidden rounded-lg">
            {heroImageUrl && (
              <img
                src={heroImageUrl}
                alt=""
                className="absolute inset-0 w-full h-full object-cover"
              />
            )}
            <div className="absolute inset-0 bg-black bg-opacity-40"></div>
            <div className="relative z-10 h-full flex items-center justify-center text-center text-white px-6">
              <div>
                <h1 className={`text-3xl md:text-5xl lg:text-6xl ${titleClasses}`} style={typography}>
                  {article.title}
                </h1>
                {article.dek && (
                  <p className={`text-lg md:text-xl ${dekClasses} mx-auto mt-6 text-gray-200`}>
                    {article.dek}
                  </p>
                )}
              </div>
            </div>
          </div>
        )

      case 'magazine':
        return (
          <div className={`${spacing.section}`}>
            <div className="text-center mb-8">
              <div className="inline-block px-4 py-2 rounded-full text-sm font-medium mb-4" style={{ backgroundColor: `${accent}15`, color: accent }}>
                {template.category.toUpperCase()}
              </div>
              <h1 className={`text-4xl md:text-6xl lg:text-7xl ${titleClasses} max-w-4xl mx-auto`} style={{ color: ink, ...typography }}>
                {article.title}
              </h1>
              {article.dek && (
                <p className={`text-xl md:text-2xl ${dekClasses} mx-auto mt-8`} style={{ color: muted }}>
                  {article.dek}
                </p>
              )}
            </div>
            {heroImageUrl && (
              <div className="max-w-4xl mx-auto">
                <img
                  src={heroImageUrl}
                  alt={article.title}
                  className="w-full h-auto rounded-lg shadow-2xl"
                />
              </div>
            )}
          </div>
        )

      default: // minimal
        return (
          <header className={`text-center ${spacing.section}`}>
            <h1 className={`text-3xl md:text-5xl lg:text-6xl ${titleClasses}`} style={{ color: ink, ...typography }}>
              {article.title}
            </h1>
            {article.dek && (
              <p className={`text-lg md:text-xl ${dekClasses} mx-auto mt-6`} style={{ color: muted }}>
                {article.dek}
              </p>
            )}
            {heroImageUrl && (
              <div className="mt-12 max-w-3xl mx-auto">
                <img
                  src={heroImageUrl}
                  alt={article.title}
                  className="w-full h-auto rounded-lg shadow-lg"
                />
              </div>
            )}
          </header>
        )
    }
  }

  // Render pull quote based on template emphasis
  const renderPullQuote = () => {
    if (!article.pull_quote) return null

    const quoteStyles = {
      visual: "text-2xl md:text-4xl font-bold text-center py-12 px-8 rounded-lg bg-gradient-to-r",
      text: "text-xl md:text-2xl font-medium border-l-4 pl-8 py-6 italic",
      balanced: "text-xl md:text-3xl font-medium text-center py-8 px-6 rounded-lg border-2",
      immersive: "text-2xl md:text-4xl font-light text-center py-16 px-4 relative"
    }

    const styleClass = quoteStyles[template.emphasis] || quoteStyles.balanced

    return (
      <div className={`max-w-4xl mx-auto my-12 ${spacing.content}`}>
        <blockquote
          className={styleClass}
          style={{
            backgroundColor: template.emphasis === 'visual' ? `${accent}10` : 'transparent',
            borderColor: accent,
            color: ink
          }}
        >
          "{article.pull_quote}"
        </blockquote>
      </div>
    )
  }

  // Render article content based on layout
  const renderContent = () => {
    const enhancedBody = getEnhancedBodyHtml(article.body_html)

    const contentClasses = `max-w-none ${spacing.content}`
    const contentStyle = {
      color: ink,
      ...typography,
      ['--tw-prose-body' as any]: ink,
      ['--tw-prose-headings' as any]: ink,
      ['--tw-prose-links' as any]: accent,
      ['--tw-prose-bold' as any]: ink,
      ['--tw-prose-quotes' as any]: accent,
    }

    switch (template.layout) {
      case 'split':
        return (
          <div className="grid md:grid-cols-3 gap-12">
            <div className="md:col-span-2">
              <article className={contentClasses} style={contentStyle}>
                <div dangerouslySetInnerHTML={{ __html: enhancedBody }} />
              </article>
            </div>
            <aside className="space-y-8">
              {renderPullQuote()}
              {children}
            </aside>
          </div>
        )

      case 'cards':
        // Split content into sections for card layout
        const sections = enhancedBody.split('<h2').filter(Boolean)
        return (
          <div className="space-y-8">
            {sections.map((section, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border p-8" style={{ borderColor: `${muted}20` }}>
                <article className={contentClasses} style={contentStyle}>
                  <div dangerouslySetInnerHTML={{ __html: index === 0 ? section : '<h2' + section }} />
                </article>
              </div>
            ))}
          </div>
        )

      case 'timeline':
        const timelineSections = enhancedBody.split('<h2').filter(Boolean)
        return (
          <div className="relative">
            <div className="absolute left-8 top-0 bottom-0 w-0.5" style={{ backgroundColor: `${accent}30` }}></div>
            <div className="space-y-12">
              {timelineSections.map((section, index) => (
                <div key={index} className="relative pl-16">
                  <div
                    className="absolute left-6 w-4 h-4 rounded-full border-4 border-white shadow-lg"
                    style={{ backgroundColor: accent }}
                  ></div>
                  <article className={contentClasses} style={contentStyle}>
                    <div dangerouslySetInnerHTML={{ __html: index === 0 ? section : '<h2' + section }} />
                  </article>
                </div>
              ))}
            </div>
          </div>
        )

      case 'showcase':
        return (
          <div className="text-center">
            <article className={`${contentClasses} max-w-4xl mx-auto`} style={contentStyle}>
              <div dangerouslySetInnerHTML={{ __html: enhancedBody }} />
            </article>
          </div>
        )

      default: // standard
        return (
          <article className={`${contentClasses} max-w-4xl mx-auto`} style={contentStyle}>
            <div dangerouslySetInnerHTML={{ __html: enhancedBody }} />
          </article>
        )
    }
  }

  return (
    <div style={{ backgroundColor: bg, color: ink }} className="min-h-screen">
      {renderHero()}
      {template.layout !== 'split' && renderPullQuote()}
      <main className={`max-w-7xl mx-auto px-4 sm:px-6 ${spacing.section}`}>
        {renderContent()}
      </main>
    </div>
  )
}