'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'

interface RelatedPage {
  id: string
  slug: string
  title: string
  dek: string
  creator_name: string
  categories: string[]
  type: string
  total_views: number
}

interface TagsAndRelatedProps {
  pageId: string
  categories: string[]
  tags: string[]
  accent: string
}

export default function OnlyPagesTagsAndRelated({ pageId, categories, tags, accent }: TagsAndRelatedProps) {
  const [relatedPages, setRelatedPages] = useState<RelatedPage[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (categories?.length > 0) {
      fetchRelatedPages()
    }
  }, [categories, pageId])

  const fetchRelatedPages = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/onlypages/search?' + new URLSearchParams({
        category: categories[0],
        limit: '6'
      }))
      const data = await response.json()

      // Filter out current page and take first 5
      const filtered = (data.pages || [])
        .filter((page: RelatedPage) => page.id !== pageId)
        .slice(0, 5)

      setRelatedPages(filtered)
    } catch (error) {
      console.error('Failed to fetch related pages:', error)
    } finally {
      setLoading(false)
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'book': return '📚'
      case 'movie': return '🎬'
      case 'album': return '🎵'
      case 'press': return '📰'
      case 'site': return '🌐'
      case 'news': return '📢'
      default: return '📄'
    }
  }

  const formatViews = (views: number) => {
    if (views >= 1000000) return `${(views / 1000000).toFixed(1)}M views`
    if (views >= 1000) return `${(views / 1000).toFixed(1)}K views`
    return `${views || 0} views`
  }

  const allTags = [...new Set([...(categories || []), ...(tags || [])])]

  return (
    <div className="space-y-8">
      {/* Tags Section */}
      {allTags.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Explore Similar Topics
          </h3>
          <div className="flex flex-wrap gap-3">
            {allTags.map((tag, index) => (
              <Link
                key={index}
                href={`/explore/category/${encodeURIComponent(tag.toLowerCase())}`}
                className="group relative"
              >
                <div
                  className="px-4 py-2 rounded-full border-2 transition-all duration-200
                           hover:shadow-lg hover:scale-105 cursor-pointer
                           bg-white border-gray-200 hover:border-violet-300
                           text-gray-700 hover:text-violet-700 font-medium"
                  style={{
                    borderColor: `${accent}40`,
                    color: accent
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = `${accent}10`
                    e.currentTarget.style.borderColor = accent
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white'
                    e.currentTarget.style.borderColor = `${accent}40`
                  }}
                >
                  #{tag}

                  {/* Hover tooltip */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2
                                opacity-0 group-hover:opacity-100 transition-opacity duration-200
                                bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                    Explore {tag} pages
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Related Pages Section */}
      {(relatedPages.length > 0 || loading) && (
        <div>
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              More Like This
            </h3>
            {categories[0] && (
              <Link
                href={`/explore/category/${encodeURIComponent(categories[0].toLowerCase())}`}
                className="text-sm font-medium text-violet-600 hover:text-violet-700"
              >
                View all {categories[0]} →
              </Link>
            )}
          </div>

          {loading ? (
            <div className="grid gap-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse bg-gray-100 rounded-lg p-4 h-24"></div>
              ))}
            </div>
          ) : (
            <div className="grid gap-4">
              {relatedPages.map((page) => (
                <Link
                  key={page.id}
                  href={`/p/${page.slug}`}
                  className="group block bg-white rounded-lg border border-gray-200 p-4
                           hover:border-violet-300 hover:shadow-lg transition-all duration-200"
                >
                  <div className="flex items-start space-x-3">
                    <span className="text-2xl">{getTypeIcon(page.type)}</span>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-900 group-hover:text-violet-700 line-clamp-1">
                        {page.title || 'Untitled'}
                      </h4>
                      <p className="text-sm text-gray-600 line-clamp-2 mt-1">
                        {page.dek}
                      </p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-gray-500">
                          by {page.creator_name || 'Anonymous'}
                        </span>
                        <span className="text-xs text-gray-400">
                          {formatViews(page.total_views)}
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}

        </div>
      )}
    </div>
  )
}