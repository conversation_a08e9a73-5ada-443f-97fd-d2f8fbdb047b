'use client'

import { useState, useEffect, useRef } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface SearchResult {
  id: string
  slug: string
  title: string
  dek: string
  creator_name: string
  categories: string[]
  type: string
  total_views: number
}

interface OnlyPagesSearchProps {
  className?: string
  placeholder?: string
  onResultClick?: (result: SearchResult) => void
}

export default function OnlyPagesSearch({
  className = '',
  placeholder = 'Search OnlyPages...',
  onResultClick
}: OnlyPagesSearchProps) {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const supabase = createSupabaseClient()

  // Search function with debouncing
  useEffect(() => {
    if (!query.trim()) {
      setResults([])
      setIsOpen(false)
      return
    }

    const timeoutId = setTimeout(async () => {
      setIsLoading(true)
      try {
        const { data, error } = await supabase
          .from('onlypages')
          .select(`
            id,
            slug,
            article,
            creator_name,
            categories,
            type,
            total_views
          `)
          .eq('status', 'published')
          .or(`
            article->>title.ilike.%${query}%,
            article->>dek.ilike.%${query}%,
            creator_name.ilike.%${query}%,
            categories.cs.{${query}}
          `)
          .order('total_views', { ascending: false })
          .limit(8)

        if (error) throw error

        const searchResults: SearchResult[] = (data || []).map(page => ({
          id: page.id,
          slug: page.slug,
          title: page.article?.title || 'Untitled',
          dek: page.article?.dek || '',
          creator_name: page.creator_name || 'Anonymous',
          categories: page.categories || [],
          type: page.type,
          total_views: page.total_views || 0
        }))

        setResults(searchResults)
        setIsOpen(searchResults.length > 0)
        setSelectedIndex(-1)
      } catch (error) {
        console.error('Search error:', error)
        setResults([])
        setIsOpen(false)
      } finally {
        setIsLoading(false)
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [query, supabase])

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          setSelectedIndex(prev => (prev < results.length - 1 ? prev + 1 : prev))
          break
        case 'ArrowUp':
          e.preventDefault()
          setSelectedIndex(prev => (prev > 0 ? prev - 1 : -1))
          break
        case 'Enter':
          e.preventDefault()
          if (selectedIndex >= 0 && results[selectedIndex]) {
            handleResultClick(results[selectedIndex])
          }
          break
        case 'Escape':
          setIsOpen(false)
          setSelectedIndex(-1)
          inputRef.current?.blur()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, selectedIndex, results])

  // Close on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSelectedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleResultClick = (result: SearchResult) => {
    setQuery('')
    setIsOpen(false)
    setSelectedIndex(-1)

    if (onResultClick) {
      onResultClick(result)
    } else {
      window.open(`/p/${result.slug}`, '_blank')
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'book': return '📚'
      case 'movie': return '🎬'
      case 'album': return '🎵'
      case 'press': return '📰'
      case 'site': return '🌐'
      case 'news': return '📢'
      default: return '📄'
    }
  }

  const formatViews = (views: number) => {
    if (views >= 1000000) return `${(views / 1000000).toFixed(1)}M`
    if (views >= 1000) return `${(views / 1000).toFixed(1)}K`
    return views.toString()
  }

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => query.trim() && results.length > 0 && setIsOpen(true)}
          placeholder={placeholder}
          className="w-full pl-10 pr-4 py-3 border-2 border-gray-200 rounded-lg
                   focus:border-violet-500 focus:ring-0 focus:outline-none
                   bg-white text-gray-900 placeholder-gray-500
                   text-base font-medium
                   transition-all duration-200"
        />
        {isLoading && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <div className="animate-spin h-4 w-4 border-2 border-violet-500 border-t-transparent rounded-full"></div>
          </div>
        )}
      </div>

      {/* Search Results Dropdown */}
      {isOpen && results.length > 0 && (
        <div className="absolute z-50 w-full mt-2 bg-white border border-gray-200 rounded-lg shadow-xl max-h-96 overflow-y-auto">
          {results.map((result, index) => (
            <div
              key={result.id}
              onClick={() => handleResultClick(result)}
              className={`px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0
                         hover:bg-gray-50 transition-colors duration-150
                         ${index === selectedIndex ? 'bg-violet-50 border-violet-200' : ''}
                       `}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-lg">{getTypeIcon(result.type)}</span>
                    <h3 className="text-sm font-semibold text-gray-900 truncate">
                      {result.title}
                    </h3>
                  </div>

                  <p className="text-xs text-gray-600 line-clamp-2 mb-2">
                    {result.dek}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-500">by {result.creator_name}</span>
                      {result.categories.length > 0 && (
                        <span className="text-xs text-violet-600 bg-violet-100 px-2 py-1 rounded-full">
                          {result.categories[0]}
                        </span>
                      )}
                    </div>
                    <span className="text-xs text-gray-400">
                      {formatViews(result.total_views)} views
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {/* View All Results Link */}
          {results.length >= 8 && (
            <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
              <button
                onClick={() => {
                  setIsOpen(false)
                  window.open(`/explore?q=${encodeURIComponent(query)}`, '_blank')
                }}
                className="w-full text-center text-sm text-violet-600 hover:text-violet-700 font-medium"
              >
                View all results for "{query}"
              </button>
            </div>
          )}
        </div>
      )}

      {/* No Results */}
      {isOpen && !isLoading && query.trim() && results.length === 0 && (
        <div className="absolute z-50 w-full mt-2 bg-white border border-gray-200 rounded-lg shadow-xl">
          <div className="px-4 py-6 text-center">
            <div className="text-gray-400 mb-2">
              <svg className="mx-auto h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33" />
              </svg>
            </div>
            <p className="text-sm text-gray-600">No pages found for "{query}"</p>
            <p className="text-xs text-gray-400 mt-1">Try searching for creator names, topics, or keywords</p>
          </div>
        </div>
      )}
    </div>
  )
}