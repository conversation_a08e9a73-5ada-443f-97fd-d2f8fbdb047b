'use client'

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { SubscribeButton } from "@/components/SubscribeButton"
import { FollowButton } from "@/components/FollowButton"
import { MailingListButton } from "@/components/MailingListButton"
import { PaywallContent } from "@/components/PaywallContent"
import { StoryMap } from "@/components/StoryMap"
import { VideoThumbnail } from "@/components/VideoThumbnail"
import { createSupabaseClient } from "@/lib/supabase/client"
import RecommendationModal from "@/components/RecommendationModal"
import { WriteMessageModal } from "@/components/WriteMessageModal"
import { AudioPost } from "@/components/AudioPost"
import { Day1Badge } from "@/components/Day1Badge"
import { ShareButton } from "@/components/ShareButton"
import { CompactCommentsSection } from "@/components/CompactCommentsSection"
import { CompactBookCommentsSection } from "@/components/CompactBookCommentsSection"
import { DuoCompactCommentsSection } from "@/components/DuoCompactCommentsSection"
import { ReactionSystem } from "@/components/ReactionSystem"
import { DuoVideoPlayer } from "@/components/duo/DuoVideoPlayer"
import { DuoTimelineCard } from "@/components/timeline/DuoTimelineCard"
import { formatDistanceToNow } from "date-fns"
import { NavigationButton } from "@/components/NavigationButton"
import { ProfileRecipesSection } from "@/components/ProfileRecipesSection"


interface DiaryEntry {
  id: string
  title: string
  body_md: string
  is_free: boolean
  created_at: string
  view_count?: number
  photos?: Array<{ id: string; url: string; alt_text: string }>
  videos?: Array<{ id: string; r2_public_url: string; title: string; view_count: number; custom_thumbnail_url?: string }>
  reactions?: Record<string, number>
  userReaction?: string | null
}

interface Project {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  is_complete: boolean
  price_type: 'project' | 'chapters'
  price_amount: number
  total_chapters: number
  total_words: number
  sales_count?: number
  bestseller_rank?: number
  created_at: string
}

interface AudioPost {
  id: string
  audio_url: string
  description?: string
  duration_seconds: number
  love_count: number
  reply_count: number
  created_at: string
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
  }
}

interface RecipeItem {
  id: string
  title: string
  description?: string
  cover_photo_url?: string
  is_free: boolean
  created_at: string
}


interface Duo {
  id: string
  created_at: string
  final_video_url?: string
  final_video_r2_key?: string
  initiator: {
    name: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
  }
  responder: {
    name: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
  }
  partA: {
    src_r2_key: string
    hls_manifest?: string
  }
  partB: {
    src_r2_key: string
    hls_manifest?: string
  }
  reactions_count: number
  comments_count: number
  downloads_count: number
  view_count?: number
  reactions?: Record<string, number>
  userReaction?: string | null
}

interface UnifiedProfileClientProps {
  user: any
  diaryEntries: DiaryEntry[]
  projects: Project[]
  audioPosts: AudioPost[]
  duos?: Duo[]
  recipes?: RecipeItem[]
  hasActiveSubscription: boolean
  isFollowing: boolean
  isOwnProfile: boolean
  currentUserId?: string
}

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

function formatDate(dateString: string) {
  // Use a simple, consistent format to avoid hydration mismatches
  const date = new Date(dateString)
  const months = ['January', 'February', 'March', 'April', 'May', 'June',
                  'July', 'August', 'September', 'October', 'November', 'December']

  return `${months[date.getUTCMonth()]} ${date.getUTCDate()}, ${date.getUTCFullYear()}`
}

function formatViewCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}k`
  }
  return count.toString()
}

// Helper function to format large numbers
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K'
  }
  return num.toString()
}

export function UnifiedProfileClient({
  user,
  diaryEntries,
  projects,
  audioPosts,
  duos = [],
  recipes = [],
  hasActiveSubscription,
  isFollowing,
  isOwnProfile,
  currentUserId
}: UnifiedProfileClientProps) {
  const [activeTab, setActiveTab] = useState<'all' | 'diary' | 'books' | 'audio' | 'duos' | 'recipes'>('all')
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'popular'>('newest')
  const [loading, setLoading] = useState(false)
  const [recommendedCreators, setRecommendedCreators] = useState<any[]>([])
  const [entriesWithPhotos, setEntriesWithPhotos] = useState<DiaryEntry[]>(diaryEntries)
  const [showRecommendationModal, setShowRecommendationModal] = useState(false)
  const [showWriteMessageModal, setShowWriteMessageModal] = useState(false)
  const supabase = createSupabaseClient()
  const router = useRouter()

  // Load photos, videos, and reactions for diary entries
  useEffect(() => {
    const loadMediaAndReactionData = async () => {
      const entriesWithAllData = await Promise.all(
        diaryEntries.map(async (entry) => {
          // Load photos
          const { data: photos } = await supabase
            .from('photos')
            .select('id, url, alt_text')
            .eq('diary_entry_id', entry.id)
            .eq('moderation_status', 'approved')
            .order('created_at', { ascending: true })

          // Load videos
          const { data: videos, error: videoError } = await supabase
            .from('videos')
            .select('id, r2_public_url, title, view_count, custom_thumbnail_url')
            .eq('post_id', entry.id)
            .order('created_at', { ascending: true })

          if (videoError) {
            console.error('Error loading videos for entry', entry.id, ':', videoError)
          }

          // Load reaction counts
          const { data: reactionCounts } = await supabase
            .rpc('get_reaction_counts', { entry_id: entry.id })

          // Load user's reaction if logged in
          const { data: userReactionData } = currentUserId ? await supabase
            .from('reactions')
            .select('reaction_type')
            .eq('diary_entry_id', entry.id)
            .eq('user_id', currentUserId)
            .single() : { data: null }

          // Convert reaction counts to object
          const reactions = reactionCounts?.reduce((acc: Record<string, number>, item: any) => {
            acc[item.reaction_type] = item.count
            return acc
          }, {}) || {}

          const entryWithAllData = {
            ...entry,
            photos: photos || [],
            videos: videos || [],
            reactions,
            userReaction: userReactionData?.reaction_type || null
          }

          // Debug logging for videos
          if (videos && videos.length > 0) {
            console.log(`Entry ${entry.id} has ${videos.length} videos:`, videos)
          }

          return entryWithAllData
        })
      )

      setEntriesWithPhotos(entriesWithAllData)
    }

    loadMediaAndReactionData()
  }, [diaryEntries, supabase, currentUserId])

  // Check if user has monetization set up
  const hasMonetizationSetup = user?.price_monthly && user?.stripe_account_id

  // Fetch favorite creators (user's actual recommendations)
  useEffect(() => {
    const fetchFavoriteCreators = async () => {
      try {
        const { data, error } = await supabase
          .from('favorite_creators')
          .select(`
            writer_id,
            users!favorite_creators_writer_id_fkey (
              id, name, bio, profile_picture_url, avatar, subscriber_count, entry_count
            )
          `)
          .eq('user_id', user.id)
          .limit(4)

        if (data && !error) {
          const creators = data.map(item => item.users).filter(Boolean)
          setRecommendedCreators(creators)
        }
      } catch (error) {
        console.error('Error fetching favorite creators:', error)
      }
    }

    if (user?.id) {
      fetchFavoriteCreators()
    }
  }, [user?.id, supabase, showRecommendationModal])

  // Safety checks
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">😕</div>
          <h2 className="text-xl font-serif text-gray-800 mb-2">Profile not found</h2>
          <p className="text-gray-600">This user doesn't exist or their profile is private.</p>
        </div>
      </div>
    )
  }

  return (
    <>
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">


        {/* Profile Header */}
        <div className="bg-white rounded-xl shadow-sm p-8 mb-6 relative">
            {/* Edit Profile Button - Top Right for Own Profile */}
            {isOwnProfile && (
            <Link
              href="/profile/edit"
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-sm font-medium transition-colors flex items-center gap-1"
            >
              ⚙️ Edit
            </Link>
          )}

          <div className="flex flex-col lg:flex-row gap-8">

            {/* Avatar */}
            <div className="flex-shrink-0 mx-auto lg:mx-0">
              <div className="w-32 h-32 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center overflow-hidden border-4 border-white shadow-lg">
                {user.profile_picture_url || user.avatar ? (
                  <img
                    src={user.profile_picture_url || user.avatar}
                    alt={user.name || 'Profile'}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                ) : (
                  <span className="text-4xl font-serif text-gray-600">
                    {user.name?.charAt(0).toUpperCase() || user.email?.charAt(0).toUpperCase() || 'U'}
                  </span>
                )}
              </div>
            </div>

            {/* Profile Info & Actions */}
            <div className="flex-1 text-center lg:text-left">

              {/* Name & Bio */}
              <div className="mb-6">
                <div className="flex items-center justify-center lg:justify-start gap-2 mb-3 flex-wrap">
                  <h1 className="text-2xl sm:text-3xl font-serif text-gray-800 text-center lg:text-left">
                    {user.name || user.email || 'Anonymous User'}
                  </h1>
                  {user.has_day1_badge && (
                    <Day1Badge
                      signupNumber={user.signup_number}
                      badgeTier={user.badge_tier}
                      size="sm"
                      className="flex-shrink-0"
                    />
                  )}
                </div>
                {user.bio && (
                  <p className="text-gray-600 text-lg leading-relaxed max-w-2xl">
                    {user.bio}
                  </p>
                )}
              </div>

              {/* Stats - Responsive Design */}
              <div className="flex justify-center lg:justify-start mb-6">
                {/* Mobile: Show only 3 most important stats */}
                <div className="flex items-center gap-2 sm:gap-6 sm:hidden">
                  <Link href={`/u/${user.id}/followers`} className="text-center group">
                    <div className="text-base sm:text-xl font-bold text-gray-800 group-hover:text-blue-600 transition-colors">{formatNumber(user.follower_count || 0)}</div>
                    <div className="text-xs text-gray-500">Followers</div>
                  </Link>
                  <div className="w-px h-6 sm:h-10 bg-gray-200"></div>
                  <div className="text-center">
                    <div className="text-base sm:text-xl font-bold text-gray-800">{formatNumber(diaryEntries.length + audioPosts.length + projects.length + duos.length)}</div>
                    <div className="text-xs text-gray-500">Posts</div>
                  </div>
                  <div className="w-px h-6 sm:h-10 bg-gray-200"></div>
                  <div className="text-center">
                    <div className="text-base sm:text-xl font-bold text-gray-800">
                      {formatNumber(
                        diaryEntries.reduce((total, entry) => {
                          return total + (entry.body_md?.split(' ').length || 0)
                        }, 0) + audioPosts.reduce((total, post) => {
                          return total + (post.description?.split(' ').length || 0)
                        }, 0) + projects.reduce((total, project) => {
                          return total + (project.total_words || 0)
                        }, 0)
                      )}
                    </div>
                    <div className="text-xs text-gray-500">Words</div>
                  </div>
                </div>

                {/* Desktop: Show all stats */}
                <div className="hidden sm:flex items-center gap-6">
                  <Link href={`/u/${user.id}/followers`} className="text-center group">
                    <div className="text-xl font-bold text-gray-800 group-hover:text-blue-600 transition-colors">{formatNumber(user.follower_count || 0)}</div>
                    <div className="text-sm text-gray-500">Followers</div>
                  </Link>
                  <div className="w-px h-10 bg-gray-200"></div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-gray-800">{formatNumber(diaryEntries.length)}</div>
                    <div className="text-sm text-gray-500">Entries</div>
                  </div>
                  <div className="w-px h-10 bg-gray-200"></div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-gray-800">{formatNumber(projects.length)}</div>
                    <div className="text-sm text-gray-500">Books</div>
                  </div>
                  <div className="w-px h-10 bg-gray-200"></div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-gray-800">{formatNumber(audioPosts.length)}</div>
                    <div className="text-sm text-gray-500">Audio</div>
                  </div>
                  <div className="w-px h-10 bg-gray-200"></div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-gray-800">
                      {formatNumber(
                        activeTab === 'all'
                          ? diaryEntries.reduce((total, entry) => {
                              return total + (entry.body_md?.split(' ').length || 0)
                            }, 0) + audioPosts.reduce((total, post) => {
                              return total + (post.description?.split(' ').length || 0)
                            }, 0) + projects.reduce((total, project) => {
                              return total + (project.total_words || 0)
                            }, 0)
                          : activeTab === 'diary'
                          ? diaryEntries.reduce((total, entry) => {
                              return total + (entry.body_md?.split(' ').length || 0)
                            }, 0)
                          : activeTab === 'books'
                          ? projects.reduce((total, project) => {
                              return total + (project.total_words || 0)
                            }, 0)
                          : audioPosts.reduce((total, post) => {
                              return total + (post.description?.split(' ').length || 0)
                            }, 0)
                      )}
                    </div>
                    <div className="text-sm text-gray-500">{activeTab === 'audio' ? 'Description Words' : 'Words'}</div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                {isOwnProfile ? (
                  // Own Profile Actions
                  <>
                    {/* Primary Create Actions */}
                    <div className="flex flex-col sm:flex-row gap-3 justify-center lg:justify-start">
                      <NavigationButton
                        href="/write"
                        className="bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center"
                      >
                        ✍️ Write Entry
                      </NavigationButton>
                      <NavigationButton
                        href="/publishing"
                        className="bg-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-purple-700 transition-colors text-center"
                      >
                        📚 Publish Book
                      </NavigationButton>
                    </div>
                  </>
                ) : (
                  // Visitor Actions - Follow is primary
                  <>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center lg:justify-start w-full">
                      <FollowButton
                        writerId={user.id}
                        writerName={user.name}
                        initialIsFollowing={isFollowing}
                      />

                      <button
                        onClick={() => setShowWriteMessageModal(true)}
                        className="bg-amber-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-amber-700 transition-colors text-center flex items-center justify-center gap-2 text-sm w-full sm:w-auto min-h-[44px] relative z-10"
                        style={{
                          color: 'white !important',
                          backgroundColor: '#d97706 !important',
                          border: 'none',
                          outline: 'none'
                        }}
                      >
                        <span className="text-lg" style={{ color: 'white !important' }}>✉️</span>
                        <span className="whitespace-nowrap font-medium" style={{ color: 'white !important' }}>Write Me</span>
                      </button>

                      <MailingListButton
                        creatorId={user.id}
                        creatorName={user.name}
                        customUrl={user.custom_url}
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
            </div>
          </div>

        {/* Content Tabs */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">

          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div className="flex">
                <button
                  onClick={() => setActiveTab('all')}
                  className={`flex-1 py-4 px-2 sm:px-3 text-center font-medium transition-colors ${
                    activeTab === 'all'
                      ? 'text-green-600 border-b-2 border-green-600 bg-green-50'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <span className="block sm:inline">📋</span>
                  <span className="block sm:inline sm:ml-1 text-xs sm:text-sm">All ({diaryEntries.length + audioPosts.length + projects.length + duos.length})</span>
                </button>
                <button
                  onClick={() => setActiveTab('diary')}
                  className={`flex-1 py-4 px-2 sm:px-3 text-center font-medium transition-colors ${
                    activeTab === 'diary'
                      ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <span className="block sm:inline">📖</span>
                  <span className="block sm:inline sm:ml-1 text-xs sm:text-sm">Diary ({diaryEntries.length})</span>
                </button>
                <button
                  onClick={() => setActiveTab('books')}
                  className={`flex-1 py-4 px-2 sm:px-3 text-center font-medium transition-colors ${
                    activeTab === 'books'
                      ? 'text-purple-600 border-b-2 border-purple-600 bg-purple-50'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <span className="block sm:inline">📚</span>
                  <span className="block sm:inline sm:ml-1 text-xs sm:text-sm">Books ({projects.length})</span>
                </button>
                <button
                  onClick={() => setActiveTab('recipes')}
                  className={`flex-1 py-4 px-2 sm:px-3 text-center font-medium transition-colors ${
                    activeTab === 'recipes'
                      ? 'text-green-600 border-b-2 border-green-600 bg-green-50'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <span className="block sm:inline">🍳</span>
                  <span className="block sm:inline sm:ml-1 text-xs sm:text-sm">Recipes ({recipes.length})</span>
                </button>
                <button
                  onClick={() => setActiveTab('audio')}
                  className={`flex-1 py-4 px-2 sm:px-3 text-center font-medium transition-colors ${
                    activeTab === 'audio'
                      ? 'text-orange-600 border-b-2 border-orange-600 bg-orange-50'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <span className="block sm:inline">🎵</span>
                  <span className="block sm:inline sm:ml-1 text-xs sm:text-sm">Audio ({audioPosts.length})</span>
                </button>
                <button
                  onClick={() => setActiveTab('duos')}
                  className={`flex-1 py-4 px-2 sm:px-3 text-center font-medium transition-colors ${
                    activeTab === 'duos'
                      ? 'text-pink-600 border-b-2 border-pink-600 bg-pink-50'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <span className="block sm:inline">🎭</span>
                  <span className="block sm:inline sm:ml-1 text-xs sm:text-sm">Duos ({duos.length})</span>
                </button>
              </div>

              {/* Sort Controls */}
              <div className="flex items-center gap-2 px-4 py-2 sm:py-0">
                <span className="text-sm text-gray-500">Sort:</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'newest' | 'oldest' | 'popular')}
                  className="text-sm border border-gray-300 rounded px-2 py-1 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="newest">Newest</option>
                  <option value="oldest">Oldest</option>
                  <option value="popular">Popular</option>
                </select>
              </div>
            </div>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'all' && (
              <UnifiedTimelineSection
                diaryEntries={entriesWithPhotos}
                audioPosts={audioPosts}
                projects={projects}
                duos={duos}
                hasActiveSubscription={hasActiveSubscription}
                writerName={user.name}
                writerId={user.id}
                isOwnProfile={isOwnProfile}
                user={user}
                currentUserId={currentUserId}
                sortBy={sortBy}
                router={router}
              />
            )}

            {activeTab === 'diary' && (
              <DiaryEntriesSection
                entries={entriesWithPhotos}
                hasActiveSubscription={hasActiveSubscription}
                writerName={user.name}
                writerId={user.id}
                isOwnProfile={isOwnProfile}
                user={user}
                sortBy={sortBy}
                currentUserId={currentUserId}
              />
            )}

            {activeTab === 'recipes' && (
              <ProfileRecipesSection
                recipes={recipes}
                writerName={user.name}
              />
            )}

            {activeTab === 'books' && (
              <BookProjectsSection
                projects={projects}
                writerName={user.name}
                isOwnProfile={isOwnProfile}
                sortBy={sortBy}
                router={router}
              />
            )}

            {activeTab === 'audio' && (
              <AudioPostsSection
                audioPosts={audioPosts}
                writerName={user.name}
                isOwnProfile={isOwnProfile}
                currentUserId={currentUserId}
                sortBy={sortBy}
              />
            )}

            {activeTab === 'duos' && (
              <DuosSection
                duos={duos}
                writerName={user.name}
                isOwnProfile={isOwnProfile}
                currentUserId={currentUserId}
                sortBy={sortBy}
              />
            )}
          </div>
        </div>

        {/* Recommended Creators Section */}
        {isOwnProfile && (
          <div className="bg-white rounded-xl shadow-sm p-6 mt-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-serif text-gray-800">Recommended Creators</h3>
              <button
                onClick={() => setShowRecommendationModal(true)}
                className="text-sm text-blue-600 hover:text-blue-700 font-medium"
              >
                {recommendedCreators.length > 0 ? 'Edit' : 'Add Recommendations'}
              </button>
            </div>

            {recommendedCreators.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {recommendedCreators.map((creator) => (
                <div key={creator.id} className="bg-gray-50 rounded-lg p-4 text-center">
                  {/* Avatar */}
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center mx-auto mb-3 overflow-hidden">
                    {creator.profile_picture_url || creator.avatar ? (
                      <img
                        src={creator.profile_picture_url || creator.avatar}
                        alt={creator.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-lg font-serif text-gray-600">
                        {creator.name?.charAt(0).toUpperCase() || '?'}
                      </span>
                    )}
                  </div>

                  {/* Name */}
                  <Link href={`/u/${creator.id}`}>
                    <h4 className="font-medium text-gray-800 mb-2 truncate hover:text-blue-600 transition-colors">
                      {creator.name}
                    </h4>
                  </Link>

                  {/* Bio - 5 words max */}
                  <p className="text-xs text-gray-600 text-center mb-3 h-8 flex items-center justify-center">
                    {creator.bio ?
                      `${creator.bio.split(' ').slice(0, 5).join(' ')}${creator.bio.split(' ').length > 5 ? '...' : ''}`
                      : 'No bio'
                    }
                  </p>

                  {/* Follow Button */}
                  <FollowButton
                    writerId={creator.id}
                    writerName={creator.name}
                    initialIsFollowing={false}
                  />
                </div>
              ))}
            </div>
            ) : (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">⭐</span>
                </div>
                <h4 className="text-gray-800 font-medium mb-2">No Recommended Creators Yet</h4>
                <p className="text-gray-500 text-sm mb-4">
                  Showcase creators you love and want to recommend to your readers
                </p>
                <button
                  onClick={() => setShowRecommendationModal(true)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm"
                >
                  Add Recommendations
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>

    {/* Recommendation Modal */}
    {showRecommendationModal && (
      <RecommendationModal
        onClose={() => {
          setShowRecommendationModal(false)
          // The useEffect will automatically refresh when showRecommendationModal changes
        }}
      />
    )}

    {/* Write Message Modal */}
    {showWriteMessageModal && (
      <WriteMessageModal
        isOpen={showWriteMessageModal}
        onClose={() => setShowWriteMessageModal(false)}
        recipientId={user.id}
        recipientName={user.name || 'this writer'}
      />
    )}
  </>
  )
}

// Diary Entries Section Component - Uniform Grid Layout
function DiaryEntriesSection({
  entries,
  hasActiveSubscription,
  writerName,
  writerId,
  isOwnProfile,
  user,
  sortBy,
  currentUserId
}: {
  entries: DiaryEntry[]
  hasActiveSubscription: boolean
  writerName: string
  writerId: string
  isOwnProfile: boolean
  user: any
  sortBy: 'newest' | 'oldest' | 'popular'
  currentUserId?: string
}) {
  if (entries.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
        <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">📔</span>
        </div>
        <h3 className="text-xl font-serif text-gray-800 mb-3">No Diary Entries Yet</h3>
        <p className="text-gray-600 font-serif">
          {writerName} hasn't published any diary entries yet. Check back soon!
        </p>
      </div>
    )
  }

  // Sort entries based on sortBy parameter
  const sortedEntries = [...entries].sort((a, b) => {
    switch (sortBy) {
      case 'oldest':
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      case 'popular':
        return (b.view_count || 0) - (a.view_count || 0)
      case 'newest':
      default:
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    }
  })

  // Separate pinned and regular entries
  const pinnedEntries = sortedEntries.filter((entry: any) => entry.is_pinned === true)
  const regularEntries = sortedEntries.filter((entry: any) => entry.is_pinned !== true)

  return (
    <div className="space-y-8">
      {/* Pinned Entry - Featured */}
      {pinnedEntries.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
            <span className="text-yellow-500">📌</span>
            Start Here
          </h3>
          <DiaryEntryCard
            entry={pinnedEntries[0]}
            hasAccess={pinnedEntries[0].is_free || hasActiveSubscription || isOwnProfile}
            featured={true}
            user={user}
            currentUserId={currentUserId}
          />
        </div>
      )}

      {/* Beautiful Entries Grid - Spacious & Elegant */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8" style={{ gridAutoRows: '1fr' }}>
        {regularEntries.map((entry) => (
          <DiaryEntryCard
            key={entry.id}
            entry={entry}
            hasAccess={entry.is_free || hasActiveSubscription || isOwnProfile}
            featured={false}
            user={user}
            currentUserId={currentUserId}
          />
        ))}
      </div>
    </div>
  )
}

// Individual Diary Entry Card Component
function DiaryEntryCard({
  entry,
  hasAccess,
  featured = false,
  user,
  currentUserId
}: {
  entry: DiaryEntry
  hasAccess: boolean
  featured?: boolean
  user: any
  currentUserId?: string
}) {
  // Get preview text (first 150 characters for better preview)
  const cleanText = entry.body_md
    .replace(/[#*`_~]/g, '') // Remove markdown formatting
    .replace(/\n/g, ' ') // Replace newlines with spaces

  const previewText = cleanText.substring(0, featured ? 200 : 150).trim()
  const wordCount = cleanText.split(/\s+/).length
  const previewWords = previewText.split(/\s+/).length
  const remainingWords = wordCount - previewWords
  const readTime = Math.ceil(wordCount / 200)
  const firstPhoto = entry.photos?.[0]
  const firstVideo = entry.videos?.[0]
  const totalViews = (entry.view_count || 0) + (entry.videos?.reduce((sum, video) => sum + (video.view_count || 0), 0) || 0)

  return (
    <Link href={`/d/${entry.id}`} className="group block h-full">
      <article className={`bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 border-0 group-hover:scale-[1.03] h-full flex flex-col relative ${
        featured ? 'sm:col-span-2 lg:col-span-3 shadow-xl' : ''
      }`} style={{
        background: 'linear-gradient(145deg, #ffffff 0%, #fefefe 100%)',
        boxShadow: '0 10px 40px -10px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.05)'
      }}>

        {/* Stunning Image/Video Section */}
        <div className="relative aspect-[4/3] overflow-hidden">
          {/* Beautiful gradient background */}
          <div className="absolute inset-0 bg-gradient-to-br from-violet-100 via-purple-50 to-indigo-100"></div>

          {firstVideo ? (
            <VideoThumbnail
              videoUrl={firstVideo.r2_public_url}
              customThumbnailUrl={firstVideo.custom_thumbnail_url}
              alt={entry.title}
              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
              timeInSeconds={1}
              showPlayButton={true}
              playButtonSize="md"
            />
          ) : firstPhoto ? (
            <img
              src={firstPhoto.url}
              alt={firstPhoto.alt_text || entry.title}
              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center relative">
              {/* Animated gradient background */}
              <div className="absolute inset-0 bg-gradient-to-br from-violet-200/40 via-purple-200/40 to-indigo-200/40 animate-pulse"></div>

              {/* Floating icon */}
              <div className="relative">
                <div className="bg-white/90 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-white/50 group-hover:scale-110 transition-transform duration-500">
                  <span className="text-4xl opacity-70 block">✨</span>
                </div>
              </div>

              {/* Subtle pattern overlay */}
              <div className="absolute inset-0 opacity-5" style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
              }}></div>
            </div>
          )}

          {/* Premium Status Badges */}
          <div className="absolute top-4 right-4 flex gap-2">
            {entry.is_free ? (
              <div className="bg-emerald-500/95 backdrop-blur-md text-white text-xs px-3 py-1.5 rounded-full font-semibold shadow-lg border border-emerald-400/30">
                <span className="mr-1">🎁</span>FREE
              </div>
            ) : (
              <div className="bg-gradient-to-r from-purple-500 to-violet-600 backdrop-blur-md text-white text-xs px-3 py-1.5 rounded-full font-semibold shadow-lg border border-purple-400/30">
                <span className="mr-1">💎</span>${((user as any)?.price_monthly / 100)?.toFixed(2)}/mo
              </div>
            )}
            {(entry as any).is_pinned === true && (
              <div className="bg-gradient-to-r from-amber-500 to-orange-500 backdrop-blur-md text-white text-xs px-3 py-1.5 rounded-full font-semibold shadow-lg border border-amber-400/30">
                📌 PINNED
              </div>
            )}
          </div>

          {/* Elegant Lock Overlay */}
          {!hasAccess && (
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-black/20 flex items-center justify-center backdrop-blur-sm">
              <div className="bg-white/95 backdrop-blur-md rounded-2xl p-4 shadow-2xl border border-white/60 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-gray-700" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          )}

          {/* Stylish Reading Time */}
          <div className="absolute bottom-4 left-4">
            <div className="bg-black/70 backdrop-blur-md text-white text-xs px-3 py-1.5 rounded-full font-medium shadow-lg">
              <span className="mr-1">⏱️</span>{readTime} min read
            </div>
          </div>
        </div>

        {/* Luxurious Content Section */}
        <div className={`p-6 ${featured ? 'sm:p-8' : ''} flex-1 flex flex-col relative`}>
          {/* Elegant Header */}
          <div className="flex items-start justify-between mb-4">
            <h3 className={`font-serif text-gray-900 line-clamp-2 group-hover:text-violet-700 transition-colors duration-300 leading-tight ${
              featured ? 'text-2xl sm:text-3xl md:text-4xl font-light' : 'text-lg font-medium'
            }`} title={entry.title}>
              {entry.title}
            </h3>
            <time className="text-xs text-gray-400 ml-4 flex-shrink-0 font-medium bg-gray-50 px-2 py-1 rounded-full">
              {formatDate(entry.created_at).split(',')[0]}
            </time>
          </div>

          {/* Beautiful Description */}
          <p className={`text-gray-600 line-clamp-3 mb-auto leading-relaxed font-light ${
            featured ? 'text-lg' : 'text-base'
          }`} style={{ lineHeight: '1.6' }}>
            {previewText}
            {entry.body_md.length > (featured ? 200 : 150) && (
              <span className="text-violet-600 font-medium ml-1">
                ... (+{remainingWords} more words)
              </span>
            )}
          </p>

          {/* Sophisticated Stats and Actions */}
          <div className="mt-6 space-y-4">
            {/* Elegant Stats Row */}
            <div className="flex items-center justify-center gap-6 text-sm text-gray-500 bg-gray-50/50 rounded-2xl py-3 px-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-violet-400 rounded-full"></div>
                <span className="font-medium">
                  {wordCount > 1000 ? `${Math.round(wordCount/1000)}k` : wordCount} words
                </span>
              </div>

              {totalViews > 0 && (
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <span className="font-medium">{formatViewCount(totalViews)} views</span>
                </div>
              )}

              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                <span className="font-medium">{readTime} min</span>
              </div>
            </div>

            {/* Reaction System */}
            {hasAccess && (
              <div className="flex justify-center py-2">
                <ReactionSystem
                  contentId={entry.id}
                  contentType="diary"
                  currentUserId={currentUserId}
                  initialReactions={entry.reactions || {}}
                  userReaction={entry.userReaction || null}
                />
              </div>
            )}

            {/* Stunning Read Button */}
            <button className={`w-full bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 hover:from-violet-700 hover:via-purple-700 hover:to-indigo-700 text-white py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 relative overflow-hidden group/btn ${
              featured ? 'text-lg py-5' : 'text-base'
            }`}>
              {/* Button shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover/btn:translate-x-full transition-transform duration-1000"></div>

              <span className="flex items-center justify-center gap-3 relative z-10">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                Read Story
                <svg className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </span>
            </button>
          </div>
        </div>
      </article>
    </Link>
  )
}

// Book Projects Section Component
function BookProjectsSection({
  projects,
  writerName,
  isOwnProfile,
  sortBy,
  router
}: {
  projects: Project[]
  writerName: string
  isOwnProfile: boolean
  sortBy: 'newest' | 'oldest' | 'popular'
  router: any
}) {
  if (projects.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
        <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">📚</span>
        </div>
        <h3 className="text-xl font-serif text-gray-800 mb-3">No Book Projects Yet</h3>
        <p className="text-gray-600 font-serif">
          {isOwnProfile ? "Ready to publish your first book?" : `${writerName} hasn't published any book projects yet. Check back soon!`}
        </p>
      </div>
    )
  }

  // Sort projects based on sortBy parameter
  const sortedProjects = [...projects].sort((a, b) => {
    switch (sortBy) {
      case 'oldest':
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      case 'popular':
        // For books, we can sort by total_words as a proxy for popularity
        return (b.total_words || 0) - (a.total_words || 0)
      case 'newest':
      default:
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    }
  })

  return (
    <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4" style={{ gridAutoRows: '1fr' }}>
      {sortedProjects.map((project) => (
        <Link
          key={project.id}
          href={`/projects/${project.id}`}
          className="group block h-full"
        >
          <div className="bg-white rounded-lg sm:rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100 group-hover:scale-[1.01] h-full flex flex-col">

            {/* Book Cover - Full visibility with proper aspect ratio */}
            <div className="aspect-[10/16] bg-gradient-to-br from-blue-100 to-purple-100 relative overflow-hidden">
              {project.cover_image_url ? (
                <img
                  src={project.cover_image_url}
                  alt={project.title}
                  className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-300"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <span className="text-4xl opacity-50">📖</span>
                </div>
              )}

              {/* Status Badge */}
              {project.is_complete && (
                <div className="absolute top-1.5 right-1.5">
                  <span className="bg-green-500/90 text-white text-xs px-1.5 py-0.5 rounded-full font-medium">
                    Complete
                  </span>
                </div>
              )}
            </div>

            {/* Project Info - Compact */}
            <div className="p-3 flex-1 flex flex-col">
              {/* Mobile: Price where author name was */}
              <div className="sm:hidden mb-1.5">
                <span className="text-sm font-semibold text-blue-600">
                  {project.price_amount === 0 ? 'Free' : `$${(project.price_amount / 100).toFixed(2)}`}
                </span>
              </div>

              {/* Title */}
              <h3 className="font-serif text-sm font-semibold text-gray-800 mb-1 line-clamp-1 group-hover:text-blue-600 transition-colors" title={project.title}>
                {project.title}
              </h3>

              {/* Author under title */}
              <div className="mb-2">
                <span className="text-xs text-gray-600">by {writerName}</span>
              </div>

              {/* Synopsis - Extended space */}
              <p className="text-xs text-gray-600 mb-auto line-clamp-3 flex-1">
                {project.description || "This book is a work in progress and does not yet have a description"}
              </p>

              <div className="mt-3">
                {/* Stats Row */}
                <div className="flex items-center gap-2 text-xs text-gray-500 mb-2 flex-wrap">
                  {(project.sales_count && project.sales_count > 0) && (
                    <>
                      <span>{project.sales_count} readers</span>
                      <span>•</span>
                    </>
                  )}
                  <span>{project.total_words > 1000 ? `${Math.round(project.total_words/1000)}k` : project.total_words} words</span>
                  {project.genre && (
                    <>
                      <span>•</span>
                      <span>{project.genre}</span>
                    </>
                  )}
                  {project.bestseller_rank && (
                    <>
                      <span>•</span>
                      <span className="text-purple-600">#{project.bestseller_rank}</span>
                    </>
                  )}
                </div>

                {/* Desktop: Price */}
                <div className="hidden sm:flex items-center justify-between mb-3">
                  <div className="text-xs font-medium text-blue-600">
                    {project.price_amount === 0 ? 'Free' : `$${(project.price_amount / 100).toFixed(2)}`}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-2">
                  <button
                    onClick={(e) => {
                      e.preventDefault()
                      router.push(`/books/${project.id}`)
                    }}
                    className="w-full bg-gray-100 text-gray-700 py-2 rounded-md font-medium hover:bg-gray-200 transition-colors text-xs"
                  >
                    View Details
                  </button>
                  <button
                    onClick={(e) => {
                      e.preventDefault()
                      router.push(`/books/${project.id}/read`)
                    }}
                    className="w-full bg-blue-600 text-white py-2 rounded-md font-medium hover:bg-blue-700 transition-colors text-xs"
                  >
                    Read Now
                  </button>
                </div>
              </div>
            </div>
          </div>
        </Link>
      ))}
    </div>
  )
}

// Audio Posts Section Component
function AudioPostsSection({
  audioPosts,
  writerName,
  isOwnProfile,
  currentUserId,
  sortBy
}: {
  audioPosts: AudioPost[]
  writerName: string
  isOwnProfile: boolean
  currentUserId?: string
  sortBy: 'newest' | 'oldest' | 'popular'
}) {
  if (audioPosts.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
        <div className="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">🎵</span>
        </div>
        <h3 className="text-xl font-serif text-gray-800 mb-3">No Audio Posts Yet</h3>
        <p className="text-gray-600 font-serif">
          {writerName} hasn't published any audio posts yet. Check back soon!
        </p>
      </div>
    )
  }

  const handleAudioLove = async (postId: string) => {
    try {
      const response = await fetch(`/api/audio/posts/${postId}/love`, {
        method: 'POST'
      })
      if (!response.ok) throw new Error('Failed to toggle love')

      const { loved } = await response.json()

      // Update the audio posts state to reflect the new love count
      // This will trigger a re-render of the AudioPost component
      // The AudioPost component handles its own love count state
    } catch (error) {
      console.error('Error toggling audio love:', error)
    }
  }

  const handleAudioReply = async (postId: string) => {
    // Audio reply functionality would be handled by the AudioPost component



}

  // Sort audio posts based on sortBy parameter
  const sortedAudioPosts = [...audioPosts].sort((a, b) => {
    switch (sortBy) {
      case 'oldest':
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      case 'popular':
        return (b.love_count || 0) - (a.love_count || 0)
      case 'newest':
      default:
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    }
  })

  return (
    <div className="space-y-6">
      {sortedAudioPosts.map((post) => (
        <AudioPost
          key={post.id}
          post={post}
          currentUserId={currentUserId}
          isFollowing={false}
          onLove={handleAudioLove}
          onReply={handleAudioReply}
        />
      ))}
    </div>
  )
}

// Unified Timeline Section Component - Shows diary entries, audio posts, and books chronologically
function UnifiedTimelineSection({
  diaryEntries,
  audioPosts,
  projects,
  duos,
  hasActiveSubscription,
  writerName,
  writerId,
  isOwnProfile,
  user,
  currentUserId,
  sortBy,
  router
}: {
  diaryEntries: DiaryEntry[]
  audioPosts: AudioPost[]
  projects: Project[]
  duos: Duo[]
  hasActiveSubscription: boolean
  writerName: string
  writerId: string
  isOwnProfile: boolean
  user: any
  currentUserId?: string
  sortBy: 'newest' | 'oldest' | 'popular'
  router: any
}) {
  const [showComments, setShowComments] = useState<Record<string, boolean>>({})
  const [commentCounts, setCommentCounts] = useState<Record<string, number>>({})
  const supabase = createSupabaseClient()

  // Load comment counts for diary entries and books
  useEffect(() => {
    if (diaryEntries.length > 0) {
      loadDiaryCommentCounts(diaryEntries.map(entry => entry.id))
    }
    if (projects.length > 0) {
      loadBookCommentCounts(projects.map(project => project.id))
    }
  }, [diaryEntries, projects])

  const loadDiaryCommentCounts = async (entryIds: string[]) => {
    try {
      const { data, error } = await supabase
        .from('comments')
        .select('diary_entry_id')
        .in('diary_entry_id', entryIds)
        .eq('is_deleted', false)

      if (!error && data) {
        const counts: Record<string, number> = {}
        entryIds.forEach(id => counts[id] = 0)

        data.forEach(comment => {
          counts[comment.diary_entry_id] = (counts[comment.diary_entry_id] || 0) + 1
        })

        setCommentCounts(prev => ({ ...prev, ...counts }))
      }
    } catch (err) {
      console.error('Error loading diary comment counts:', err)
    }
  }

  const loadBookCommentCounts = async (bookIds: string[]) => {
    try {
      const { data, error } = await supabase
        .from('comments')
        .select('book_id')
        .in('book_id', bookIds)
        .eq('is_deleted', false)

      if (!error && data) {
        const counts: Record<string, number> = {}
        bookIds.forEach(id => counts[id] = 0)

        data.forEach(comment => {
          counts[comment.book_id] = (counts[comment.book_id] || 0) + 1
        })

        setCommentCounts(prev => ({ ...prev, ...counts }))
      }
    } catch (err) {
      console.error('Error loading book comment counts:', err)
    }
  }

  const toggleComments = (entryId: string) => {
    setShowComments(prev => ({
      ...prev,
      [entryId]: !prev[entryId]
    }))
  }
  // Combine and sort all posts based on sortBy parameter
  const allPosts = [
    ...diaryEntries.map(entry => ({
      ...entry,
      type: 'diary' as const,
      created_at: entry.created_at,
      popularity_score: (entry.view_count || 0)
    })),
    ...audioPosts.map(post => ({
      ...post,
      type: 'audio' as const,
      created_at: post.created_at,
      popularity_score: (post.love_count || 0) + (post.reply_count || 0)
    })),
    ...projects.map(project => ({
      ...project,
      type: 'book' as const,
      created_at: project.created_at,
      popularity_score: (project.total_words || 0) / 100 // Scale down word count for comparison
    })),
    ...duos.map(duo => ({
      ...duo,
      type: 'duo' as const,
      created_at: duo.created_at,
      popularity_score: (duo.reactions_count || 0) + (duo.view_count || 0)
    }))
  ].sort((a, b) => {
    switch (sortBy) {
      case 'oldest':
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      case 'popular':
        return b.popularity_score - a.popularity_score
      case 'newest':
      default:
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    }
  })

  if (allPosts.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">📋</span>
        </div>
        <h3 className="text-xl font-serif text-gray-800 mb-3">No Content Yet</h3>
        <p className="text-gray-600 font-serif">
          {writerName} hasn't published any diary entries, audio posts, or books yet. Check back soon!
        </p>
      </div>
    )
  }

  const handleAudioLove = async (postId: string) => {
    try {
      const response = await fetch(`/api/audio/posts/${postId}/love`, {
        method: 'POST'
      })
      if (!response.ok) throw new Error('Failed to toggle love')

      const { loved } = await response.json()

      // Update the audio posts state to reflect the new love count
      // This will trigger a re-render of the AudioPost component
      // The AudioPost component handles its own love count state
    } catch (error) {
      console.error('Error toggling audio love:', error)
    }
  }

  const handleAudioReply = async (postId: string) => {
    // Audio reply functionality would be handled by the AudioPost component
  }

  return (
    <div className="space-y-6">
      {allPosts.map((post) => {
        if (post.type === 'audio') {
          return (
            <AudioPost
              key={`audio-${post.id}`}
              post={post}
              currentUserId={currentUserId}
              isFollowing={false}
              onLove={handleAudioLove}
              onReply={handleAudioReply}
            />
          )
        } else if (post.type === 'book') {
          // For book projects, show them in a timeline-friendly format with brilliant mobile design
          return (
            <div key={`book-${post.id}`} className="bg-white rounded-xl border border-gray-100 hover:border-purple-200 hover:shadow-lg transition-all duration-300 overflow-hidden group">
              <Link href={`/books/${post.id}`} className="block">
                <div className="p-6">
                  {/* Top row - Badge and date */}
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-xs bg-purple-100 text-purple-700 px-3 py-1 rounded-full font-medium">📚 Book</span>
                    <span className="text-xs text-gray-500">{formatDate(post.created_at)}</span>
                  </div>

                  <div className="flex gap-6">
                    {/* Book Cover */}
                    <div className="w-20 h-28 bg-gradient-to-br from-purple-100 to-blue-100 rounded-lg flex-shrink-0 overflow-hidden shadow-sm group-hover:shadow-md transition-all duration-300">
                      {post.cover_image_url ? (
                        <Image
                          src={post.cover_image_url}
                          alt={post.title}
                          width={80}
                          height={112}
                          className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <span className="text-xl opacity-50">📖</span>
                        </div>
                      )}
                    </div>

                    {/* Content - Using full available space */}
                    <div className="flex-1 min-w-0">
                      {/* Title */}
                      <h4 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 leading-tight group-hover:text-purple-700 transition-colors duration-300">
                        {post.title}
                      </h4>

                      {/* Author */}
                      <p className="text-sm text-gray-600 mb-2">
                        by {user.name}
                      </p>

                      {/* Synopsis - Sharp, compact, more words */}
                      <p className="text-sm text-gray-700 leading-snug line-clamp-3 -mb-1">
                        {post.description || "This book is a work in progress and does not yet have a description"}
                      </p>
                    </div>
                  </div>

                  {/* Bottom section - Full width, proper spacing */}
                  <div className="mt-6 pt-4 border-t border-gray-100">
                    {/* Stats and genre row */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        {(post.sales_count && post.sales_count > 0) && (
                          <>
                            <span>{post.sales_count} sales</span>
                            <span>•</span>
                          </>
                        )}
                        <span>{post.total_words > 1000 ? `${Math.round(post.total_words/1000)}k` : post.total_words} words</span>
                      </div>
                      {post.genre && (
                        <span className="text-xs bg-gray-100 text-gray-600 px-3 py-1 rounded-full">
                          {post.genre}
                        </span>
                      )}
                    </div>

                    {/* Price and action - Full width */}
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-semibold text-purple-600">
                        {post.price_amount === 0 ? 'Free' : `$${(post.price_amount / 100).toFixed(2)}`}
                      </span>

                      <button
                        onClick={(e) => {
                          e.preventDefault()
                          const button = e.currentTarget
                          const spinner = button.querySelector('.loading-spinner')
                          const text = button.querySelector('.button-text')

                          if (spinner && text) {
                            spinner.classList.remove('hidden')
                            text.classList.add('opacity-0')
                          }

                          setTimeout(() => {
                            router.push(`/books/${post.id}`)
                          }, 100)
                        }}
                        className="bg-purple-600 text-white px-6 py-2 rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors duration-200 relative"
                      >
                        <span className="button-text transition-opacity duration-200">View Book</span>
                        <div className="loading-spinner hidden absolute inset-0 flex items-center justify-center">
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              </Link>

              {/* Share Button */}
              <div className="px-6 pb-4">
                <ShareButton
                  title={post.title}
                  writerName={user.name}
                  contentType="book"
                  contentId={post.id}
                  variant="compact"
                  url={`${typeof window !== 'undefined' ? window.location.origin : ''}/books/${post.id}`}
                />
              </div>

              {/* Book Comments Section */}
              <CompactBookCommentsSection
                bookId={post.id}
                canComment={!!currentUserId}
                userId={currentUserId}
                isOpen={showComments[post.id] || false}
                onToggle={() => toggleComments(post.id)}
                commentCount={commentCounts[post.id] || 0}
              />
            </div>
          )
        } else if (post.type === 'duo') {
          // Use the exact same DuoCard component as the Duos tab
          const duo = post as any
          return (
            <DuoCard
              key={`duo-${duo.id}`}
              duo={duo}
              currentUserId={currentUserId}
            />
          )
        } else {
          return (
            <div key={`diary-${post.id}`} className="bg-white rounded-lg border border-gray-100 p-4 hover:shadow-md transition-all duration-200 group">
              <Link href={`/d/${post.id}`} className="block">
                {/* Header row */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0">
                      {user.avatar || user.profile_picture_url ? (
                        <Image
                          src={(user.avatar || user.profile_picture_url) as string}
                          alt={user.name}
                          width={32}
                          height={32}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <span className="text-sm font-serif text-gray-500">
                          {user.name.charAt(0).toUpperCase()}
                        </span>
                      )}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{user.name}</p>
                      <p className="text-xs text-gray-500">{formatDate(post.created_at)}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded">📖</span>
                    {!post.is_free && (
                      <span className="text-xs bg-purple-100 text-purple-700 px-2 py-0.5 rounded">�</span>
                    )}
                  </div>
                </div>

                {/* Title */}
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-purple-700 transition-colors">{post.title}</h3>

                {/* Preview text */}
                <p className="text-gray-600 line-clamp-3 text-sm mb-3 leading-relaxed">
                  {post.body_md
                    .replace(/[#*`_~]/g, '')
                    .replace(/\n/g, ' ')
                    .substring(0, 160)
                    .trim()}
                  {post.body_md.length > 160 && '...'}
                </p>

                {/* Media preview */}
                {post.photos && post.photos.length > 0 && (
                  <div className="mb-3">
                    <Image
                      src={post.photos[0].url}
                      alt={post.photos[0].alt_text || post.title}
                      width={300}
                      height={200}
                      className="w-full h-32 object-cover rounded-lg"
                    />
                  </div>
                )}

                {post.videos && post.videos.length > 0 && (
                  <div className="mb-3">
                    <div className="relative h-32">
                      <VideoThumbnail
                        videoUrl={post.videos[0].r2_public_url}
                        customThumbnailUrl={post.videos[0].custom_thumbnail_url}
                        alt={post.title}
                        className="w-full h-full object-cover rounded-lg"
                        timeInSeconds={1}
                        showPlayButton={true}
                        playButtonSize="sm"
                      />
                    </div>
                  </div>
                )}

                {/* Read more indicator */}
                <div className="flex justify-end mt-2">
                  <span className="text-purple-600 font-medium text-sm">Read →</span>
                </div>
              </Link>

              {/* Reactions footer */}
              <div className="pt-3 border-t border-gray-50">
                <ReactionSystem
                  contentId={post.id}
                  contentType="diary"
                  currentUserId={currentUserId}
                  initialReactions={post.reactions || {}}
                  userReaction={post.userReaction}
                  onReactionUpdate={() => {}}
                />
              </div>

              {/* Comments Section */}
              <CompactCommentsSection
                entryId={post.id}
                canComment={(post.is_free || hasActiveSubscription || isOwnProfile) && !!currentUserId}
                userId={currentUserId}
                isOpen={showComments[post.id] || false}
                onToggle={() => toggleComments(post.id)}
                commentCount={commentCounts[post.id] || 0}
              />
            </div>
          )
        }
      })}
    </div>
  )
}

// Duos Section Component - Shows completed duos where user participated
function DuosSection({
  duos,
  writerName,
  isOwnProfile,
  currentUserId,
  sortBy
}: {
  duos: Duo[]
  writerName: string
  isOwnProfile: boolean
  currentUserId?: string
  sortBy: 'newest' | 'oldest' | 'popular'
}) {
  if (duos.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
        <div className="w-20 h-20 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">🎭</span>
        </div>
        <h3 className="text-xl font-serif text-gray-800 mb-3">No Duos Yet</h3>
        <p className="text-gray-600 font-serif">
          {isOwnProfile
            ? "You haven't participated in any duos yet. Create your first duo to get started!"
            : `${writerName} hasn't participated in any duos yet.`
          }
        </p>
        {isOwnProfile && (
          <div className="mt-6">
            <a
              href="/duo/new"
              className="inline-flex items-center px-6 py-3 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors font-medium"
            >
              🎭 Create Your First Duo
            </a>
          </div>
        )}
      </div>
    )
  }

  // Sort duos
  const sortedDuos = [...duos].sort((a, b) => {
    switch (sortBy) {
      case 'oldest':
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      case 'popular':
        const aEngagement = (a.reactions_count || 0) + (a.view_count || 0)
        const bEngagement = (b.reactions_count || 0) + (b.view_count || 0)
        return bEngagement - aEngagement
      case 'newest':
      default:
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    }
  })

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
      {sortedDuos.map((duo) => (
        <DuoCard
          key={duo.id}
          duo={duo}
          currentUserId={currentUserId}
        />
      ))}
    </div>
  )
}

// Individual Duo Card Component - Timeline Style
function DuoCard({
  duo,
  currentUserId
}: {
  duo: Duo
  currentUserId?: string
}) {
  const [isInView, setIsInView] = useState(false)
  const [shouldAutoplay, setShouldAutoplay] = useState(false)
  const videoContainerRef = useRef<HTMLDivElement>(null)
  const timeAgo = formatDistanceToNow(new Date(duo.created_at), { addSuffix: true })

  // Intersection Observer for autoplay
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting)
        if (entry.isIntersecting) {
          setTimeout(() => setShouldAutoplay(true), 300)
        } else {
          setShouldAutoplay(false)
        }
      },
      { threshold: 0.7 }
    )

    if (videoContainerRef.current) {
      observer.observe(videoContainerRef.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <div className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 hover:border-purple-200">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex -space-x-2">
              {/* Initiator Avatar */}
              <div className="relative">
                {duo.initiator.profile_picture_url ? (
                  <img
                    src={duo.initiator.profile_picture_url}
                    alt={duo.initiator.name}
                    className="w-10 h-10 rounded-full border-3 border-white bg-gray-100 shadow-sm"
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full border-3 border-white bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center text-white text-sm font-bold shadow-sm">
                    {duo.initiator.name.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>

              {/* Responder Avatar */}
              <div className="relative">
                {duo.responder.profile_picture_url ? (
                  <img
                    src={duo.responder.profile_picture_url}
                    alt={duo.responder.name}
                    className="w-10 h-10 rounded-full border-3 border-white bg-gray-100 shadow-sm"
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full border-3 border-white bg-gradient-to-br from-pink-500 to-pink-600 flex items-center justify-center text-white text-sm font-bold shadow-sm">
                    {duo.responder.name.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>
            </div>

            <div className="flex flex-col">
              <div className="flex items-center gap-1 text-sm font-medium text-gray-800">
                <span>{duo.initiator.name}</span>
                <span className="text-purple-500">×</span>
                <span>{duo.responder.name}</span>
              </div>
              <span className="text-xs text-gray-500">{timeAgo}</span>
            </div>
          </div>

          <div className="flex items-center gap-1">
            <span className="text-xs bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 px-2 py-0.5 rounded-full font-medium">
              🎭 Duo
            </span>
          </div>
        </div>
      </div>

      {/* Video Player */}
      <div className="relative group flex justify-center">
        <div
          ref={videoContainerRef}
          className="relative bg-black rounded-xl mb-4 overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 w-80 max-w-[90vw]"
          style={{ aspectRatio: '9/16', maxHeight: '600px' }}
        >
          {duo.partA && duo.partB ? (
            <div className="absolute inset-0">
              <DuoVideoPlayer
                partA={{
                  type: 'video',
                  src_r2_key: duo.partA.src_r2_key,
                  hls_manifest: duo.partA.hls_manifest
                }}
                partB={{
                  type: 'video',
                  src_r2_key: duo.partB.src_r2_key,
                  hls_manifest: duo.partB.hls_manifest
                }}
                signedPlayback={false}
                autoPlay={shouldAutoplay}
                showControls={true}
                initiatorName={duo.initiator.name}
                responderName={duo.responder.name}
                initiatorPhoto={duo.initiator.profile_picture_url}
                responderPhoto={duo.responder.profile_picture_url}
                finalVideoUrl={duo.final_video_url}
                finalVideoR2Key={duo.final_video_r2_key}
              />
            </div>
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-purple-900 to-pink-900">
              <div className="text-center text-white">
                <span className="text-4xl mb-2 block">🎭</span>
                <p className="text-sm opacity-80">Duo Video</p>
              </div>
            </div>
          )}

          {/* OnlyDuo Overlay */}
          <div className="absolute bottom-4 left-4 right-4 pointer-events-none z-10">
            <div className="flex items-end justify-between">
              <div className="flex flex-col gap-2">
                <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg w-fit opacity-55">
                  🎬 OnlyDuo
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Simple Engagement Stats */}
      <div className="px-4 pb-4 bg-gradient-to-r from-gray-50 to-purple-50/30">
        <div className="flex items-center justify-between pt-4">
          <div className="flex items-center gap-4 sm:gap-6">
            <div className="flex items-center gap-2 text-gray-600">
              <span className="text-lg">❤️</span>
              <span className="text-sm font-bold">{duo.reactions_count || 0}</span>
            </div>

            <div className="flex items-center gap-2 text-gray-600">
              <span className="text-lg">💬</span>
              <span className="text-sm font-bold">{duo.comments_count || 0}</span>
            </div>

            <div className="flex items-center gap-2 text-gray-600">
              <span className="text-lg">👁️</span>
              <span className="text-sm font-bold">{duo.view_count || 0}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
