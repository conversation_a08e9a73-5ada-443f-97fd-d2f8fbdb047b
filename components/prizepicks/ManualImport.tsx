'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'

// Simple icon components
const CheckCircle = () => <span className="text-green-500">✅</span>
const AlertCircle = () => <span className="text-red-500">❌</span>
const Upload = () => <span>📤</span>
const Loader2 = ({ className }: { className?: string }) => (
  <span className={`inline-block animate-spin ${className}`}>⏳</span>
)

interface ImportResult {
  success: boolean
  imported: number
  skipped: number
  errors: string[]
  props: Array<{
    player_name: string
    team?: string
    prop_type: string
    line_value: number
  }>
}

// Simple Badge component
const Badge = ({ children, variant = 'default', className = '' }: {
  children: React.ReactNode
  variant?: 'default' | 'secondary' | 'destructive' | 'outline'
  className?: string
}) => {
  const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium'
  const variantClasses = {
    default: 'bg-blue-100 text-blue-800',
    secondary: 'bg-gray-100 text-gray-800',
    destructive: 'bg-red-100 text-red-800',
    outline: 'border border-gray-300 text-gray-700'
  }

  return (
    <span className={`${baseClasses} ${variantClasses[variant]} ${className}`}>
      {children}
    </span>
  )
}

// Simple Alert component
const Alert = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <div className={`border border-orange-200 bg-orange-50 p-4 rounded-lg ${className}`}>
    {children}
  </div>
)

const AlertDescription = ({ children }: { children: React.ReactNode }) => (
  <div className="text-sm text-orange-800">{children}</div>
)

const SPORTS = [
  { value: 'NFL', label: 'NFL', emoji: '🏈' },
  { value: 'WNBA', label: 'WNBA', emoji: '🏀' },
  { value: 'NBA', label: 'NBA', emoji: '🏀' },
  { value: 'MLB', label: 'MLB', emoji: '⚾' }
]

const EXAMPLE_DATA = {
  NFL: `Lamar Jackson
BAL - QB
Lamar Jackson
@ PIT Mon 8:15pm
275.5
Pass Yards
Less
More
Trending
2.1K`,

  WNBA: `Kelsey Mitchell
IND - G
Kelsey Mitchell
@ LVA Tue 9:30pm
20.5
Points
Less
More
Trending
1.9K
Jackie Young
LVA - G
Jackie Young
vs IND Tue 9:30pm
25.5
Pts+Rebs+Asts
Less
More
Trending
1.4K`,

  NBA: `LeBron James
LAL - F
LeBron James
vs GSW Wed 10:00pm
25.5
Points
Less
More
Trending
3.2K`,

  MLB: `Aaron Judge
NYY - OF
Aaron Judge
@ HOU Thu 8:00pm
1.5
Home Runs
Less
More
Trending
2.8K`
}

export default function ManualImport() {
  const [sport, setSport] = useState<string>('')
  const [data, setData] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<ImportResult | null>(null)
  const [autoDetect, setAutoDetect] = useState(false)

  const handleImport = async () => {
    if (!data.trim()) {
      alert('Please paste some data to import')
      return
    }

    if (!sport && !autoDetect) {
      alert('Please select a sport or enable auto-detection')
      return
    }

    setIsLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/prizepicks/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sport: sport || undefined,
          data: data.trim(),
          auto_detect: autoDetect
        })
      })

      const result = await response.json()
      setResult(result.data ? { ...result, ...result.data } : result)

      if (result.success) {
        // Clear data on success
        setData('')
      }

    } catch (error) {
      console.error('Import error:', error)
      setResult({
        success: false,
        imported: 0,
        skipped: 0,
        errors: [`Import failed: ${error}`],
        props: []
      })
    } finally {
      setIsLoading(false)
    }
  }

  const loadExample = (sportKey: string) => {
    setSport(sportKey)
    setData(EXAMPLE_DATA[sportKey as keyof typeof EXAMPLE_DATA] || '')
    setResult(null)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload />
            Manual Props Import
          </CardTitle>
          <CardDescription>
            Import sports props data by pasting from PrizePicks, DraftKings, or other sources
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Sport Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Sport</label>
            <div className="flex gap-2 items-center">
              <select
                value={sport}
                onChange={(e) => setSport(e.target.value)}
                disabled={autoDetect}
                className="w-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              >
                <option value="">Select sport...</option>
                {SPORTS.map(s => (
                  <option key={s.value} value={s.value}>
                    {s.emoji} {s.label}
                  </option>
                ))}
              </select>
              <label className="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  checked={autoDetect}
                  onChange={(e) => setAutoDetect(e.target.checked)}
                  className="rounded"
                />
                Auto-detect sport
              </label>
            </div>
          </div>

          {/* Example Data Buttons */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Quick Examples</label>
            <div className="flex gap-2 flex-wrap">
              {SPORTS.map(s => (
                <Button
                  key={s.value}
                  variant="outline"
                  size="sm"
                  onClick={() => loadExample(s.value)}
                  className="text-xs"
                >
                  {s.emoji} {s.label} Example
                </Button>
              ))}
            </div>
          </div>

          {/* Data Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Paste Props Data</label>
            <Textarea
              value={data}
              onChange={(e) => setData(e.target.value)}
              placeholder="Paste your props data here..."
              className="min-h-[200px] font-mono text-sm"
            />
            <p className="text-xs text-muted-foreground">
              Supports data from PrizePicks, DraftKings, FanDuel, and other formats
            </p>
          </div>

          {/* Import Button */}
          <Button
            onClick={handleImport}
            disabled={isLoading || (!data.trim())}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2" />
                Importing...
              </>
            ) : (
              <>
                <Upload /> Import Props
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Results */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {result.success ? <CheckCircle /> : <AlertCircle />}
              Import Results
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Summary */}
            <div className="flex gap-4">
              <Badge variant="default" className="bg-green-100 text-green-800">
                ✅ {result.imported} imported
              </Badge>
              {result.skipped > 0 && (
                <Badge variant="secondary">
                  ⏭️ {result.skipped} skipped
                </Badge>
              )}
              {result.errors.length > 0 && (
                <Badge variant="destructive">
                  ❌ {result.errors.length} errors
                </Badge>
              )}
            </div>

            {/* Imported Props */}
            {result.props.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Successfully Imported:</h4>
                <div className="grid gap-2 max-h-40 overflow-y-auto">
                  {result.props.map((prop, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-muted rounded text-sm">
                      <span className="font-medium">{prop.player_name}</span>
                      <span className="text-muted-foreground">{prop.team}</span>
                      <span>{prop.prop_type.replace(/_/g, ' ')}</span>
                      <Badge variant="outline">{prop.line_value}</Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Errors */}
            {result.errors.length > 0 && (
              <Alert>
                <AlertDescription>
                  <div className="space-y-1">
                    <p className="font-medium">Errors encountered:</p>
                    <ul className="text-sm space-y-1">
                      {result.errors.slice(0, 5).map((error, index) => (
                        <li key={index} className="text-red-600">• {error}</li>
                      ))}
                      {result.errors.length > 5 && (
                        <li className="text-muted-foreground">... and {result.errors.length - 5} more</li>
                      )}
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
