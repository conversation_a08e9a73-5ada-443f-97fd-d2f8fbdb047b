"use client"

import { useState, useEffect } from "react"
import Link from "next/link"

interface OnlyPage {
  id: string
  slug: string
  status: string
  type: string
  article?: {
    title: string
    dek: string
  }
  waitlist_count?: number
  created_at: string
}

interface OnlyPagesManagerProps {
  userId: string
}

export default function OnlyPagesManager({ userId }: OnlyPagesManagerProps) {
  const [pages, setPages] = useState<OnlyPage[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchOnlyPages()
  }, [userId])

  const fetchOnlyPages = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/onlypages/dashboard')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch OnlyPages')
      }

      setPages(data.pages || [])
    } catch (err: any) {
      setError(err.message || 'Failed to load OnlyPages')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      draft: { color: 'bg-gray-100 text-gray-800', text: 'Draft' },
      published: { color: 'bg-green-100 text-green-800', text: 'Published' },
      unlisted: { color: 'bg-blue-100 text-blue-800', text: 'Unlisted' }
    }
    
    const statusInfo = statusMap[status] || { color: 'bg-gray-100 text-gray-800', text: status }
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusInfo.color}`}>
        {statusInfo.text}
      </span>
    )
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">OnlyPages</h3>
          <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
        </div>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse bg-gray-100 rounded-lg p-4 h-20"></div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">OnlyPages</h3>
          <button
            onClick={fetchOnlyPages}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            Retry
          </button>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">OnlyPages</h3>
        <div className="flex items-center gap-2">
          <Link
            href="/explore"
            className="text-violet-600 hover:text-violet-700 text-sm font-medium"
          >
            🔍 Explore
          </Link>
          <Link
            href="/explore/top-100"
            className="text-amber-600 hover:text-amber-700 text-sm font-medium"
          >
            🏆 Top 100
          </Link>
          <button
            onClick={fetchOnlyPages}
            className="text-gray-600 hover:text-gray-800 p-1"
            title="Refresh"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
          <Link
            href="/write/onlypages"
            className="bg-blue-600 text-white px-3 py-1.5 rounded text-sm font-medium hover:bg-blue-700 transition-colors"
          >
            Create New
          </Link>
        </div>
      </div>

      {pages.length === 0 ? (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
          <div className="text-gray-400 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h4 className="text-gray-600 font-medium mb-2">No OnlyPages yet</h4>
          <p className="text-gray-500 text-sm mb-4">
            Create your first OnlyPage to start building waitlists and engaging with your audience.
          </p>
          <Link
            href="/write/onlypages"
            className="inline-block bg-blue-600 text-white px-4 py-2 rounded text-sm font-medium hover:bg-blue-700 transition-colors"
          >
            Create Your First OnlyPage
          </Link>
        </div>
      ) : (
        <div className="space-y-3">
          {pages.map((page) => (
            <div key={page.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    {getStatusBadge(page.status)}
                    <span className="text-xs text-gray-500">{page.type}</span>
                  </div>
                  
                  <h4 className="font-medium text-gray-900 truncate mb-1">
                    {page.article?.title || 'Untitled Page'}
                  </h4>
                  
                  {page.article?.dek && (
                    <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                      {page.article.dek}
                    </p>
                  )}
                  
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <span>Created {formatDate(page.created_at)}</span>
                    {page.waitlist_count !== undefined && (
                      <span className="flex items-center gap-1">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        {page.waitlist_count} on waitlist
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2 ml-4">
                  <Link
                    href={`/p/${page.slug}`}
                    target="_blank"
                    className="text-gray-600 hover:text-gray-800 p-1"
                    title="View page"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </Link>
                  
                  <Link
                    href={`/write/onlypages/${page.id}`}
                    className="text-gray-600 hover:text-gray-800 p-1"
                    title="Edit page"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
