-- Create onlypages_waitlist table
CREATE TABLE IF NOT EXISTS onlypages_waitlist (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  page_id UUID NOT NULL REFERENCES onlypages(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure a user can only join a page's waitlist once
  UNIQUE(page_id, user_id)
);

-- Enable Row Level Security
ALTER TABLE onlypages_waitlist ENABLE ROW LEVEL SECURITY;

-- Create policies for RLS

-- Users can see their own waitlist entries
CREATE POLICY "Users can view their own waitlist entries" 
ON onlypages_waitlist 
FOR SELECT 
USING (auth.uid() = user_id);

-- Users can insert themselves into waitlists
CREATE POLICY "Users can join waitlists" 
ON onlypages_waitlist 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Users can remove themselves from waitlists
CREATE POLICY "Users can leave waitlists" 
ON onlypages_waitlist 
FOR DELETE 
USING (auth.uid() = user_id);

-- Page creators can see all waitlist entries for their pages
CREATE POLICY "Page creators can view their page waitlists" 
ON onlypages_waitlist 
FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM onlypages 
    WHERE onlypages.id = onlypages_waitlist.page_id 
    AND onlypages.user_id = auth.uid()
  )
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_onlypages_waitlist_page_id ON onlypages_waitlist(page_id);
CREATE INDEX IF NOT EXISTS idx_onlypages_waitlist_user_id ON onlypages_waitlist(user_id);
CREATE INDEX IF NOT EXISTS idx_onlypages_waitlist_joined_at ON onlypages_waitlist(joined_at);
